﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class change_permission_tables_name : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_Per_RoleInfo",
                table: "Per_RoleInfo");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Per_RelRoleUser",
                table: "Per_RelRoleUser");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Per_RelRoleFunction",
                table: "Per_RelRoleFunction");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Per_RelRoleDataPermission",
                table: "Per_RelRoleDataPermission");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Per_FunctionInfo",
                table: "Per_FunctionInfo");

            migrationBuilder.RenameTable(
                name: "Per_RoleInfo",
                newName: "per_roleinfo");

            migrationBuilder.RenameTable(
                name: "Per_RelRoleUser",
                newName: "per_relroleuser");

            migrationBuilder.RenameTable(
                name: "Per_RelRoleFunction",
                newName: "per_relrolefunction");

            migrationBuilder.RenameTable(
                name: "Per_RelRoleDataPermission",
                newName: "per_relroledatapermission");

            migrationBuilder.RenameTable(
                name: "Per_FunctionInfo",
                newName: "per_functioninfo");

            migrationBuilder.AddPrimaryKey(
                name: "PK_per_roleinfo",
                table: "per_roleinfo",
                column: "ID");

            migrationBuilder.AddPrimaryKey(
                name: "PK_per_relroleuser",
                table: "per_relroleuser",
                column: "ID");

            migrationBuilder.AddPrimaryKey(
                name: "PK_per_relrolefunction",
                table: "per_relrolefunction",
                column: "ID");

            migrationBuilder.AddPrimaryKey(
                name: "PK_per_relroledatapermission",
                table: "per_relroledatapermission",
                column: "ID");

            migrationBuilder.AddPrimaryKey(
                name: "PK_per_functioninfo",
                table: "per_functioninfo",
                column: "ID");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_per_roleinfo",
                table: "per_roleinfo");

            migrationBuilder.DropPrimaryKey(
                name: "PK_per_relroleuser",
                table: "per_relroleuser");

            migrationBuilder.DropPrimaryKey(
                name: "PK_per_relrolefunction",
                table: "per_relrolefunction");

            migrationBuilder.DropPrimaryKey(
                name: "PK_per_relroledatapermission",
                table: "per_relroledatapermission");

            migrationBuilder.DropPrimaryKey(
                name: "PK_per_functioninfo",
                table: "per_functioninfo");

            migrationBuilder.RenameTable(
                name: "per_roleinfo",
                newName: "Per_RoleInfo");

            migrationBuilder.RenameTable(
                name: "per_relroleuser",
                newName: "Per_RelRoleUser");

            migrationBuilder.RenameTable(
                name: "per_relrolefunction",
                newName: "Per_RelRoleFunction");

            migrationBuilder.RenameTable(
                name: "per_relroledatapermission",
                newName: "Per_RelRoleDataPermission");

            migrationBuilder.RenameTable(
                name: "per_functioninfo",
                newName: "Per_FunctionInfo");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Per_RoleInfo",
                table: "Per_RoleInfo",
                column: "ID");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Per_RelRoleUser",
                table: "Per_RelRoleUser",
                column: "ID");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Per_RelRoleFunction",
                table: "Per_RelRoleFunction",
                column: "ID");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Per_RelRoleDataPermission",
                table: "Per_RelRoleDataPermission",
                column: "ID");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Per_FunctionInfo",
                table: "Per_FunctionInfo",
                column: "ID");
        }
    }
}
