﻿using Hub.Mobile.Model;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Xamarin.Forms;

namespace Hub.Mobile.Interface
{
    public interface IAccountService
    {
        Task<UserInfo> GetCurrentUser();
        Task LoginAsync(UserInfo userInfo);
        Task LogoutAsync(bool showLoginDialog = true, Action loginDialogClosedCallBack = null);
        Task<bool> ValidateTokenAsync();
        Task<bool> SubmitCheckAsync(bool autoShowLoginDialog = true, bool autoShowNetworkDialog = true);
        /// <summary>
        /// 正常是不需要，但是由于设置现在是在Cme&Tower里面，无法打开Mobile基础设置模块
        /// </summary>
        /// <returns></returns>
        Task OpenSettingPage(INavigation navigation);
        Task<bool> IsLogin();
    }
}
