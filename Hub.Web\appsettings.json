{"AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "server=127.0.0.1;user id=root;password=root;port=3306;persistsecurityinfo=True;database=fdsp_hub"}, "IdentityServerBaseUrl": "https://identity.fiberhome.work", "IdentityServerApiName": "FDSP_Hub_Api", "HybridAppFolderName": "HybridApps", "OTAFolderName": "OTA", "DomainURL": "https://qmo-test.fiberhome.work", "CDNRootURL": "https://localhost:8086", "CDNProjectName": "fdsp_hub_test", "CDNPhysicalFolderPath": "D:\\FTS_Hub\\CDNPhysicalFolderPath", "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}}