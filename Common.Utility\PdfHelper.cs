﻿using DinkToPdf;
using DinkToPdf.Contracts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.ViewEngines;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Spire.Pdf;
using Spire.Pdf.AutomaticFields;
using Spire.Pdf.Graphics;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.Drawing.Printing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Utility
{
    public class PdfHelper
    {

        private static IConverter _converter;
        public PdfHelper(IConverter converter)
        {
            _converter = converter;

        }
        /// <summary>
        /// Razor视图转换成html
        /// </summary>
        /// <param name="controller">控制器</param>
        /// <param name="viewName">视图</param>
        /// <param name="model">实体</param>
        /// <param name="partial"></param>
        /// <returns></returns>
        public static async Task<string> RenderViewAsync(Controller controller, string viewName, object model, bool partial = false)
        {
            if (string.IsNullOrEmpty(viewName))
            {
                viewName = controller.ControllerContext.ActionDescriptor.ActionName;
            }

            controller.ViewData.Model = model;

            using (var writer = new StringWriter())
            {
                IViewEngine viewEngine = controller.HttpContext.RequestServices.GetService(typeof(ICompositeViewEngine)) as ICompositeViewEngine;
                ViewEngineResult viewResult = viewEngine.FindView(controller.ControllerContext, viewName, !partial);

                if (viewResult.Success == false)
                {
                    return $"A view with the name {viewName} could not be found";
                }

                ViewContext viewContext = new ViewContext(
                    controller.ControllerContext,
                    viewResult.View,
                    controller.ViewData,
                    controller.TempData,
                    writer,
                    new HtmlHelperOptions()
                );

                await viewResult.View.RenderAsync(viewContext);

                return writer.GetStringBuilder().ToString();
            }
        }


        /// <summary>
        /// 转换pdf [备注：linux场景下需将libwkhtmltox依赖包放在程序运行的当前目录
        /// 参考：https://github.com/rdvojmoc/DinkToPdf/tree/master/v0.12.4/64%20bit]
        /// </summary>
        /// <param name="htmlContent">待转换的内容(html)</param>
        /// <param name="savePath">保存路径</param>
        /// <param name="fileName">文件名</param>
        /// <returns></returns>
        public string ConvertToPdf(string htmlContent, string savePath, string fileName = "")
        {
            if (!Directory.Exists(savePath))
            {
                Directory.CreateDirectory(savePath);
            }
            fileName = string.IsNullOrEmpty(fileName) ? $"{DateTime.Now.ToString("yyyy-MM-dd-yy-MM-dd-HH-mm-ss")}.pdf" : fileName;
            string outPath = Path.Combine(savePath, fileName);

            var globalSettings = new GlobalSettings
            {
                ColorMode = DinkToPdf.ColorMode.Color,
                Orientation = Orientation.Portrait,//横向/纵向
                PaperSize = DinkToPdf.PaperKind.A4,
                Out = outPath,
                #region 拓展参数
                ImageQuality = 50,//jpeg压缩因子，默认94
                ImageDPI = 300,//pdf文档中用于图像的最大DPI。默认= 600
                //Orientation = Orientation.Landscape,//横向或纵向
                //Margins = new MarginSettings
                //{
                //    Top = 10,
                //    Left = 0,
                //    Right = 0,
                //}, 
                #endregion
                Margins = new MarginSettings(26, 0, 24, 0),
                DocumentTitle = "PDF Report",
            };

            var objectSettings = new ObjectSettings
            {
                PagesCount = true,
                HtmlContent = htmlContent,
                //Page = "",// 现有的网页生成PDF
                //WebSettings = { DefaultEncoding = "utf-8", UserStyleSheet = Path.Combine(Directory.GetCurrentDirectory(), "assets", "styles.css") },
                WebSettings = { DefaultEncoding = "utf-8" },
                HeaderSettings = { FontName = "Arial", FontSize = 9, Line = false,Left= "内部信息▲公布前" },
                FooterSettings = { FontName = "Arial", FontSize = 9, Line = false }//, Center = " [page]/[toPage]"
            };

            var pdf = new HtmlToPdfDocument()
            {

                GlobalSettings = globalSettings,
                Objects = { objectSettings }
            };
            var file = _converter.Convert(pdf);
            outPath = AddHeaderFooterWithLogo(outPath, "内部信息▲公布前", "权属：武汉烽火技术服务有限公司文件 请勿外传", Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Statics", "images", "company-pdfauth.png"));
            return outPath;
        }



        /// <summary>
        /// 给指定PDF添加商密信息
        /// </summary>
        /// <param name="pdfPath">pdf路径</param>
        /// <param name="headerTxt">页眉</param>
        /// <param name="footerTxt">页脚</param>
        /// <param name="imgPath">页眉logo</param>
        /// <returns></returns>
        public static string AddHeaderFooterWithLogo(string pdfPath, string headerTxt, string footerTxt, string imgPath)
        {

            PdfDocument existingPdf = new PdfDocument();
            existingPdf.LoadFromFile(pdfPath);

            //创建新的PDF文档
            PdfDocument newPdf = new PdfDocument();

            //设置页面大小
            newPdf.PageSettings.Size = existingPdf.Pages[0].Size;

            //将页边距设置为0 
            newPdf.PageSettings.Margins = new PdfMargins(0);

            //创建PdfMargins对象，指定期望设置的页边距
            //期望边距需小于或等于现有文档的实际边距，否则页眉页脚可能覆盖主体内容
            PdfMargins margins = new PdfMargins(90, 72, 90, 72);

            //在新建文档的顶部和底部应用页眉页脚模板 
            newPdf.Template.Top = PdfHelper.CreateHeaderTemplate(newPdf, margins, headerTxt, imgPath);
            newPdf.Template.Bottom = PdfHelper.CreateFooterTemplate(newPdf, margins, footerTxt);

            //在新建文档的左右部分应用空白模板 
            newPdf.Template.Left = new PdfPageTemplateElement(margins.Left, newPdf.PageSettings.Size.Height);
            newPdf.Template.Right = new PdfPageTemplateElement(margins.Right, newPdf.PageSettings.Size.Height);

            for (int i = 0; i < existingPdf.Pages.Count; i++)
            {
                //添加页面到新建文档，并将现有文档当做模板绘制到新页面
                newPdf.Pages.Add().Canvas.DrawTemplate(existingPdf.Pages[i].CreateTemplate(), new PointF(-margins.Left, -margins.Top));
            }

            //保存文档
            newPdf.SaveToFile(pdfPath);
            return pdfPath;
        }


        /// <summary>
        /// 创建页眉模板
        /// </summary>
        /// <param name="doc"></param>
        /// <param name="margins"></param>
        /// <param name="headerTxt"></param>
        /// <param name="imgPath"></param>
        /// <returns></returns>
        private static PdfPageTemplateElement CreateHeaderTemplate(PdfDocument doc, PdfMargins margins, string headerTxt, string imgPath)
        {
            //获取页面大小
            SizeF pageSize = doc.PageSettings.Size;

            //创建PdfPageTemplateElement对象headerSpace，即作页眉模板
            PdfPageTemplateElement headerSpace = new PdfPageTemplateElement(pageSize.Width, margins.Top);
            headerSpace.Foreground = false;

            //声明x,y两个float型变量
            float x = margins.Left;
            float y = 0;

            //绘制文字

            PdfTrueTypeFont font = new PdfTrueTypeFont("黑体", 10f, PdfFontStyle.Regular, true);
            PdfStringFormat format = new PdfStringFormat(PdfTextAlignment.Left);
            headerSpace.Graphics.DrawString(headerTxt, font, PdfBrushes.Black, x, y + 10, format);

            //在headerSpace中绘制图片
            //y = y + 1; 
            PdfImage headerImage = PdfImage.FromFile(imgPath);
            float width = headerImage.Width / 4;
            float height = headerImage.Height / 3;
            headerSpace.Graphics.DrawImage(headerImage, x, margins.Top - height - 2, width, height);

            ////在headerSpace中绘制横线
            //PdfPen pen = new PdfPen(PdfBrushes.Gray, 0.5f);
            //headerSpace.Graphics.DrawLine(pen, x, y + margins.Top - 2, pageSize.Width - x, y + margins.Top - 2);

            return headerSpace;
        }

        /// <summary>
        /// 创建页脚模板
        /// </summary>
        /// <param name="doc"></param>
        /// <param name="margins"></param>
        /// <param name="footerTxt"></param>
        /// <returns></returns>
        private static PdfPageTemplateElement CreateFooterTemplate(PdfDocument doc, PdfMargins margins, string footerTxt)
        {
            //获取页面大小
            SizeF pageSize = doc.PageSettings.Size;

            //创建PdfPageTemplateElement对象footerSpace，即页脚模板
            PdfPageTemplateElement footerSpace = new PdfPageTemplateElement(pageSize.Width, margins.Bottom);
            footerSpace.Foreground = false;

            //声明x,y两个float型变量
            float x = margins.Left;
            float y = 0;

            //在footerSpace中绘制横线
            PdfPen pen = new PdfPen(PdfBrushes.Gray, 0.5f);
            footerSpace.Graphics.DrawLine(pen, x, y, pageSize.Width - x, y);

            //在footerSpace中绘制文字
            y = y + 5; //y = y + 5;
            PdfTrueTypeFont font = new PdfTrueTypeFont("黑体", 10f, PdfFontStyle.Regular, true);

            PdfStringFormat format = new PdfStringFormat(PdfTextAlignment.Left);
            String footerText = footerTxt;
            footerSpace.Graphics.DrawString(footerText, font, PdfBrushes.Black, x, y, format);

            //在footerSpace中绘制当前页码和总页码
            PdfPageNumberField number = new PdfPageNumberField();
            PdfPageCountField count = new PdfPageCountField();
            PdfCompositeField compositeField = new PdfCompositeField(font, PdfBrushes.Black, "第{0}页/共{1}页", number, count);
            compositeField.StringFormat = new PdfStringFormat(PdfTextAlignment.Right, PdfVerticalAlignment.Top);
            SizeF size = font.MeasureString(compositeField.Text);
            compositeField.Bounds = new RectangleF(pageSize.Width - x - size.Width, y, size.Width, size.Height);
            compositeField.Draw(footerSpace.Graphics);

            return footerSpace;
        }


    }
}
