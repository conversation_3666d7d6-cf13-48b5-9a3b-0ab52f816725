<html>

<head>
    <title>webViewSample</title>
    <style type="text/css">
        button {
            margin: 5px;
        }
    </style>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <script src="hub_mobile_drive.js?ver=1.0" type="text/javascript"></script>
    <script type="text/javascript">
        hub_mobile_drive.registerCustomBack(function () {
            alert("press back");
        });
        function closePage() {
            hub_mobile_drive.closeCurrentPage();
        }
        function takePicture() {
            hub_mobile_drive.plugins.camera.takePicture(function (imageData) {
                debugger;
                console.log(imageData);
                document.getElementById("image").src = "data:image/jpeg;base64," + imageData.Photo;
            }, function (errMessage) {
                console.log(errMessage);
                alert(errMessage);
            });
        }
        function takePictureNeedPosition() {
            hub_mobile_drive.plugins.camera.takePicture(function (imageData) {
                debugger;
                console.log(imageData);
                document.getElementById("image").src = "data:image/jpeg;base64," + imageData.Photo;
                document.getElementById("Latitude").value = imageData.Position.Latitude;
                document.getElementById("Longitude").value = imageData.Position.Longitude;
                document.getElementById("Altitude").value = imageData.Position.Altitude;
            }, function (errMessage) {
                console.log(errMessage);
                alert(errMessage);
            }, { NeedPosition: true });
        }
        function pickPicture() {
            hub_mobile_drive.plugins.camera.pickPicture(function (imageData) {
                debugger;

                console.log(imageData.length);
                if (imageData.length > 0) {
                    document.getElementById("image").src = "data:image/jpeg;base64," + imageData[imageData.length - 1];
                }
                //   document.getElementById("image").src="data:image/jpeg;base64," + imageData;
            }, function (errMessage) {
                console.log(errMessage);
                alert(errMessage);
            });
        }
        function getToken() {
            hub_mobile_drive.plugins.authorize.getToken(function (token) {
                console.log(token);
                alert(token);
            }, function (errMessage) {
                console.log(errMessage);
                alert(errMessage);
            });
        }
        function getCurrentUser() {
            hub_mobile_drive.plugins.authorize.getCurrentUser(function (user) {
                console.log(user);
                alert(JSON.stringify(user));
            }, function (errMessage) {
                console.log(errMessage);
                alert(errMessage);
            });
        }
        function getGPS() {
            hub_mobile_drive.plugins.position.getGPS(function (gps) {
                console.log(gps);
                alert(JSON.stringify(gps));
                document.getElementById("Latitude").value = gps.Latitude;
                document.getElementById("Longitude").value = gps.Longitude;
                document.getElementById("Altitude").value = gps.Altitude;
            }, function (errMessage) {
                console.log(errMessage);
                alert(errMessage);
            });
        }
        function saveToAlbum() {
            hub_mobile_drive.plugins.camera.saveToAlbum(function (albumPath) {
                debugger;
                console.log(albumPath);
            }, function (errMessage) {
                debugger;
                console.log(errMessage);
                alert(errMessage);
            }, { "Url": document.getElementById("downloadImg").value, "Md5": document.getElementById("downloadImgMd5").value });
        }
        function saveToLocal() {
            hub_mobile_drive.plugins.file.saveToLocal(function (filePath) {
                debugger;
                console.log(filePath);
            }, function (errMessage) {
                debugger;
                console.log(errMessage);
                alert(errMessage);
            }, { "Url": document.getElementById("downloadFile").value, "Md5": document.getElementById("downloadFileMd5").value });
        }
    </script>
</head>

<body>
    <h1>TestPlugin</h1>
    <button onclick="takePicture()">TakePicture</button>
    <button onclick="takePictureNeedPosition()">TakePictureNeedPosition</button>
    <button onclick="pickPicture()">PickPicture</button>
    <button onclick="closePage()">ClosePage</button>
    <button onclick="getToken()">GetToken</button>
    <button onclick="getCurrentUser()">GetCurrentUser</button>
    <button onclick="getGPS()">GetGPS</button>
    <br />
    请输入图片下载地址<input id="downloadImg" />
    <br />
    请输入图片md5值<input id="downloadImgMd5" />
    <br />
    <button onclick="saveToAlbum()">保存到手机相册</button>
    <br />
    请输入文件下载地址<input id="downloadFile" />
    <br />
    请输入文件md5值<input id="downloadFileMd5" />
    <br />
    <button onclick="saveToLocal()">保存到本地</button>
    <image id="image" style="width: 300px; height:400px;"></image>
    <br />
    Latitude:<input id="Latitude" />
    <br />
    Longitude:<input id="Longitude" />
    <br />
    Altitude:<input id="Altitude" />
</body>

</html>