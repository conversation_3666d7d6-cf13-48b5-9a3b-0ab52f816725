﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Hub.Mobile.Views.SettingPage"
             Title="{DynamicResource Setting}">
    <ContentPage.Resources>
        <ResourceDictionary>
            <x:Double x:Key="RowHeight">50</x:Double>
            <Style TargetType="Line">
                <Setter Property="Background" Value="#F7F8FA" />
            </Style>
            <Style TargetType="Label">
                <Setter Property="TextColor" Value="Black"></Setter>
            </Style>
        </ResourceDictionary>
    </ContentPage.Resources>
    <ContentPage.Content>
        <StackLayout>
            <Grid Margin="0,10,0,10" RowSpacing="30">
                <Grid.RowDefinitions>
                    <RowDefinition></RowDefinition>
                    <RowDefinition Height="{StaticResource RowHeight}"></RowDefinition>
                </Grid.RowDefinitions>
                <Grid Grid.Row="0" BackgroundColor="White">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="{StaticResource RowHeight}"></RowDefinition>
                        <RowDefinition Height="1"></RowDefinition>
                        <RowDefinition Height="{StaticResource RowHeight}"></RowDefinition>
                        <RowDefinition Height="1"></RowDefinition>
                        <RowDefinition Height="{StaticResource RowHeight}"></RowDefinition>
                        <RowDefinition Height="1"></RowDefinition>
                        <RowDefinition Height="{StaticResource RowHeight}"></RowDefinition>
                        <RowDefinition Height="1"></RowDefinition>
                        <RowDefinition Height="{StaticResource RowHeight}"></RowDefinition>
                        <RowDefinition Height="1"></RowDefinition>
                        <RowDefinition Height="{StaticResource RowHeight}"></RowDefinition>
                        <RowDefinition Height="1"></RowDefinition>
                    </Grid.RowDefinitions>
                    <Grid Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="40"></ColumnDefinition>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                            <ColumnDefinition Width="80"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding PersonalInformationCommand}" NumberOfTapsRequired="1" />
                        </Grid.GestureRecognizers>
                        <Image  Grid.Column="0"  Margin="10,0,0,0"  Source="account_box.xml" VerticalOptions="Center"></Image>
                        <Label  Grid.Column="1" Text="{DynamicResource PersonalInformation }" VerticalOptions="Center" FontSize="Subtitle"></Label>
                        <Image  Grid.Column="2" Source="chevron_right.xml"  HorizontalOptions="End"  VerticalOptions="Center"  Margin="0,0,10,0">
                        </Image>
                    </Grid>
                    <Line Grid.Row="1"></Line>
                    <Grid Grid.Row="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="40"></ColumnDefinition>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                            <ColumnDefinition Width="80"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding ChangePasswordCommand}" NumberOfTapsRequired="1" />
                        </Grid.GestureRecognizers>
                        <Image  Grid.Column="0"  Margin="10,0,0,0"  Source="manage_account.xml" VerticalOptions="Center"></Image>
                        <Label  Grid.Column="1" Text="{DynamicResource ChangePassword }" VerticalOptions="Center" FontSize="Subtitle"></Label>
                        <Image  Grid.Column="2" Source="chevron_right.xml"  HorizontalOptions="End"  VerticalOptions="Center"  Margin="0,0,10,0">
                        </Image>
                    </Grid>
                    <Line Grid.Row="3"></Line>
                    <Grid Grid.Row="4">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="40"></ColumnDefinition>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                            <ColumnDefinition Width="80"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding SynchronizeDataCommand}" NumberOfTapsRequired="1" />
                        </Grid.GestureRecognizers>
                        <Image Grid.Column="0"  Margin="10,0,0,0"  Source="cloud_download.xml" VerticalOptions="Center"></Image>
                        <Label Grid.Column="1" Text="{DynamicResource SynchronizeBasicData}" VerticalOptions="Center" FontSize="Subtitle"></Label>
                        <Image Grid.Column="2" Source="chevron_right.xml"  HorizontalOptions="End"  VerticalOptions="Center"  Margin="0,0,10,0">
                        </Image>
                    </Grid>
                    <Line Grid.Row="5"></Line>
                    <Grid Grid.Row="6">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="40"></ColumnDefinition>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                            <ColumnDefinition Width="80"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding SwitchLanguageCommand}" NumberOfTapsRequired="1" />
                        </Grid.GestureRecognizers>
                        <Image Grid.Column="0" Margin="10,0,0,0"  Source="swap_horizontal_circle.xml" VerticalOptions="Center"></Image>
                        <Label Grid.Column="1" Text="{DynamicResource LanguageSwitch}" VerticalOptions="Center" FontSize="Subtitle"></Label>
                        <Image Grid.Column="2" Source="chevron_right.xml" HorizontalOptions="End" VerticalOptions="Center"  Margin="0,0,10,0">
                        </Image>
                    </Grid>
                    <Line Grid.Row="7"></Line>
                    <Grid Grid.Row="8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="40"></ColumnDefinition>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                            <ColumnDefinition Width="80"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding CheckUpgradeCommand}" NumberOfTapsRequired="1"></TapGestureRecognizer>
                        </Grid.GestureRecognizers>
                        <Image  Grid.Column="0" Source="arrow_circle_down.xml" Margin="10,0,0,0"  VerticalOptions="Center"></Image>
                        <Label  Grid.Column="1" Text="{DynamicResource CheckVersion}" VerticalOptions="Center" FontSize="Subtitle"></Label>
                        <Label  Grid.Column="2" TextColor="#499EF4" Text="{Binding CurrentVersion}" Margin="0,0,10,0" HorizontalOptions="End" VerticalOptions="Center"></Label>
                    </Grid>
                    <Line Grid.Row="9"></Line>
                    <Grid Grid.Row="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="40"></ColumnDefinition>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                            <ColumnDefinition Width="80"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding UploadLogCommand}" NumberOfTapsRequired="1" />
                        </Grid.GestureRecognizers>
                        <Image  Grid.Column="0"   Source="cloud_upload.xml"  Margin="10,0,0,0" VerticalOptions="Center"></Image>
                        <Label  Grid.Column="1" Text="{DynamicResource UploadLog}" VerticalOptions="Center" FontSize="Subtitle"></Label>
                        <Image  Grid.Column="2" Source="chevron_right.xml" HorizontalOptions="End" VerticalOptions="Center" Margin="0,0,10,0" >
                        </Image>
                    </Grid>
                    <Line Grid.Row="11"></Line>
                </Grid>
                <Grid Grid.Row="1" BackgroundColor="White">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="40"></ColumnDefinition>
                        <ColumnDefinition></ColumnDefinition>
                    </Grid.ColumnDefinitions>
                    <Image Grid.Row="0" Grid.Column="0" Margin="10,0,0,0" Source="logout.xml" VerticalOptions="Center"></Image>
                    <Label Grid.Row="0" Grid.Column="1" Text="{DynamicResource Login}" VerticalOptions="Center" IsVisible="{Binding IsLogin,Converter={StaticResource InverseBoolConverter}}" FontSize="Subtitle"></Label>
                    <Label Grid.Row="0" Grid.Column="1" Text="{DynamicResource LogOut}" VerticalOptions="Center" IsVisible="{Binding IsLogin}" FontSize="Subtitle"></Label>
                    <Grid.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding LogoutCommand}" NumberOfTapsRequired="1">

                        </TapGestureRecognizer>
                    </Grid.GestureRecognizers>
                </Grid>
            </Grid>
        </StackLayout>
    </ContentPage.Content>
</ContentPage>