﻿using Hub.Mobile.ViewModels;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace Hub.Mobile.Views
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class UpgradePage : PopupPage
    {
        public UpgradePage(string fileName)
        {
            InitializeComponent();
            this.BindingContext = new UpgradeViewModel(fileName) { Navigation = this.Navigation };
        }
    }
}