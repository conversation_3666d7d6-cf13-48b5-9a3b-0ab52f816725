﻿using NLog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using Common.Utility;
using Hub.Env;
using Microsoft.Extensions.Logging;

namespace Hub.Service
{
    public class OTAService
    { 
        private ILogger<OTAService> _loggger = null; 
        public OTAService(ILogger<OTAService> logger)
        {
            _loggger = logger; 
        }

        public string GetCDNDownloadUrl(string filename)
        {
            if (string.IsNullOrEmpty(filename))
            {
                filename = GetLatestAppName();
                if (string.IsNullOrEmpty(filename))
                {
                    return null;
                }
            }
            return CdnAddressHelper.GetCdnAddress( $"/{ConfigEnvValues.OTAFolderName}/{filename}");
        }

        private string GetLatestAppName()
        {  
            XElement xElement = XElement.Load(ConfigEnvValues.OTAConfigFileFullPath);
            if (xElement != null)
            {
                return xElement.Elements().Where(p => p.Name == "AppName").FirstOrDefault()?.Value;
            }
            return string.Empty;
        }
    }
}