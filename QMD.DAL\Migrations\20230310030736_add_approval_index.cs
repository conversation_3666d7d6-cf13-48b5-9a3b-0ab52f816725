﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_approval_index : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_Approvals_TaskItemID_StepID_Level_TargetType_ValueID",
                table: "Approvals",
                columns: new[] { "TaskItemID", "StepID", "Level", "TargetType", "ValueID" },
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Approvals_TaskItemID_StepID_Level_TargetType_ValueID",
                table: "Approvals");
        }
    }
}
