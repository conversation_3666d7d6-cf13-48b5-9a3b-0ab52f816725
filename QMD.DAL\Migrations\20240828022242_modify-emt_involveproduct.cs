﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class modifyemt_involveproduct : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "ActObjectCount",
                table: "emt_involveproduct",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "PlanObjectCount",
                table: "emt_involveproduct",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<DateTime>(
                name: "PlanMiddleTime",
                table: "emt_executetask",
                type: "datetime(6)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ActObjectCount",
                table: "emt_involveproduct");

            migrationBuilder.DropColumn(
                name: "PlanObjectCount",
                table: "emt_involveproduct");

            migrationBuilder.DropColumn(
                name: "PlanMiddleTime",
                table: "emt_executetask");
        }
    }
}
