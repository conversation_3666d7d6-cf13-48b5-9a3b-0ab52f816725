﻿using Hub.Mobile.ViewModels;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace Hub.Mobile.Views
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class SynchronizeBasicDataPopupPage : PopupPage
    {
        SynchronizeBasicDataViewModel viewModel;
        public SynchronizeBasicDataPopupPage()
        {
            InitializeComponent();
            BindingContext = viewModel = new SynchronizeBasicDataViewModel();
        }
    }
}