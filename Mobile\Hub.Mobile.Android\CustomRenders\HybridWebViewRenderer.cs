﻿using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Views;
using Android.Widget;
using Hub.Mobile.CommonControl;
using Hub.Mobile.Droid.CustomRenders;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Xamarin.Forms;
using Xamarin.Forms.Platform.Android;
[assembly: ExportRenderer(typeof(HybridWebView), typeof(HybridWebViewRenderer))]
namespace Hub.Mobile.Droid.CustomRenders
{
    public class HybridWebViewRenderer : WebViewRenderer
    {
        const string JavascriptExecFunction = "function mobileExec(successCallBackId, errorCallBackId, service, action, args){jsBridge.handleRequest(successCallBackId, errorCallBackId, service, action, args);}";
        const string JavascriptBackFunction = "function mobileBack(){jsBridge.mobileBack();}";
        Context _context;
        public HybridWebViewRenderer(Context context) : base(context)
        {
            _context = context;
        }
        protected override void OnElementChanged(ElementChangedEventArgs<WebView> e)
        {
            base.OnElementChanged(e);

            if (e.OldElement != null)
            {
                Control.RemoveJavascriptInterface("jsBridge");
            }
            if (e.NewElement != null)
            {
                Control.SetWebViewClient(new JavascriptWebViewClient(this, $"javascript: {JavascriptExecFunction} {JavascriptBackFunction}"));
                Control.AddJavascriptInterface(new JSBridge(this), "jsBridge");
            }
        }
    }
}