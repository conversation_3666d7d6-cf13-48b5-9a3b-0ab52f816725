﻿using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Utility
{
    /// <summary>
    /// 缩略图
    /// </summary>
    public class ImageThumbHelper
    {
        private const int DefaultWidth = 484;
        private const int DefaultHeight = 484;
        public static void Thumb(string source, string dest)
        {
            var dir = Path.GetDirectoryName(dest);
            if (!Directory.Exists(dir))
            {
                Directory.CreateDirectory(dir);
            }
            if (File.Exists(dest))
            {
                File.Delete(dest);
            }

            Image iSource = Image.FromFile(source);
            RotateImage(iSource);
            //如果为参数为0就保持原图片的高宽嘛（不然想保持原图外面还要去读取一次）

            ImageFormat tFormat = iSource.RawFormat;

            Bitmap ob = new Bitmap(DefaultWidth, DefaultHeight);
            Graphics g = Graphics.FromImage(ob);

            g.Clear(Color.WhiteSmoke);
            g.CompositingQuality = CompositingQuality.HighQuality;
            g.SmoothingMode = SmoothingMode.HighQuality;
            g.InterpolationMode = InterpolationMode.HighQualityBicubic;

            g.DrawImage(iSource, new Rectangle(0, 0, DefaultWidth, DefaultHeight), 0, 0, iSource.Width, iSource.Height, GraphicsUnit.Pixel);

            g.Dispose();
            //以下代码为保存图片时，设置压缩质量  
            EncoderParameters ep = new EncoderParameters();
            long[] qy = new long[1];
            qy[0] = 50;//设置压缩的比例1-100  
            EncoderParameter eParam = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, qy);
            ep.Param[0] = eParam;
            try
            {
                ImageCodecInfo[] arrayICI = ImageCodecInfo.GetImageEncoders();
                ImageCodecInfo jpegICIinfo = null;
                for (int x = 0; x < arrayICI.Length; x++)
                {
                    if (arrayICI[x].FormatDescription.Equals("JPEG"))
                    {
                        jpegICIinfo = arrayICI[x];
                        break;
                    }
                }
                if (jpegICIinfo != null)
                {
                    ob.Save(dest, jpegICIinfo, ep);//dFile是压缩后的新路径  
                }
                else
                {
                    ob.Save(dest, tFormat);
                }
            }
            catch
            {
            }
            finally
            {
                iSource.Dispose();
                ob.Dispose();
            }
        }

        /// <summary>
        /// 图片缩放
        /// </summary>
        /// <param name="source"></param>
        /// <param name="dest"></param>
        /// <param name="isProportion">是否等比例缩放，默认否</param>
        public static void Thumb(string source, string dest,int imgWidth,int imgHieght,bool isProportion=false)
        {
            var dir = Path.GetDirectoryName(dest);
            if (!Directory.Exists(dir))
            {
                Directory.CreateDirectory(dir);
            }
            if (File.Exists(dest))
            {
                File.Delete(dest);
            }

            Image iSource = Image.FromFile(source);
            RotateImage(iSource);
            //如果为参数为0就保持原图片的高宽嘛（不然想保持原图外面还要去读取一次）
            var percent = Math.Min((double)imgWidth / iSource.Width, (double)imgHieght / iSource.Height);
            if (isProportion)
            {
                imgWidth = Convert.ToInt32(iSource.Width * percent);
                imgHieght = Convert.ToInt32(iSource.Height * percent);
            }
            ImageFormat tFormat = iSource.RawFormat;

            Bitmap ob = new Bitmap(imgWidth, imgHieght);
            Graphics g = Graphics.FromImage(ob);

            g.Clear(Color.WhiteSmoke);
            g.CompositingQuality = CompositingQuality.HighQuality;
            g.SmoothingMode = SmoothingMode.HighQuality;
            g.InterpolationMode = InterpolationMode.HighQualityBicubic;

            g.DrawImage(iSource, new Rectangle(0, 0, imgWidth, imgHieght), 0, 0, iSource.Width, iSource.Height, GraphicsUnit.Pixel);

            g.Dispose();
            //以下代码为保存图片时，设置压缩质量  
            EncoderParameters ep = new EncoderParameters();
            long[] qy = new long[1];
            qy[0] = 50;//设置压缩的比例1-100  
            EncoderParameter eParam = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, qy);
            ep.Param[0] = eParam;
            try
            {
                ImageCodecInfo[] arrayICI = ImageCodecInfo.GetImageEncoders();
                ImageCodecInfo jpegICIinfo = null;
                for (int x = 0; x < arrayICI.Length; x++)
                {
                    if (arrayICI[x].FormatDescription.Equals("JPEG"))
                    {
                        jpegICIinfo = arrayICI[x];
                        break;
                    }
                }
                if (jpegICIinfo != null)
                {
                    ob.Save(dest, jpegICIinfo, ep);//dFile是压缩后的新路径  
                }
                else
                {
                    ob.Save(dest, tFormat);
                }
            }
            catch
            {
            }
            finally
            {
                iSource.Dispose();
                ob.Dispose();
            }
        }

        /// <summary>
        /// 旋转图片
        /// </summary>
        /// <param name="img"></param>
        public static void RotateImage(Image img)
        {
            var properties = img.PropertyItems;
            var findOrientation = properties?.Where(p => p.Id == 0x0112).FirstOrDefault();
            if(findOrientation != null)
            {
                if (2 <= findOrientation.Value.Length)
                {
                    int intOrientation =BitConverter.ToInt16(findOrientation.Value, 0);
                    switch (intOrientation)
                    {
                        case 2:
                            img.RotateFlip(RotateFlipType.RotateNoneFlipX);//horizontal flip
                            break;
                        case 3:
                            img.RotateFlip(RotateFlipType.Rotate180FlipNone);//right-top
                                                                             //img = Rotate((Bitmap)img, 180);
                            break;
                        case 4:
                            img.RotateFlip(RotateFlipType.RotateNoneFlipY);//vertical flip
                            break;
                        case 5:
                            img.RotateFlip(RotateFlipType.Rotate90FlipX);
                            break;
                        case 6:
                            img.RotateFlip(RotateFlipType.Rotate90FlipNone);//right-top
                            break;
                        case 7:
                            img.RotateFlip(RotateFlipType.Rotate270FlipX);
                            break;
                        case 8:
                            img.RotateFlip(RotateFlipType.Rotate270FlipNone);//left-bottom
                            break;
                    }
                }
              
            }
        }

      public  static string ImageToBase64(string filePath)
        {
            // 读取图片文件的字节数据
            byte[] imageBytes = File.ReadAllBytes(filePath);
            // 将字节数组转换为Base64字符串
            string base64String = Convert.ToBase64String(imageBytes);
            return base64String;
        }

        /// <summary>
        /// 是否在限制之内
        /// </summary>
        /// <param name="imgPath"></param>
        /// <param name="maxWidth">最大宽度（像素）</param>
        /// <param name="maxHeight">最大高度（像素）</param>
        /// <param name="length">文件长度（字节）</param>
        /// <returns></returns>
        public static bool IsInLimit(string imgPath, int maxWidth = 0, int maxHeight = 0, long length = 0)
        {
            bool inLimit = false;
            try
            {

                Image iSource = Image.FromFile(imgPath);
                RotateImage(iSource);
                bool inWidth = true;
                bool inHeight = true;
                bool inLength = true;
                if (maxWidth > 0 && iSource.Width > maxWidth)
                    inWidth = false;
                if (maxHeight > 0 && iSource.Height > maxHeight)
                    inHeight = false;
                iSource.Dispose();
                if (length > 0)
                {
                    var imgFile = new FileInfo(imgPath);
                    if (imgFile.Length > length)
                        inLength = false;
                }
                inLimit = inWidth && inHeight && inLength;

            }
            catch (Exception ex)
            {
                inLimit = false;
            }
            return inLimit;
        }
    }
}
