﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.DAL
{
    public interface IPrimaryKeyEntity
    {
        public string ID { get; set; }

    }

    public interface IPrimaryKeyExtEntity : IPrimaryKeyEntity
    {
        public string SearchContent { get; set; }
    }
}
