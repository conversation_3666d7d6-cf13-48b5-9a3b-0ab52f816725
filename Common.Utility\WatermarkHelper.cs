﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;

namespace Common.Utility
{
    /// <summary>
    /// 水印
    /// </summary>
    public static class WatermarkHelper
    {
        public static string AddWatermarkAndThumb(List<string> lines, string originPath, string thumbPath, string markPath)
        {
            ImageThumbHelper.Thumb(originPath, thumbPath);

            if (File.Exists(thumbPath))
            { 
                AddWatermark(lines, thumbPath, markPath);
                return markPath;
            }
            return thumbPath;
        }
        public static void AddWatermark(List<string> lines, string sourceImagePath, string savePath)
        {
            if (File.Exists(sourceImagePath))
            {
                using (Image img = Image.FromFile(sourceImagePath))
                {
                    var graphic = Graphics.FromImage(img);
                    StringBuilder sb = new StringBuilder();
                    lines.ForEach(x => { sb.AppendLine(x); });
                    var maxHeight = img.Height;
                    var maxWidth = img.Width / 5 * 3;
                    var txt = sb.ToString();
                    StringFormat format = new StringFormat();
                    format.FormatFlags = StringFormatFlags.LineLimit;
                    format.LineAlignment = StringAlignment.Near;
                    var font = new Font("宋体", 10, GraphicsUnit.Point);//微软雅黑
                    var size = graphic.MeasureString(txt, font, maxWidth, format);
                    graphic.DrawString(txt, font, new SolidBrush(Color.Black), new Rectangle(img.Width - (int)size.Width - 9, img.Height - (int)size.Height - 9, maxWidth, (int)size.Height), format: format);
                    graphic.DrawString(txt, font, new SolidBrush(Color.White), new Rectangle(img.Width - (int)size.Width - 10, img.Height - (int)size.Height - 10, maxWidth, (int)size.Height), format: format);
                    graphic.Dispose();
                    var dir = Path.GetDirectoryName(savePath);
                    if (!Directory.Exists(dir))
                    {
                        Directory.CreateDirectory(dir);
                    }
                    if (File.Exists(savePath))
                    {
                        File.Delete(savePath);
                    }
                    img.Save(savePath);
                    img.Dispose();
                }
            }
        }
    }
}
