﻿using Hub.Mobile.Interface;
using Hub.Mobile.Model;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Xamarin.Forms;

namespace Hub.Mobile.ViewModels
{
    public class PersonalInformationViewModel : BasePageViewModel
    {
        public PersonalInformationViewModel()
        {
            LoadData();
        }
        private void LoadData()
        {
            Task.Run(async () =>
            {
                IAccountService accountService = DependencyService.Get<IAccountService>();
                var currentUser=await accountService.GetCurrentUser();
                Name = currentUser?.NickName;
                Email = currentUser?.Email;
                Position = currentUser?.Position;
                Area = currentUser?.Areas;
            });
        }
        private string name;
        private string email;
        private string position;
        private string area;
        public string Name { get { return name; } set { SetProperty(ref name, value); } }
        public string Email { get { return email; } set { SetProperty(ref email, value); } }
        public string Position { get { return position; } set { SetProperty(ref position, value); } }
        public string Area { get { return area; } set { SetProperty(ref area, value); } }
    }
}
