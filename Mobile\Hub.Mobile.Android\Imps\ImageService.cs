﻿using Android.App;
using Android.Content;
using Android.Media;
using Android.OS;
using Android.Provider;
using Android.Runtime;
using Android.Util;
using Android.Views;
using Android.Widget;
using Hub.Mobile.Const;
using Hub.Mobile.Droid.Imps;
using Hub.Mobile.Interface;
using Hub.Mobile.Utility;
using Java.IO;
using Plugin.Media;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using Xamarin.Forms;

[assembly: Dependency(typeof(ImageService))]
namespace Hub.Mobile.Droid.Imps
{
    public class ImageService : IImageService
    {
        static NLog.Logger log = NLog.LogManager.GetCurrentClassLogger();
        public string ImageToBase64(string path)
        {
            InputStream inputStream = null;
            byte[] data = null;
            String result = null;
            try
            {
                inputStream = new FileInputStream(path);
                //创建一个字符流大小的数组。
                data = new byte[inputStream.Available()];
                //写入数组
                inputStream.Read(data);
                //用默认的编码格式进行编码
                result = Base64.EncodeToString(data, Base64Flags.NoClose);
            }
            catch (Exception e)
            {
                throw e;
            }
            finally
            {
                if (null != inputStream)
                {
                    try
                    {
                        inputStream.Close();
                    }
                    catch (Java.IO.IOException e)
                    {

                    }
                }

            }
            return result;
        }
        private string LessQ_SaveToAlbum(string path)
        {
            string albumPath = string.Empty;
            var fileName = System.IO.Path.GetFileName(path);
            var publicUri = MediaPickerActivity.GetOutputMediaFile(MainActivity.AppContext, null, fileName, true, true);
            using (System.IO.Stream input = System.IO.File.OpenRead(path))
            using (System.IO.Stream output = System.IO.File.Create(publicUri.Path))
                input.CopyTo(output);
            albumPath = publicUri.Path;
            var f = new Java.IO.File(publicUri.Path);
            try
            {
                MediaScannerConnection.ScanFile(MainActivity.AppContext, new[] { f.AbsolutePath }, null, null);
                var values = new ContentValues();
                values.Put(MediaStore.Images.Media.InterfaceConsts.Title, System.IO.Path.GetFileNameWithoutExtension(f.AbsolutePath));
                values.Put(MediaStore.Images.Media.InterfaceConsts.Description, string.Empty);
                values.Put(MediaStore.Images.Media.InterfaceConsts.DateTaken, Java.Lang.JavaSystem.CurrentTimeMillis());
                values.Put(MediaStore.Images.IImageColumns.BucketId, f.ToString().ToLowerInvariant().GetHashCode());
                values.Put(MediaStore.Images.IImageColumns.BucketDisplayName, f.Name.ToLowerInvariant());
                values.Put(MediaStore.Images.Media.InterfaceConsts.Data, f.AbsolutePath);
                var cr = MainActivity.AppContext.ContentResolver;
                cr.Insert(MediaStore.Images.Media.ExternalContentUri, values);
            }
            catch (Exception ex)
            {
                log.Error(ex);
            }
            return albumPath;
        }
        private string MoreQ_SaveToAlbum(string path)
        {
            var fileName = System.IO.Path.GetFileName(path);
            ContentValues contentValues = new ContentValues();
            contentValues.Put(MediaStore.Images.Media.InterfaceConsts.DisplayName, fileName);
            contentValues.Put(MediaStore.Images.Media.InterfaceConsts.Description, string.Empty);
            contentValues.Put(MediaStore.Images.Media.InterfaceConsts.RelativePath, Android.OS.Environment.DirectoryPictures);
            var insertUri = MainActivity.AppContext.ContentResolver.Insert(MediaStore.Images.Media.ExternalContentUri, contentValues);
            using (System.IO.Stream input = System.IO.File.OpenRead(path))
            using (System.IO.Stream output = MainActivity.AppContext.ContentResolver.OpenOutputStream(insertUri))
                input.CopyTo(output);
            return insertUri.Path;
        }
        public string SaveToAlbum(string path)
        {
            if (Build.VERSION.SdkInt < Android.OS.BuildVersionCodes.Q)
            {
                return LessQ_SaveToAlbum(path);
            }
            else
            {
                return MoreQ_SaveToAlbum(path);
            }
        }
        public Tuple<bool, string> ImageExistInAlbum(string fileName, string fileMD5)
        {
            bool exist = false;
            string retPath = string.Empty;
            if (Build.VERSION.SdkInt < Android.OS.BuildVersionCodes.Q)
            {
                string imageDir = DependencyService.Get<IPathService>().GetAlbumPath();
                retPath = System.IO.Path.Combine(imageDir, fileName);
                if (System.IO.File.Exists(retPath))
                {
                    string existMd5 = FileHelper.GetMD5HashFromFile(retPath);
                    exist = string.Compare(fileMD5, existMd5, true) == 0;
                }
            }
            else
            {
                var mediaUri = MediaStore.Images.Media.ExternalContentUri;
                var cursor = MainActivity.AppContext.ContentResolver.Query(mediaUri, new String[] { MediaStore.Images.Media.InterfaceConsts.Id },
                      MediaStore.Images.Media.InterfaceConsts.DisplayName + "= ?",
                    new String[] { fileName }, null);
                if (cursor != null)
                {
                    if (cursor.MoveToFirst())
                    {
                        var uri = ContentUris.WithAppendedId(mediaUri,
                  cursor.GetLong(cursor.GetColumnIndex(MediaStore.Images.Media.InterfaceConsts.Id)));
                        retPath = uri.Path;
                        using (System.IO.Stream input = MainActivity.AppContext.ContentResolver.OpenInputStream(uri))
                        {
                            MD5 md5 = new MD5CryptoServiceProvider();
                            byte[] retVal = md5.ComputeHash(input);
                            StringBuilder sb = new StringBuilder();
                            for (int i = 0; i < retVal.Length; i++)
                            {
                                sb.Append(retVal[i].ToString("x2"));
                            }
                            var fMD5 = sb.ToString();
                            exist = string.Compare(fileMD5, fMD5, true) == 0;
                        }
                    }
                }
            }
            return new Tuple<bool, string>(exist, retPath);
        }
    }
}