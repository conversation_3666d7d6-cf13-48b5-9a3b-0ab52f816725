﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="EPPlus" Version="6.2.8" />
    <PackageReference Include="Flurl.Http" Version="3.2.4" />
    <PackageReference Include="FreeSpire.Doc" Version="12.2.0" />
    <PackageReference Include="FreeSpire.PDF" Version="10.2.0" />
    <PackageReference Include="FreeSpire.XLS" Version="14.2.0" />
    <PackageReference Include="Haukcode.DinkToPdf" Version="1.1.2" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc" Version="2.2.0" />
    <PackageReference Include="NodaTime" Version="3.2.2" />
    <PackageReference Include="NPOI" Version="2.5.6" />
    <PackageReference Include="Npoi.Mapper" Version="4.1.0" />
    <PackageReference Include="SkiaSharp.NativeAssets.Linux.NoDependencies" Version="2.88.0" />
    <PackageReference Include="System.CodeDom" Version="4.6.0" />
    <PackageReference Include="System.Drawing.Common" Version="5.0.3" />
    <PackageReference Include="System.Drawing.Primitives" Version="4.3.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Common.DAL\Common.DAL.csproj" />
    <ProjectReference Include="..\Common.Model\Common.Model.csproj" />
  </ItemGroup>

</Project>
