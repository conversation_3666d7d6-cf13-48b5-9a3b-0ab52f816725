﻿using Hub.Mobile.ApiClient;
using Hub.Mobile.Interface;
using Hub.Mobile.Model;
using Hub.Mobile.Utility;
using NLog;
using Plugin.Permissions;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.IO.Compression;
using System.Text;
using System.Threading.Tasks;
using WebApiClient;
using Xamarin.Forms;

namespace Hub.Mobile.ViewModels
{
    public class HybridAppUpgradeViewModel : BasePageViewModel
    {
        private readonly ModuleInfo _moduleInfo;
        private readonly IUIResourceService _resourceService;
        private readonly IApp _appService;
        ILogger logger = LogManager.GetCurrentClassLogger();
        public Command CloseCommand { get; }
        private bool btnCloseEnabled = false;
        public bool BtnCloseEnabled
        {
            get
            {
                return btnCloseEnabled;
            }
            set
            {
                SetProperty(ref btnCloseEnabled, value);
            }
        }
        private double progress = 0;
        public double Progress
        {
            get { return progress; }
            set
            {
                SetProperty(ref progress, value);
            }
        }
        public ObservableCollection<Tip> Tips { get; set; } = new ObservableCollection<Tip>();
        public HybridAppUpgradeViewModel(ModuleInfo moduleInfo)
        {
            _resourceService = DependencyService.Get<IUIResourceService>();
            _appService = DependencyService.Get<IApp>();
            _moduleInfo = moduleInfo;
            CloseCommand = new Command(OnCloseClicked);
            StartUpgrade();
        }
        private async void OnCloseClicked(object obj)
        {
            await Navigation.PopPopupAsync();
        }
        private void UpdateTip(string msg, bool isError = false)
        {
            Tips.Add(new Tip() { Message = msg, Color = isError ? "#FF0000" : "#000000" });
        }
        private async Task StartUpgrade()
        {
            try
            {
                if (await PermissionHelper.CheckPermission<StoragePermission>())
                {
                    await Task.Factory.StartNew(() =>
                {
                    string downloadFile = string.Empty;
                    string tempDir = string.Empty;
                    string extractPath=string.Empty;
                    try
                    {
                        UpdateTip("Get new version ...");
                        var response = HttpApi.Resolve<IHubApiClient>().GetAppVersion(_moduleInfo.AppCode).Result;
                        if (response.Flag)
                        {
                            UpdateTip("Get new version finished");
                            var newApp = response.Data;
                            IDownload download = DependencyService.Get<IDownload>();
                            download.FileDownload -= Download;
                            download.FileDownload += Download;
                            UpdateTip("download ...");
                            downloadFile = download.DownloadFile(newApp.Url);
                            UpdateTip("download finished");
                            if (string.IsNullOrWhiteSpace(downloadFile) || !File.Exists(downloadFile))
                            {
                                string errMsg = string.IsNullOrWhiteSpace(downloadFile) ? "file is empty" : "file not exist";
                                logger.Error(errMsg);
                                UpdateTip(errMsg, true);
                                return;
                            }
                            string extractFolderName = System.Guid.NewGuid().ToString("N");
                            extractPath = Path.Combine(_appService.GetHybridAppBasePath(), extractFolderName);
                            if (!Directory.Exists(extractPath))
                            {
                                Directory.CreateDirectory(extractPath);
                            }
                            UpdateTip("unzip ...");
                            ZipFile.ExtractToDirectory(downloadFile, extractPath);
                            UpdateTip("unzip finished");
                            string phsicalPath = _appService.GetHybridAppPhysicalPath(_moduleInfo.AppCode);
                            UpdateTip("install ...");
                            if (Directory.Exists(phsicalPath))
                            {
                                string tempFolderName = System.Guid.NewGuid().ToString("N");
                                tempDir = Path.Combine(_appService.GetHybridAppBasePath(), tempFolderName);
                                Directory.Move(phsicalPath, tempDir);
                            }
                            //新包
                            Directory.Move(extractPath, phsicalPath);
                            //更新版本
                            if (string.IsNullOrEmpty(newApp.AppCode))
                            {
                                newApp.AppCode =_moduleInfo.AppCode;
                            }
                            newApp.InstallTime = DateTime.UtcNow;
                            _appService.UpdateAppVersion(newApp);
                            UpdateTip("install finished");
                        }
                        else
                        {
                            UpdateTip($"Get new version failed:{response.Message}", true);
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.Error(ex);
                        UpdateTip($"{ex?.Message}", true);
                    }
                    finally
                    {
                        if (!string.IsNullOrWhiteSpace(downloadFile) && File.Exists(downloadFile))
                        {
                            File.Delete(downloadFile);
                        }
                        if (!string.IsNullOrWhiteSpace(extractPath) && Directory.Exists(extractPath))
                        {
                            Directory.Delete(extractPath, true);
                        }
                        if (!string.IsNullOrWhiteSpace(tempDir) && Directory.Exists(tempDir))
                        {
                            Directory.Delete(tempDir,true);
                        }
                    }
                });
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UpdateTip($"{ex?.Message}", true);
            }
            BtnCloseEnabled = true;
            return;
        }
        private void Download(object sender, DownloadEventArgs e)
        {
            Progress = e.Progress;
        }
    }
}
