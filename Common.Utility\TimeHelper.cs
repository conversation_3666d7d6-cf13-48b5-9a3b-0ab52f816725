﻿using NodaTime;
using NPOI.OpenXmlFormats.Spreadsheet;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Org.BouncyCastle.Bcpg.Attr.ImageAttrib;

namespace Common.Utility
{
    public static class TimeHelper
    {
        /// <summary>
        /// 默认时间格式
        /// </summary>
        private const string DefaultDateTimeFormat = "yyyy-MM-dd HH:mm:ss";
        public static DateTime? GetDateTime(this long timeStamp)
        {
            DateTime dtStart = TimeZoneInfo.ConvertTime(new DateTime(1970, 1, 1), TimeZoneInfo.Local);
            //long lTime = long.Parse(timeStamp + "0000000");
            //java返回的是13为的时间戳
            long lTime = long.Parse(timeStamp + "0000");
            TimeSpan toNow = new TimeSpan(lTime);
            DateTime targetDt = dtStart.Add(toNow);
            return dtStart.Add(toNow);
        }


        public static List<string[]> GetBlocksTime(string startTimeStr, string endTimeStr)
        {
            int circleHour = 12;
            List<string[]> spanCouple = new List<string[]>();
            DateTime startTime = DateTime.Parse(startTimeStr);
            DateTime endTime = DateTime.Parse(endTimeStr);
            double totalHours = endTime.Subtract(startTime).TotalHours;
            if (totalHours <= circleHour)
            {
                spanCouple.Add(new string[] { startTimeStr, endTimeStr });
                return spanCouple;
            }
            do
            {
                DateTime addHours = startTime.AddHours(circleHour);
                spanCouple.Add(new string[] { startTime.ToString("yyyy-MM-dd HH:mm:ss"), addHours.ToString("yyyy-MM-dd HH:mm:ss") });
                startTime = addHours;
            } while (endTime.Subtract(startTime).TotalHours > circleHour);
            spanCouple.Add(new string[]
                { startTime.ToString("yyyy-MM-dd HH:mm:ss"), endTimeStr });
            return spanCouple;
        }

        /// <summary>
        /// 获取时间戳,isMs-是否毫秒级
        /// </summary>
        /// <returns></returns>
        public static long GetTimeStamp(bool isMs = true)
        {
            TimeSpan timteSpan = DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0);
            return Convert.ToInt64(isMs ? timteSpan.TotalMilliseconds : timteSpan.TotalSeconds);
        }

        public static DateTime StampToDatetime(this long timeStamp, bool accurateToMilliseconds = true)
        {
            if (accurateToMilliseconds)
            {
                return DateTimeOffset.FromUnixTimeMilliseconds(timeStamp).LocalDateTime;
            }
            else
            {
                return DateTimeOffset.FromUnixTimeSeconds(timeStamp).LocalDateTime;
            }
        }

        //将总秒数转换为时间
        public static DateTime GetLocalDateTime(long timeStamp)
        {
            return DateTimeOffset.FromUnixTimeSeconds(timeStamp).LocalDateTime;
        }

        /// <summary>
        /// 将总秒数转换为时间
        /// </summary>
        /// <param name="seconds"></param>
        /// <returns></returns>
        public static DateTime ConvertSecondsToDateTime(long seconds)
        {
            // Unix时间起始点
            DateTime epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            // 将秒数转换为时间间隔，并与Unix起始点相加
            TimeSpan timeSpan = TimeSpan.FromSeconds(seconds);
            // 将时间间隔添加到Unix起始点，得到最终的DateTime
            return epoch.Add(timeSpan);
        }

        /// <summary>
        /// uct时间转换为北京时间
        /// </summary>
        /// <param name="utcTime"></param>
        /// <returns></returns>
        public static DateTime? ToBeijingLocalTime(this DateTime? utcTime)
        {
            if (utcTime == null)
            {
                return null;
            }
            else
            {
                return utcTime.Value.AddHours(8);
                //var utcVal = utcTime.Value;
                //if (utcVal.Kind == DateTimeKind.Local)
                //{
                //    utcVal = utcVal.ToUniversalTime();
                //}
                //else if (utcVal.Kind == DateTimeKind.Unspecified)
                //{
                //    // 如果 Kind 是 Unspecified，需要手动设置为 Utc
                //    utcVal = DateTime.SpecifyKind(utcVal, DateTimeKind.Utc);
                //}
                //else
                //{
                //    // 已经是 Utc
                //}
                //Instant utc = Instant.FromDateTimeUtc(utcVal);
                //DateTimeZone timeZone = DateTimeZoneProviders.Tzdb["Asia/Shanghai"];
                //// 转换到纽约时区
                //ZonedDateTime localTime = utc.InZone(timeZone);
                //return localTime.ToDateTimeOffset().DateTime;
            }
        }


        ///// <summary>
        ///// 转成北京时间
        ///// </summary>
        ///// <param name="utcTime"></param>
        ///// <returns></returns>
        //public static DateTime? ToBeijingTime(this DateTime utcTime)
        //{
        //    try
        //    {
        //        return Convert.ToDateTime(utcTime).AddHours(8);
        //    }
        //    catch (Exception ex)
        //    {

        //        return null;
        //    }
        //}


        /// <summary>
        /// 转成北京时间
        /// </summary>
        /// <param name="utcTime"></param>
        /// <returns></returns>
        public static DateTime? ToBeijingTime(this DateTime? utcTime)
        {
            return ToBeijingLocalTime(utcTime);
        }

        /// <summary>
        /// 转成北京时间
        /// </summary>
        /// <param name="utcTime"></param>
        /// <returns></returns>
        public static DateTime ToBeijingTime(this DateTime utcTime)
        {
            return ToBeijingLocalTime(utcTime).Value;
        }

        /// <summary>
        /// uct时间转换为对应时区的当地时间
        /// </summary>
        /// <param name="utcTime"></param>
        /// <param name="timeZoneId">linux系统对应的timezoneid</param>
        /// <returns></returns>
        public static DateTime? ToLinuxLocalTime(this DateTime? utcSource,string timeZoneId)
        {
            try
            {
                if (utcSource == null)
                {
                    return null;
                }
                else
                {
                    var utcTime = utcSource.Value;
                    if (utcTime.Kind == DateTimeKind.Local)
                    {
                        utcTime = utcTime.ToUniversalTime();
                    }
                    else if (utcTime.Kind == DateTimeKind.Unspecified)
                    {
                        // 如果 Kind 是 Unspecified，需要手动设置为 Utc
                        utcTime = DateTime.SpecifyKind(utcTime, DateTimeKind.Utc);
                    }
                    else
                    {
                        // 已经是 Utc
                    }
                    Instant utc = Instant.FromDateTimeUtc(utcTime);
                    DateTimeZone timeZone = DateTimeZoneProviders.Tzdb[timeZoneId];
                    // 转换到纽约时区
                    ZonedDateTime localTime = utc.InZone(timeZone);
                    return localTime.ToDateTimeOffset().DateTime;

                    //var timeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
                    //return TimeZoneInfo.ConvertTimeFromUtc(utcTime.Value, timeZone);
                }
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        /// <summary>
        /// uct时间转换为对应时区的当地时间
        /// </summary>
        /// <param name="utcTime"></param>
        /// <param name="timeZoneId">linux系统对应的timezoneid</param>
        /// <returns></returns>
        public static DateTime? ToLinuxLocalTime(this DateTime utcTime, string timeZoneId)
        {
            try
            {
                //var timeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
                //return TimeZoneInfo.ConvertTimeFromUtc(utcTime, timeZone);
                if (utcTime.Kind == DateTimeKind.Local)
                {
                    utcTime = utcTime.ToUniversalTime();
                }
                else if (utcTime.Kind == DateTimeKind.Unspecified)
                {
                    // 如果 Kind 是 Unspecified，需要手动设置为 Utc
                    utcTime = DateTime.SpecifyKind(utcTime, DateTimeKind.Utc);
                }
                else
                {
                    // 已经是 Utc
                }
                Instant utc = Instant.FromDateTimeUtc(utcTime);
                DateTimeZone timeZone = DateTimeZoneProviders.Tzdb[timeZoneId];
                // 转换到纽约时区
                ZonedDateTime localTime = utc.InZone(timeZone);
                return localTime.ToDateTimeOffset().DateTime;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        /// <summary>
        /// 对应时区的当地时间转换为uct时间
        /// </summary>
        /// <param name="utcTime"></param>
        /// <param name="timeZoneId">linux系统对应的timezoneid</param>
        /// <returns></returns>
        public static DateTime? LinuxLocalToUtcTime(this DateTime? localTime, string timeZoneId)
        {
            try
            {
                if (localTime == null)
                {
                    return null;
                }
                else
                {
                    var timeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
                    return TimeZoneInfo.ConvertTimeToUtc(localTime.Value, timeZone);
                }
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        /// <summary>
        /// 根据时区偏移量转换UTC时间为本地时间
        /// </summary>
        /// <param name="utcDateTime">UTC时间</param>
        /// <param name="timeZoneOffset">时区偏移量(小时)</param>
        /// <param name="format">时间格式,默认为 yyyy-MM-dd HH:mm:ss</param>
        /// <returns>本地时间</returns>
        public static string ToLocalTimeByOffset(this DateTime? utcDateTime, double timeZoneOffset, string format = DefaultDateTimeFormat)
        {
            if (!utcDateTime.HasValue)
            {
                return string.Empty;
            }
            return utcDateTime.Value.AddHours(timeZoneOffset).ToString(format);
        }

        /// <summary>
        /// 根据时区ID转换UTC时间为本地时间
        /// </summary>
        /// <param name="utcDateTime">UTC时间</param>
        /// <param name="timeZoneId">时区ID</param>
        /// <param name="format">时间格式,默认为 yyyy-MM-dd HH:mm:ss</param>
        /// <returns>本地时间</returns>
        public static string ToLocalTimeByZoneId(this DateTime? utcDateTime, string timeZoneId, string format = DefaultDateTimeFormat)
        {
            if (!utcDateTime.HasValue || string.IsNullOrEmpty(timeZoneId))
            {
                return string.Empty;
            }

            try
            {
                var timeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
                return TimeZoneInfo.ConvertTimeFromUtc(utcDateTime.Value, timeZone).ToString(format);
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 北京时间转换为uct时间
        /// </summary>
        /// <param name="utcTime"></param>
        /// <returns></returns>
        public static DateTime? ToUtcTime(this DateTime? beijignTime)
        {
            if (beijignTime == null)
            {
                return null;
            }
            var tempTime = Convert.ToDateTime(beijignTime);
            TimeZoneInfo tz = GetBeijingTimeZone();
            return TimeZoneInfo.ConvertTime(tempTime, tz, TimeZoneInfo.Utc);
        }
        /// <summary>
        /// 北京时间转换为UTC时间
        /// </summary>
        /// <param name="utcTime"></param>
        /// <returns></returns>
        public static DateTime ToUtcTime(this DateTime beijignTime)
        {
            var tempTime = Convert.ToDateTime(beijignTime);
            TimeZoneInfo tz = GetBeijingTimeZone();
            if (tempTime.Kind == DateTimeKind.Local)
                return TimeZoneInfo.ConvertTimeToUtc(tempTime);
            else if (tempTime.Kind == DateTimeKind.Utc)
            {
                return tempTime;
            }
            else
                return TimeZoneInfo.ConvertTime(tempTime, tz, TimeZoneInfo.Utc);
        }

        /// <summary>
        /// 北京时间转换为UTC时间,并且转换为边界时间，如果isStart为true，则转成0点，否则转成24点
        /// </summary>
        /// <param name="utcTime"></param>
        /// <returns></returns>
        public static DateTime ToUtcTimeSide(this DateTime beijignTime, bool isStart = true)
        {
            beijignTime = Convert.ToDateTime(beijignTime.ToString($"yyyy-MM-dd {(isStart?"00:00:00": "23:59:59")}"));
            return beijignTime.ToUtcTime();
        }

        /// <summary>
        /// 获取两个时间中某一百分比的时间，默认返回开始时间
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="percent"></param>
        /// <returns></returns>
        public static DateTime GetPercentTime(DateTime startTime, DateTime endTime, double percent)
        {
            DateTime result = startTime;
            if (percent == 100)
            {
                result = endTime;
            }
            else if (percent != 0)
            {
                var timeSpan = endTime - startTime;
                var totalSeconds = timeSpan.TotalSeconds;
                double percentSeconds = totalSeconds * percent / 100;
                result = startTime.AddSeconds(percentSeconds);
            }
            return result;
        }

        /// <summary>
        /// windows使用 "China Standard Time"，‌linux使用"Asia/Shanghai"
        /// </summary>
        /// <returns></returns>
        public static TimeZoneInfo GetBeijingTimeZone()
        {  //windows使用 "China Standard Time"，‌linux使用"Asia/Shanghai"
            TimeZoneInfo tz = TimeZoneInfo.Local;
            try
            {
                tz = TimeZoneInfo.FindSystemTimeZoneById("Asia/Shanghai");
                if (tz == null)
                {
                    tz = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time");
                }
            }
            catch (Exception)
            {
                try
                {
                    tz = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time");
                }
                catch
                {

                }
            }
            if (tz == null)
            {
                tz = TimeZoneInfo.Local;
            }
            return tz;
        }

        /// <summary>
        /// 格式化为北京时间字符串
        /// </summary>
        /// <param name="dateTime">可空的日期时间</param>
        /// <param name="format">日期格式，默认为 "yyyy-MM-dd HH:mm:ss"</param>
        /// <returns>格式化后的北京时间字符串，如果输入为null则返回空字符串</returns>
        public static string FormatBeijingTimeString(this DateTime? dateTime, string format = DefaultDateTimeFormat)
        {
            return dateTime?.ToBeijingTime().ToString(format) ?? string.Empty;
        }
        public static string FormatTimeString(this DateTime? dateTime, string format = DefaultDateTimeFormat)
        {
            return dateTime?.ToString(format) ?? string.Empty;
        }
        public static string FormatTimeString(this DateTime dateTime, string format = DefaultDateTimeFormat)
        {
            return dateTime.ToString(format) ?? string.Empty;
        }

        /// <summary>
        /// 格式化为北京时间字符串
        /// </summary>
        /// <param name="dateTime">日期时间</param>
        /// <param name="format">日期格式，默认为 "yyyy-MM-dd HH:mm:ss"</param>
        /// <returns>格式化后的北京时间字符串</returns>
        public static string FormatBeijingTimeString(this DateTime dateTime, string format = DefaultDateTimeFormat)
        {
            return dateTime.ToBeijingTime().ToString(format) ?? string.Empty;
        }

        /// <summary>
        /// 格式化为指定时区的时间字符串
        /// </summary>
        /// <param name="dateTime">可空的日期时间</param>
        /// <param name="timeZoneId">时区ID</param>
        /// <param name="format">日期格式，默认为 "yyyy-MM-dd HH:mm:ss"</param>
        /// <returns>格式化后的指定时区时间字符串，如果输入为null则返回空字符串</returns>
        public static string FormatNetworkTimeString(this DateTime? dateTime, string timeZoneId, string format = DefaultDateTimeFormat)
        {
            return dateTime?.ToLinuxLocalTime(timeZoneId)?.ToString(format) ?? string.Empty;
        }

        /// <summary>
        /// 格式化为指定时区的时间字符串
        /// </summary>
        /// <param name="dateTime">日期时间</param>
        /// <param name="timeZoneId">时区ID</param>
        /// <param name="format">日期格式，默认为 "yyyy-MM-dd HH:mm:ss"</param>
        /// <returns>格式化后的指定时区时间字符串</returns>
        public static string FormatNetworkTimeString(this DateTime dateTime, string timeZoneId, string format = DefaultDateTimeFormat)
        {
            return dateTime.ToLinuxLocalTime(timeZoneId)?.ToString(format) ?? string.Empty;
        }
    }
}
