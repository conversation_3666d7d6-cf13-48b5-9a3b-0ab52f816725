﻿<?xml version="1.0" encoding="utf-8" ?>
<Application xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" xmlns:local="clr-namespace:Hub.Mobile"
               xmlns:converters="clr-namespace:Hub.Mobile.Converter;assembly=Hub.Mobile.Converter"
             x:Class="Hub.Mobile.App">
    <Application.Resources>
        <ResourceDictionary>
            <Color x:Key="Primary">#2196F3</Color>
            <Style TargetType="Button">
                <Setter Property="TextColor" Value="White"></Setter>
                <Setter Property="VisualStateManager.VisualStateGroups">
                    <VisualStateGroupList>
                        <VisualStateGroup x:Name="CommonStates">
                            <VisualState x:Name="Normal">
                                <VisualState.Setters>
                                    <Setter Property="BackgroundColor" Value="{StaticResource Primary}" />
                                </VisualState.Setters>
                            </VisualState>
                            <VisualState x:Name="Disabled">
                                <VisualState.Setters>
                                    <Setter Property="BackgroundColor" Value="#332196F3" />
                                </VisualState.Setters>
                            </VisualState>
                        </VisualStateGroup>
                    </VisualStateGroupList>
                </Setter>
            </Style>
            <Style TargetType="Label">
                <Setter Property="TextColor" Value="Black"></Setter>
                <Setter Property="FontSize" Value="14"></Setter>
            </Style>
            <converters:InverseBooleanConverter x:Key="InverseBoolConverter" />
            <converters:RadioIsCheckedConverter x:Key="RadioCheckedConverter"/>
            <ResourceDictionary.MergedDictionaries>
                <local:UIResourceManager></local:UIResourceManager>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>