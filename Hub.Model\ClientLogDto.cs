﻿using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hub.Model
{
    public class ClientLogDto
    {
        /// <summary>
        /// 日志zip包
        /// </summary>
        [JsonIgnore]
        public IFormFile File { get; set; }
        /// <summary>
        /// 手机厂商
        /// </summary>
        /// <returns></returns>
        public string DeviceBrand { get; set; }
        /// <summary>
        /// 手机型号
        /// </summary>
        /// <returns></returns>
        public string DeviceModel { get; set; }
        /// <summary>
        /// 手机系统版本号
        /// </summary>
        /// <returns></returns>
        public string SystemVersion { get; set; }
        /// <summary>
        /// App版本
        /// </summary>
        public string AppVersion { get; set; }
    }
}
