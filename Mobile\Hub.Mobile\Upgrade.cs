﻿using Hub.Mobile.Const;
using Hub.Mobile.Interface;
using Hub.Mobile.Resources;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using Xamarin.Forms;

namespace Hub.Mobile
{
    public class UpgradeCheckResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public bool NeedUpgrade { get; set; }
        public string FileName { get; set; }
    }
    public static class Upgrade
    {
        private static string paramLostMessage = "param {0} lost";
        private static string serverUrl = ApiAddresses.HubBaseUrl;
        private static string serverVersionPath = Path.Combine(serverUrl, "ota", "version.xml");
        static NLog.Logger log = NLog.LogManager.GetCurrentClassLogger();
        public static async Task<UpgradeCheckResult> Check()
        {
            UpgradeCheckResult result = new UpgradeCheckResult();
            await Task.Factory.StartNew(() =>
            {
                try
                {
                    if (DependencyService.Get<INetWork>().IsNetworkAvailable())
                    {
                        string serverVersion = GetXmlItemValue(serverVersionPath, "Version");
                        string fileName = GetXmlItemValue(serverVersionPath, "FileName");
                        string localVersion = DependencyService.Get<IUpgrade>().GetVersion();
                        if (!string.IsNullOrWhiteSpace(serverVersion) && !string.IsNullOrWhiteSpace(fileName) && !string.IsNullOrWhiteSpace(localVersion))
                        {
                            result.Success = true;
                            if (new Version(serverVersion).CompareTo(new Version(localVersion)) > 0)
                            {
                                result.NeedUpgrade = true;
                                result.FileName = fileName;
                            }
                            else
                            {
                                result.NeedUpgrade = false;
                            }
                        }
                        else
                        {
                            result.Success = false;
                            result.Message = string.Format(paramLostMessage, string.IsNullOrWhiteSpace(serverVersion) ? "serverversion" : (string.IsNullOrWhiteSpace(fileName) ? "filename" : "localversion"));
                        }
                    }
                    else
                    {
                        result.Success = false;
                        result.Message = UIResources.NetworkUnavailable;
                    }
                }
                catch (Exception ex)
                {
                    log.Error(ex, ex.Message);
                    result.Success = false;
                    result.Message = ExceptionResources.ExceptionLevel_Error;
                }
            });
            return result;
        }
        public static string GetCurrentVersion()
        {
            string version = string.Empty;
            try
            {
                version = DependencyService.Get<IUpgrade>().GetVersion();
            }
            catch (Exception ex)
            {
                log.Error(ex, ex.Message);
            }
            return version;
        }
        public static string GetXmlItemValue(string xmlPath, string xmlItemName)
        {
            XElement xe = XElement.Load(xmlPath);
            if (xe != null)
            {
                return xe.Elements().Where(p => p.Name == xmlItemName).FirstOrDefault()?.Value;
            }
            return string.Empty;
        }
    }
}
