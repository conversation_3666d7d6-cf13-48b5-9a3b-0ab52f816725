using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using QMD.Service.QMOV2Service.AI;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace QMD.Web.Controllers.QMOV2
{
    /// <summary>
    /// Dify AI控制器
    /// </summary>
    [Route("api/qmov2/[controller]")]
    [ApiController]
    public class DifyController : ControllerBase
    {
        private readonly ILogger<DifyController> _logger;
        private readonly DifyService _difyService;

        public DifyController(
            ILogger<DifyController> logger,
            DifyService difyService)
        {
            _logger = logger;
            _difyService = difyService;
        }

        /// <summary>
        /// 批量写入知识库
        /// </summary>
        /// <param name="orderNoList">工单号列表</param>
        /// <returns>写入结果</returns>
        [HttpPost("writekb")]
        public async Task<IActionResult> WriteToKnowledgeBase([FromBody] List<string> orderNoList)
        {
            try
            {
                _logger.LogInformation("接收到批量写入知识库请求");

                if (orderNoList == null || orderNoList.Count == 0)
                {
                    return BadRequest("请提供工单号列表");
                }

                await _difyService.WriteToKnowledgeBase(orderNoList);

                // 返回结果
                return Ok(new
                {
                    code = 0,
                    message = "写入知识库成功",
                    data = (object)null
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量写入知识库失败");
                return StatusCode(500, new
                {
                    code = 500,
                    message = $"处理失败: {ex.Message}",
                    data = (object)null
                });
            }
        }

        /// <summary>
        /// 上传文件到Dify
        /// </summary>
        /// <returns>上传结果</returns>
        [HttpPost("uploadfile")]
        public async Task<IActionResult> UploadFileToDify(IFormFile file)
        {
            try
            {
                _logger.LogInformation("接收到上传文件到Dify请求");

                if (file == null || file.Length == 0)
                {
                    return BadRequest("请上传文件");
                }

                // 验证文件类型
                string fileExtension = Path.GetExtension(file.FileName).ToLower();
                if (fileExtension != ".docx" && fileExtension != ".doc" && fileExtension != ".pdf")
            {
                    return BadRequest("只支持.docx、.doc或.pdf格式的文件");
                }

                // 保存上传的文件到临时目录
                string tempDir = Path.Combine(Path.GetTempPath(), "QMDDifyTemp");
                if (!Directory.Exists(tempDir))
                {
                    Directory.CreateDirectory(tempDir);
            }

                string tempFilePath = Path.Combine(tempDir, file.FileName);
                using (var fileStream = new FileStream(tempFilePath, FileMode.Create))
                {
                    await file.CopyToAsync(fileStream);
                }

                // 调用Dify服务上传文件
                var result = await _difyService.UploadFileToDify(tempFilePath, User.Identity.Name ?? "system");

                // 删除临时文件
                try
                {
                    System.IO.File.Delete(tempFilePath);
                }
                catch
                {
                    // 忽略删除失败的异常
                }

                // 返回结果
                return Ok(new
                {
                    code = 0,
                    message = "上传文件成功",
                    data = result
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "上传文件到Dify失败");
                return StatusCode(500, new
                {
                    code = 500,
                    message = $"处理失败: {ex.Message}",
                    data = (object)null
                });
            }
        }
    }
} 