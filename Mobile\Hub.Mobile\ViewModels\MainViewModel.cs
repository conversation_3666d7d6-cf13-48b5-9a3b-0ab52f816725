﻿using Acr.UserDialogs;
using Hub.Mobile.CommonControl;
using Hub.Mobile.Const;
using Hub.Mobile.Interface;
using Hub.Mobile.Model;
using Hub.Mobile.Resources;
using Hub.Mobile.Views;
using NLog;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Text;
using Xamarin.Forms;

namespace Hub.Mobile.ViewModels
{
    public class MainViewModel : BasePageViewModel
    {
        private ILogger logger = LogManager.GetCurrentClassLogger();
        public Command<ModuleInfo> NavigationCommand { get; }
        public Command SetCommand { get; }
        public ObservableCollection<ModuleInfo> Modules { get; set; } = new ObservableCollection<ModuleInfo>();
        const int RowColumns = 3;
        const string DefaultIcon = "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";
        IApp appService = DependencyService.Get<IApp>();
        IAccountService accountService = DependencyService.Get<IAccountService>();
        public MainViewModel()
        {
            NavigationCommand = new Command<ModuleInfo>(OnNavigation);
            SetCommand = new Command(OnSet);
        }
        public void LoadData()
        {
            if (!IsBusy)
            {
                try
                {
                    IsBusy = true;
                    Modules.Clear();
                    var apps = appService.GetAppConfig();
                    int rowNumber = 0;
                    int colNumber = 0;
                    string defaultBackgroundColor = "#FFF9F0";
                    foreach (var app in apps)
                    {
                        byte[] Base64Stream = string.IsNullOrWhiteSpace(app.IconData) ? Convert.FromBase64String(DefaultIcon) : Convert.FromBase64String(app.IconData);
                        defaultBackgroundColor = defaultBackgroundColor == "#FFF9F0" ? "#F0F7FE" : "#FFF9F0";
                        ModuleInfo moduleInfo = DependencyService.Get<IAutoMapperService>().AutoMapper.Map<AppItem, ModuleInfo>(app);
                        moduleInfo.ImageSource = ImageSource.FromStream(() => new MemoryStream(Base64Stream));
                        moduleInfo.BackgroundColor = defaultBackgroundColor;
                        moduleInfo.RowNumber = rowNumber;
                        moduleInfo.ColumnNumber = colNumber;
                        if (colNumber + 1 == RowColumns)
                        {
                            colNumber = 0;
                            rowNumber++;
                        }
                        else
                        {
                            colNumber++;
                        }
                        Modules.Add(moduleInfo);
                    }
                }
                catch (Exception ex)
                {
                    logger.Error(ex);
                }
                finally
                {
                    IsBusy = false;
                }
            }
        }
        private async void OnSet(object obj)
        {
            if (!IsBusy)
            {
                IsBusy = true;
                await Navigation.PushAsync(new SettingPage());
                IsBusy = false;
            }
        }
        async void OnNavigation(ModuleInfo moduleInfo)
        {
            if (!IsBusy)
            {
                try
                {
                    IsBusy = true;
                    string targetUrl = String.Empty;
                    if (moduleInfo.AppMode == HubAppMode.Online)
                    {
                        //在线
                        if (string.IsNullOrWhiteSpace(moduleInfo.URL))
                        {
                            await UserDialogs.Instance.AlertAsync("URL is null", okText: UIResources.Ok);
                            return;
                        }
                        if (!DependencyService.Get<INetWork>().IsNetworkAvailable())
                        {
                            await UserDialogs.Instance.AlertAsync(UIResources.NetworkUnavailable, okText: UIResources.Ok);
                            return;
                        }
                        targetUrl = moduleInfo.URL;
                    }
                    else
                    {
                        //离线hybrid
                        EnumAppVersionState enumAppVersionState = appService.GetAppVersionState(moduleInfo.AppCode.Trim());
                        switch (enumAppVersionState)
                        {
                            case EnumAppVersionState.待下载:
                                if (await UserDialogs.Instance.ConfirmAsync("you need to install this app,install now?", okText: UIResources.Ok, cancelText: UIResources.Cancel))
                                {
                                    if (!DependencyService.Get<INetWork>().IsNetworkAvailable())
                                    {
                                        await UserDialogs.Instance.AlertAsync(UIResources.NetworkUnavailable, okText: UIResources.Ok);
                                        return;
                                    }
                                    await Navigation.PushPopupAsync(new HybridAppUpgrade(moduleInfo));
                                }
                                return;
                            case EnumAppVersionState.待升级:
                                if (await UserDialogs.Instance.ConfirmAsync(UIResources.UpgradeConfirmMsg, okText: UIResources.Ok, cancelText: UIResources.Cancel))
                                {
                                    if (!DependencyService.Get<INetWork>().IsNetworkAvailable())
                                    {
                                        await UserDialogs.Instance.AlertAsync(UIResources.NetworkUnavailable, okText: UIResources.Ok);
                                        return;
                                    }
                                    await Navigation.PushPopupAsync(new HybridAppUpgrade(moduleInfo));
                                    return;
                                }
                                else
                                {
                                    targetUrl = appService.GetHybridStartUrl(moduleInfo.AppCode, moduleInfo.HomeAddress);
                                    break;
                                }
                            case EnumAppVersionState.最新:
                                targetUrl = appService.GetHybridStartUrl(moduleInfo.AppCode, moduleInfo.HomeAddress);
                                break;
                        }
                    }
                    var lan = Resources.UIResources.Culture.Name;
                    lan = String.IsNullOrWhiteSpace(lan) ? "en" : lan;
                    if (moduleInfo.AuthType == HubAppAuthType.Integrated)
                    {
                        if (!await accountService.ValidateTokenAsync())
                        {
                            //统一登录token验证不过需要强制登录
                            await Navigation.PushPopupAsync(new LoginPage());
                            return;
                        }
                        string token =(await accountService.GetCurrentUser())?.Token;
                        targetUrl = targetUrl.Trim().Trim(new char[] { '/', '\\', '?' }) + $"?token={token}";
                    }
                    //else
                    //{
                    //    targetUrl = targetUrl.Trim().Trim(new char[] { '/', '\\', '?' }) + $"?lan={lan}";
                    //}
                    if (targetUrl.ToLower().Contains("{lan}"))
                    {
                        targetUrl = targetUrl.Replace("{lan}",lan);
                    }
                    else
                    {
                        targetUrl = targetUrl.Trim().Trim(new char[] { '/', '\\', '?' }) + (targetUrl.IndexOf("?") > 0 ? "&" : "?") + $"lan={lan}";
                    }
                    await Navigation.PushAsync(new HybridPage(moduleInfo.DisplayName, targetUrl));
                }
                catch (Exception ex)
                {
                    logger.Error(ex);
                    await UserDialogs.Instance.AlertAsync(ExceptionResources.ExceptionLevel_Error, okText: UIResources.Ok);
                }
                finally
                {
                    IsBusy = false;
                }
            }
        }
    }
}
