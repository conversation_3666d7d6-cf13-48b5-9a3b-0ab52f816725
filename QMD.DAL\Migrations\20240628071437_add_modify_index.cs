﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_modify_index : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_RelProjectUser_ProjectID_UserEmail",
                table: "RelProjectUser");

            migrationBuilder.DropIndex(
                name: "IX_Approvers_UserEmail_AllowLevel_ProjectID",
                table: "Approvers");

            migrationBuilder.DropIndex(
                name: "IX_Approvals_TaskItemID_StepID_Level_TargetType_ValueID",
                table: "Approvals");

            migrationBuilder.CreateIndex(
                name: "IX_RelProjectUser_ProjectID_UserEmail",
                table: "RelProjectUser",
                columns: new[] { "ProjectID", "UserEmail" });

            migrationBuilder.CreateIndex(
                name: "IX_Approvers_UserEmail_AllowLevel_ProjectID",
                table: "Approvers",
                columns: new[] { "UserEmail", "AllowLevel", "ProjectID" });

            migrationBuilder.CreateIndex(
                name: "IX_Approvals_TaskItemID_StepID_Level_TargetType_ValueID",
                table: "Approvals",
                columns: new[] { "TaskItemID", "StepID", "Level", "TargetType", "ValueID" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_RelProjectUser_ProjectID_UserEmail",
                table: "RelProjectUser");

            migrationBuilder.DropIndex(
                name: "IX_Approvers_UserEmail_AllowLevel_ProjectID",
                table: "Approvers");

            migrationBuilder.DropIndex(
                name: "IX_Approvals_TaskItemID_StepID_Level_TargetType_ValueID",
                table: "Approvals");

            migrationBuilder.CreateIndex(
                name: "IX_RelProjectUser_ProjectID_UserEmail",
                table: "RelProjectUser",
                columns: new[] { "ProjectID", "UserEmail" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Approvers_UserEmail_AllowLevel_ProjectID",
                table: "Approvers",
                columns: new[] { "UserEmail", "AllowLevel", "ProjectID" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Approvals_TaskItemID_StepID_Level_TargetType_ValueID",
                table: "Approvals",
                columns: new[] { "TaskItemID", "StepID", "Level", "TargetType", "ValueID" },
                unique: true);
        }
    }
}
