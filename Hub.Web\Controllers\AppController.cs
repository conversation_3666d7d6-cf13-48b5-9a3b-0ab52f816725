﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Hub.Service;
using Common.DAL.Methods;
using Hub.DAL.Table;
using Hub.Model;

namespace Hub.Web.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
#if RELEASE
    [Authorize]
#endif
    public class AppController : ControllerBase
    {
        private AppService _appService;
        public AppController(AppService appService)
        {
            _appService = appService;
        }

        /// <summary>
        /// 提供给手机端获取所有小程序数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAllApps")]
        public BaseRes GetAllApps()
        {
            return HttpResult.Success(_appService.FindAll());
        }

        /// <summary>
        /// 提供给网页端插入一条新的小程序信息,Online模式使用这方法
        /// </summary> 
        /// <returns>返回的实体对象包含新增对象的ID</returns>
        [HttpPost]
        [Route("Insert")]
        public BaseRes Insert([FromBody] AppDto req)
        {
            return HttpResult.Success(_appService.Insert(req));
        }

        /// <summary>
        /// 提供给网页端更新一条小程序信息
        /// </summary> 
        /// <returns>返回的实体对象包含新增对象的ID</returns>
        [RequestFormLimits(MultipartBodyLengthLimit = 1048576 * 1024)]
        [RequestSizeLimit(1048576 * 1024)]
        [HttpPost]
        [Route("Update")]
        public BaseRes Update([FromForm] AppDto req)
        {
            return HttpResult.Success(_appService.Update(req));
        }

        /// <summary>
        /// 提供给网页端返回特定页面的小程序数据
        /// </summary> 
        [HttpGet]
        [Route("GetPageData")]
        public BaseRes GetPageData([FromQuery] PageCriteria pageCriteria)
        {
            var list = _appService.GetPageData(pageCriteria, out int totalCount);
            return HttpResult.PaginationSuccess(list, totalCount);
        }

        /// <summary>
        /// 根据ID删除某个小程序，如果是Hybrid小程序，还会删除服务器上保存的更新文件
        /// </summary> 
        [HttpGet]
        [Route("Delete")]
        public BaseRes Delete([FromQuery] string id)
        {
            _appService.Delete(id);
            return HttpResult.Success();
        }

        /// <summary>
        /// 上传一个新的混合式App，如果服务器上已经存在相同AppCode的App，则会更新一个版本号
        /// </summary>
        /// <param name="req">带有文件流的APP实体对象，版本号自动更新，并且自动根据文件名生成cdn的下载地址</param>
        /// <returns>返回的实体对象包含新增对象的ID以及CDN下载地址</returns>
        [HttpPost]
        [Route("UploadHybridApp")]
        public BaseRes UploadHybridApp([FromBody] AppFileDto req)
        {
            return HttpResult.Success(_appService.UploadHybridApp(req));
        }

        /// <summary>
        /// 获取混合式APP的新版本信息
        /// </summary>  
        [HttpGet]
        [Route("CheckLocalVersion")]
        public BaseRes GetAppDownloadInfo([FromQuery] string appCode)
        {
            return HttpResult.Success(_appService.GetAppDownloadInfo(appCode));
        }

        /// <summary>
        /// 测试用
        /// </summary>
        [HttpGet]
        [Route("NewApp")]
        public void NewApp()
        {
            _appService.Insert(new AppDto
            {
                AppCode = "测试",
                AppMode = HubAppMode.Online,
                AuthType = HubAppAuthType.Integrated,
                Order = 1
            });
        }
    }
}
