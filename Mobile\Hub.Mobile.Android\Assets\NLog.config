<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:schemaLocation="http://www.nlog-project.org/schemas/NLog.xsd NLog.xsd"
      autoReload="true"
      throwExceptions="false"
      internalLogLevel="Off"
      internalLogFile="c:\temp\nlog-internal.log">
  <targets>
    <target xsi:type="File"
            name="fileTarget"
            fileName="${specialfolder:folder=MyDocuments}/logs/${shortdate}.log.csv"
            archiveFileName="${specialfolder:folder=MyDocuments}/logs/archive.{#}.log.csv"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="7"
            concurrentWrites="true"
            keepFileOpen="false"
            encoding="UTF8">
      <layout xsi:type="CsvLayout" delimiter="Tab" quoting="Nothing" withHeader="true">
        <column name="time" layout="${longdate:universalTime=true}" />
        <column name="threadid" layout="${threadid}" />
        <column name="level" layout="${level:upperCase=true}" />
        <column name="callsite" layout="${callsite:includeSourcePath=true}" />
        <column name="message" layout="${message}" />
        <column name="stacktrace" layout="${callsite:includeSourcePath=true}" />
        <column name="exception" layout="${exception:format=ToString}" />
      </layout>
    </target>
  </targets>

  <rules>
    <logger name="*" writeTo="fileTarget" />
  </rules>
</nlog>