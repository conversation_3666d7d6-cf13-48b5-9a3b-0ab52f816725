(function () {
  function File() {}
    //option
  // {
  //     Url:"", //string  下载地址
  //     FileName:"", //string  文件名
  //     Md5:"", //string  文件md5值
  // }
  //success  返回手机文件存储的物理路径
  //error    错误提示
  File.prototype.saveToLocal = function (
    successCallback,
    errorCallback,
    option
  ) {
    if (typeof errorCallback != "function") {
      console.error("parameter [errorCallback] should be function");
      return;
    }
    if (typeof successCallback != "function") {
      console.error("parameter [successCallback] should be function");
      return;
    }
    hub_mobile_drive.exec(
      function (result) {
        successCallback(result);
      },
      function (error) {
        errorCallback(error);
      },
      "MFile",
      "SaveToLocal",
      option
    );
  };
  //添加插件
  hub_mobile_drive.addPlugin("file", new File());
})();
