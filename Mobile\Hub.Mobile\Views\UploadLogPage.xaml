﻿<?xml version="1.0" encoding="utf-8" ?>
<pages:PopupPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
              xmlns:animations="clr-namespace:Rg.Plugins.Popup.Animations;assembly=Rg.Plugins.Popup"
                                  xmlns:pages="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup"
             x:Class="Hub.Mobile.Views.UploadLogPage" CloseWhenBackgroundIsClicked="False">
    <pages:PopupPage.Animation>
        <animations:ScaleAnimation DurationIn="400"
                                   DurationOut="300"
                                   EasingIn="SinOut"
                                   EasingOut="SinIn"
                                   HasBackgroundAnimation="True"
                                   PositionIn="Center"
                                   PositionOut="Center"
                                   ScaleIn="1.2"
                                   ScaleOut="0.8" />
    </pages:PopupPage.Animation>
    <Grid VerticalOptions="Center" HorizontalOptions="Center" Margin="40,20" HeightRequest="120" WidthRequest="200">
        <Frame  BackgroundColor="White">
            <StackLayout HorizontalOptions="Center" VerticalOptions="Center">
                <Label Text="{Binding Tip}" HorizontalOptions="Center" Margin="10" FontSize="15" TextColor="Green"></Label>
            </StackLayout>
        </Frame>
        <AbsoluteLayout>
            <ImageButton Command="{Binding CloseCommand}" BackgroundColor="Transparent" IsEnabled="{Binding BtnCloseEnabled}"
          Source="icon_close.png"
          HeightRequest="30"
                  AbsoluteLayout.LayoutFlags="PositionProportional"
          WidthRequest="30" AbsoluteLayout.LayoutBounds="1, 0, -1, -1"/>
        </AbsoluteLayout>
    </Grid>
</pages:PopupPage>