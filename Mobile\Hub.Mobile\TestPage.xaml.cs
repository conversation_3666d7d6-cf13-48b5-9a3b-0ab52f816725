﻿using Acr.UserDialogs;
using Hub.Mobile.CommonControl;
using Hub.Mobile.Interface;
using Hub.Mobile.Resources;
using Hub.Mobile.Views;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace Hub.Mobile
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class TestPage : ContentPage
    {
        public TestPage()
        {
            InitializeComponent();
        }

        private async void BtnJump_Clicked(object sender, EventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(this.TxtUrl.Text))
            {
                string targetUrl = this.TxtUrl.Text;
                if (!DependencyService.Get<INetWork>().IsNetworkAvailable())
                {
                    await UserDialogs.Instance.AlertAsync(UIResources.NetworkUnavailable, okText: UIResources.Ok);
                    return;
                }
                var lan = UIResources.Culture.Name;
                lan = String.IsNullOrWhiteSpace(lan) ? "en" : lan;
                if (!await DependencyService.Get<IAccountService>().ValidateTokenAsync())
                {
                    //统一登录token验证不过需要强制登录
                    await Navigation.PushPopupAsync(new LoginPage());
                    return;
                }
                targetUrl = targetUrl.Trim().Trim(new char[] { '/', '\\', '?' }) + (targetUrl.IndexOf("?") > 0 ? "&" : "?") + $"lan={lan}";
                await Navigation.PushAsync(new HybridPage("Test", targetUrl));
            }
        }

    }
}