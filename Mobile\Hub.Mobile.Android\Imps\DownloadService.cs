﻿using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Views;
using Android.Widget;
using Hub.Mobile.Interface;
using Hub.Mobile.Model;
using Java.IO;
using Java.Net;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using Xamarin.Forms;

[assembly: Dependency(typeof(Hub.Mobile.Droid.Imps.DownloadService))]
namespace Hub.Mobile.Droid.Imps
{
    public class DownloadService : IDownload
    {
        public event EventHandler<DownloadEventArgs> FileDownload;

        private static string _defaultPath
        {
            get
            {
                return Path.Combine(System.Environment.GetFolderPath(System.Environment.SpecialFolder.LocalApplicationData), "temp");
            }
        }
        public string DownloadFile(string downloadUrl,string saveDir="",string fileName="")
        {
            string targetDir=string.IsNullOrWhiteSpace(saveDir)?_defaultPath:saveDir;
            if (!Directory.Exists(targetDir))
            {
                Directory.CreateDirectory(targetDir);
            }
            string newfileName = string.IsNullOrWhiteSpace(fileName) ? Path.GetFileName(new Uri(downloadUrl).AbsolutePath) : fileName;
            string pathToNewFile = Path.Combine(targetDir, newfileName);
            if (System.IO.File.Exists(pathToNewFile))
            {
                System.IO.File.Delete(pathToNewFile);
            }
            URL url = new URL(downloadUrl);
            HttpURLConnection conn = (HttpURLConnection)url.OpenConnection();
            conn.SetRequestProperty("User-Agent", "PacificHttpClient");
            conn.Connect();
            conn.ConnectTimeout = 10000;
            conn.ReadTimeout = 20000;
            int length = conn.ContentLength;
            if (length == 0)
            {
                throw new System.IO.FileNotFoundException($"{newfileName} not exist or empty") { };
            }
            System.IO.Stream stream = conn.InputStream;
            Java.IO.File zipFile = new Java.IO.File(pathToNewFile);
            FileOutputStream fileOS = new FileOutputStream(zipFile);
            int count = 0;
            int intBufferSize = 16384 * 8;
            byte[] byteBuffer = new byte[intBufferSize];
            int numread = 0;
            int progress = 0;
            while ((numread = stream.Read(byteBuffer, 0, intBufferSize)) > 0)
            {
                count += numread;
                progress = (int)(((float)count / length) * 100);
                if (FileDownload != null)
                {
                    FileDownload(this, new DownloadEventArgs((double)count / length));
                }
                fileOS.Write(byteBuffer, 0, numread);
            }
            fileOS.Close();
            stream.Close();
            return pathToNewFile;
        }
    }
}