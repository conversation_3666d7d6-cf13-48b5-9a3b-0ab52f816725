﻿using Common.DAL;
using Common.DAL.Methods;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Common.Repository
{
    public interface IRepository<T> : IDisposable where T : EntityBase, new()
    {
        T Find(string id);
        bool Exist(string id);
        void Active(string id);
        T Create();
        T Update(T entity, bool autoChangeModifiedInfo = true);
        List<T> Update(IList<T> entities, bool autoChangeModifiedInfo = true);
        /// <summary>
        /// 此方法不会自动更新modifieduser和modifieddate字段
        /// </summary> 
        int BulkUpdate(Expression<Func<T, bool>> filter, Expression<Func<T, T>> updates);
        T Insert(T entity);
        List<T> Insert(IList<T> entities);
        void Delete(T entity, bool indeed = false);
        void Delete(IList<string> ids, bool indeed = false);
        /// <summary>
        /// 此方法不会自动更新modifieduser和modifieddate字段
        /// </summary> 
        int BulkDelete(Expression<Func<T, bool>> filter);
        void Delete(IList<T> entities, bool indeed = false);
        void Delete(string id, bool indeed = false);
        /// <summary>
        /// 检查对象字段的业务字段合理性，如果不合理直接抛出异常
        /// </summary>
        bool CheckValidation(T entity, out string msg);
        void DeleteAll();
        List<T> FindAll();
        IQueryable<T> Query();
        bool Any(Expression<Func<T, bool>> filter);
        bool All(Expression<Func<T, bool>> filter);
        IQueryable<T> Query(Expression<Func<T, bool>> filter);
        List<T> GetPageData(Expression<Func<T, bool>> filter, PageCriteria criteriaBase, out int totalCount);
        List<T> GetPageData(PageCriteria criteriaBase, out int totalCount);
        List<T> GetPageData(IQueryable<T> query, PageCriteria criteriaBase, out int totalCount);
    }
}
