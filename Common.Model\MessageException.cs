﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace Common.Model
{
    public class MessageException : ApplicationException
    {
        public MessageException() : base() { }

        public MessageException(string message) : base(message) { }

        public MessageException(string message, Exception innerException) : base(message, innerException) { } 
    }
}
