﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_index : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_QaWorkOrders_TaskItemId_TplId_TaskStepID_TriggerDay",
                table: "QaWorkOrders",
                columns: new[] { "TaskItemId", "TplId", "TaskStepID", "TriggerDay" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CycleTaskRecords_TplID_TaskItemID_TriggerDay",
                table: "CycleTaskRecords",
                columns: new[] { "TplID", "TaskItemID", "TriggerDay" });

            migrationBuilder.CreateIndex(
                name: "IX_ContractorUserRels_ContractorID",
                table: "ContractorUserRels",
                column: "ContractorID");

            migrationBuilder.CreateIndex(
                name: "IX_ContractorTaskItemTplRels_ContractorID_TaskItemID_TplID",
                table: "ContractorTaskItemTplRels",
                columns: new[] { "ContractorID", "TaskItemID", "TplID" },
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_QaWorkOrders_TaskItemId_TplId_TaskStepID_TriggerDay",
                table: "QaWorkOrders");

            migrationBuilder.DropIndex(
                name: "IX_CycleTaskRecords_TplID_TaskItemID_TriggerDay",
                table: "CycleTaskRecords");

            migrationBuilder.DropIndex(
                name: "IX_ContractorUserRels_ContractorID",
                table: "ContractorUserRels");

            migrationBuilder.DropIndex(
                name: "IX_ContractorTaskItemTplRels_ContractorID_TaskItemID_TplID",
                table: "ContractorTaskItemTplRels");
        }
    }
}
