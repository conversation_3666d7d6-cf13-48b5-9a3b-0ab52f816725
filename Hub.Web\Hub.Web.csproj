﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <UserSecretsId>58ecc384-b031-40d0-b864-c30c60d2f160</UserSecretsId>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>1701;1702;1591;</NoWarn>
    <DocumentationFile>\Hub.Web.xml</DocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="IdentityServer4.AccessTokenValidation" Version="3.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.SpaServices.Extensions" Version="5.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="5.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="5.0.17">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="NLog.Web.AspNetCore" Version="5.0.0" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="5.0.4" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="5.6.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Common.Model\Common.Model.csproj" />
    <ProjectReference Include="..\Common.Repository\Common.Repository.csproj" />
    <ProjectReference Include="..\Common.Utility\Common.Utility.csproj" />
    <ProjectReference Include="..\Hub.DAL\Hub.DAL.csproj" />
    <ProjectReference Include="..\Hub.Env\Hub.Env.csproj" />
    <ProjectReference Include="..\Hub.Model\Hub.Model.csproj" />
    <ProjectReference Include="..\Hub.Service\Hub.Service.csproj" />
  </ItemGroup>

</Project>
