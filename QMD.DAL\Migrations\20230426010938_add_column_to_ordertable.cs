﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_column_to_ordertable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "NetAttributes",
                table: "QaWorkOrders",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "OrderSolution",
                table: "QaWorkOrders",
                type: "varchar(2000)",
                maxLength: 2000,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "NetAttributes",
                table: "ItrWorkOrders",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "OrderSolution",
                table: "ItrWorkOrders",
                type: "varchar(2000)",
                maxLength: 2000,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "NetAttributes",
                table: "QaWorkOrders");

            migrationBuilder.DropColumn(
                name: "OrderSolution",
                table: "QaWorkOrders");

            migrationBuilder.DropColumn(
                name: "NetAttributes",
                table: "ItrWorkOrders");

            migrationBuilder.DropColumn(
                name: "OrderSolution",
                table: "ItrWorkOrders");
        }
    }
}
