﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class cycle_tables : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "LastTriggerDay",
                table: "TaskItemAndTplRels");

            migrationBuilder.DropColumn(
                name: "TriggerCount",
                table: "TaskItemAndTplRels");

            migrationBuilder.AlterColumn<string>(
                name: "TriggerDay",
                table: "TaskTpls",
                type: "varchar(128)",
                maxLength: 128,
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int")
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "TriggerDay",
                table: "Approvals",
                type: "varchar(16)",
                maxLength: 16,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "CycleTaskRecords",
                columns: table => new
                {
                    ID = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    TplID = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    TaskItemID = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    TriggerDay = table.Column<string>(type: "varchar(16)", maxLength: 16, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    TotalRequiredStepCount = table.Column<int>(type: "int", nullable: false),
                    UploadedRequiredStepCount = table.Column<int>(type: "int", nullable: false),
                    TotalFileCount = table.Column<int>(type: "int", nullable: false),
                    Level1PassedFileCount = table.Column<int>(type: "int", nullable: false),
                    Level2PassedFileCount = table.Column<int>(type: "int", nullable: false),
                    Level1NotPassedFileCount = table.Column<int>(type: "int", nullable: false),
                    Level2NotPassedFileCount = table.Column<int>(type: "int", nullable: false),
                    Level1PassedStepCount = table.Column<int>(type: "int", nullable: false),
                    Level2PassedStepCount = table.Column<int>(type: "int", nullable: false),
                    Level1NotPassedStepCount = table.Column<int>(type: "int", nullable: false),
                    Level2NotPassedStepCount = table.Column<int>(type: "int", nullable: false),
                    LastUploadDateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    WaitLevel1ApproveStepCount = table.Column<int>(type: "int", nullable: false),
                    WaitLevel2ApproveStepCount = table.Column<int>(type: "int", nullable: false),
                    WaitLevel1ApproveFileCount = table.Column<int>(type: "int", nullable: false),
                    WaitLevel2ApproveFileCount = table.Column<int>(type: "int", nullable: false),
                    IsActived = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    ModifiedDateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedDateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedUser = table.Column<string>(type: "varchar(128)", maxLength: 128, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ModifiedUser = table.Column<string>(type: "varchar(128)", maxLength: 128, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CycleTaskRecords", x => x.ID);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            //migrationBuilder.CreateIndex(
            //    name: "IX_CycleTaskRecords_TplID_TaskItemID_TriggerDay",
            //    table: "CycleTaskRecords",
            //    columns: new[] { "TplID", "TaskItemID", "TriggerDay" },
            //    unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CycleTaskRecords");

            migrationBuilder.DropColumn(
                name: "TriggerDay",
                table: "Approvals");

            migrationBuilder.AlterColumn<int>(
                name: "TriggerDay",
                table: "TaskTpls",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(string),
                oldType: "varchar(128)",
                oldMaxLength: 128,
                oldNullable: true)
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "LastTriggerDay",
                table: "TaskItemAndTplRels",
                type: "varchar(16)",
                maxLength: 16,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<int>(
                name: "TriggerCount",
                table: "TaskItemAndTplRels",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }
    }
}
