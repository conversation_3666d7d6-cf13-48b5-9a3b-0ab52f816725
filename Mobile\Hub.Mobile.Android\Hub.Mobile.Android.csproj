﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{DF422CBE-ADDC-4D07-98C6-0950C6446803}</ProjectGuid>
    <ProjectTypeGuids>{EFBA0AD7-5A72-4C68-AF49-83D382785DCF};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <TemplateGuid>{c9e5eea5-ca05-42a1-839b-61506e0a37df}</TemplateGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>Hub.Mobile.Droid</RootNamespace>
    <AssemblyName>Hub.Mobile.Android</AssemblyName>
    <Deterministic>True</Deterministic>
    <AndroidApplication>True</AndroidApplication>
    <AndroidResgenFile>Resources\Resource.designer.cs</AndroidResgenFile>
    <AndroidResgenClass>Resource</AndroidResgenClass>
    <AndroidManifest>Properties\AndroidManifest.xml</AndroidManifest>
    <MonoAndroidResourcePrefix>Resources</MonoAndroidResourcePrefix>
    <MonoAndroidAssetsPrefix>Assets</MonoAndroidAssetsPrefix>
    <AndroidUseLatestPlatformSdk>false</AndroidUseLatestPlatformSdk>
    <TargetFrameworkVersion>v11.0</TargetFrameworkVersion>
    <AndroidEnableSGenConcurrent>true</AndroidEnableSGenConcurrent>
    <AndroidUseAapt2>true</AndroidUseAapt2>
    <AndroidHttpClientHandlerType>Xamarin.Android.Net.AndroidClientHandler</AndroidHttpClientHandlerType>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug</OutputPath>
    <DefineConstants>DEBUG;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AndroidLinkMode>None</AndroidLinkMode>
    <EmbedAssembliesIntoApk>false</EmbedAssembliesIntoApk>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AndroidManagedSymbols>true</AndroidManagedSymbols>
    <AndroidUseSharedRuntime>false</AndroidUseSharedRuntime>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Mono.Android" />
    <Reference Include="Mono.Android.Export" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Acr.UserDialogs">
      <Version>7.2.0.564</Version>
    </PackageReference>
    <PackageReference Include="NLog">
      <Version>5.0.0</Version>
    </PackageReference>
    <PackageReference Include="Plugin.CurrentActivity">
      <Version>2.1.0.4</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.Forms" Version="5.0.0.2401" />
    <PackageReference Include="Xamarin.Essentials" Version="1.6.1" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CustomRenders\HybridWebViewRenderer.cs" />
    <Compile Include="CustomRenders\JSBridge.cs" />
    <Compile Include="Imps\DeviceService.cs" />
    <Compile Include="Imps\DownloadService.cs" />
    <Compile Include="Imps\FileService.cs" />
    <Compile Include="Imps\ImageService.cs" />
    <Compile Include="Imps\Network.cs" />
    <Compile Include="Imps\PathService.cs" />
    <Compile Include="Imps\Upgrade.cs" />
    <Compile Include="JavascriptWebViewClient.cs" />
    <Compile Include="MainActivity.cs" />
    <Compile Include="Resources\Resource.designer.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <AndroidAsset Include="Assets\NLog.config" />
    <AndroidAsset Include="Assets\NLog.xsd">
      <SubType>Designer</SubType>
    </AndroidAsset>
    <None Include="Resources\AboutResources.txt" />
    <None Include="Assets\AboutAssets.txt" />
    <None Include="Properties\AndroidManifest.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\layout\Tabbar.xml" />
    <AndroidResource Include="Resources\layout\Toolbar.xml" />
    <AndroidResource Include="Resources\values\styles.xml" />
    <AndroidResource Include="Resources\values\colors.xml" />
    <AndroidResource Include="Resources\mipmap-anydpi-v26\icon.xml" />
    <AndroidResource Include="Resources\mipmap-anydpi-v26\icon_round.xml" />
    <AndroidResource Include="Resources\mipmap-hdpi\icon.png" />
    <AndroidResource Include="Resources\mipmap-hdpi\launcher_foreground.png" />
    <AndroidResource Include="Resources\mipmap-mdpi\icon.png" />
    <AndroidResource Include="Resources\mipmap-mdpi\launcher_foreground.png" />
    <AndroidResource Include="Resources\mipmap-xhdpi\icon.png" />
    <AndroidResource Include="Resources\mipmap-xhdpi\launcher_foreground.png" />
    <AndroidResource Include="Resources\mipmap-xxhdpi\icon.png" />
    <AndroidResource Include="Resources\mipmap-xxhdpi\launcher_foreground.png" />
    <AndroidResource Include="Resources\mipmap-xxxhdpi\icon.png" />
    <AndroidResource Include="Resources\mipmap-xxxhdpi\launcher_foreground.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\arrow_circle_down.xml" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Hub.Mobile.CommonControl\Hub.Mobile.CommonControl.csproj">
      <Project>{87174b92-32a4-4c24-ab15-4e837c8e690a}</Project>
      <Name>Hub.Mobile.CommonControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\Hub.Mobile.Const\Hub.Mobile.Const.csproj">
      <Project>{FF6959ED-B9C4-4434-A6AC-3E3529235359}</Project>
      <Name>Hub.Mobile.Const</Name>
    </ProjectReference>
    <ProjectReference Include="..\Hub.Mobile.Interface\Hub.Mobile.Interface.csproj">
      <Project>{1A712A56-16D4-4A8B-848F-6A9A702775E3}</Project>
      <Name>Hub.Mobile.Interface</Name>
    </ProjectReference>
    <ProjectReference Include="..\Hub.Mobile.Model\Hub.Mobile.Model.csproj">
      <Project>{75100C68-7E32-4E19-8959-A9EDB09BD551}</Project>
      <Name>Hub.Mobile.Model</Name>
    </ProjectReference>
    <ProjectReference Include="..\Hub.Mobile.Plugin\Hub.Mobile.Plugin.csproj">
      <Project>{F2FEDEBA-8ED0-4355-AB2D-D819D6B48620}</Project>
      <Name>Hub.Mobile.Plugin</Name>
    </ProjectReference>
    <ProjectReference Include="..\Hub.Mobile.Utility\Hub.Mobile.Utility.csproj">
      <Project>{717AA7B5-577F-429D-92ED-55ADBADABED9}</Project>
      <Name>Hub.Mobile.Utility</Name>
    </ProjectReference>
    <ProjectReference Include="..\Hub.Mobile\Hub.Mobile.csproj">
      <Project>{2a677e25-2cf9-4e38-b5fe-b3e69d4a51e8}</Project>
      <Name>Hub.Mobile</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\xml\file_paths.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\cover.jpg" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\chevron_right.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\cloud_download.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\cloud_upload.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\logout.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\manage_account.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\swap_horizontal_circle.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\account_box.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\icon_close.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\app_icon.png" />
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\Android\Xamarin.Android.CSharp.targets" />
</Project>