﻿<?xml version="1.0" encoding="utf-8" ?>
<pages:PopupPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
                   xmlns:animations="clr-namespace:Rg.Plugins.Popup.Animations;assembly=Rg.Plugins.Popup"
                      xmlns:pages="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup"    
             x:Class="Hub.Mobile.Views.SwitchLanguagePopupPage" CloseWhenBackgroundIsClicked="False">
    <pages:PopupPage.Animation>
        <animations:ScaleAnimation DurationIn="400"
                                   DurationOut="300"
                                   EasingIn="SinOut"
                                   EasingOut="SinIn"
                                   HasBackgroundAnimation="True"
                                   PositionIn="Center"
                                   PositionOut="Center"
                                   ScaleIn="1.2"
                                   ScaleOut="0.8" />
    </pages:PopupPage.Animation>
    <Grid Background="white" HorizontalOptions="Center" VerticalOptions="Center">
        <AbsoluteLayout>
            <StackLayout HorizontalOptions="Center" VerticalOptions="Center"  WidthRequest="300" Padding="10,20,10,20" Spacing="30" 
                 RadioButtonGroup.GroupName="languages" RadioButtonGroup.SelectedValue="{Binding Selection}">
                <RadioButton x:Name="indonesia" Content="Indonesia" Value="id"  TextColor="Black"  IsChecked="{Binding Selection, Converter={StaticResource RadioCheckedConverter},ConverterParameter={x:Reference indonesia},Mode=OneWay}"></RadioButton>
                <RadioButton x:Name="english" Content="English" Value="" TextColor="Black" IsChecked="{Binding Selection, Converter={StaticResource RadioCheckedConverter},ConverterParameter={x:Reference english}, Mode=OneWay}"></RadioButton>
                <RadioButton x:Name="spanish" Content="Spanish" Value="es"  TextColor="Black"  IsChecked="{Binding Selection, Converter={StaticResource RadioCheckedConverter},ConverterParameter={x:Reference spanish},Mode=OneWay}"></RadioButton>
                <RadioButton x:Name="chinese" Content="中文" Value="zh"  TextColor="Black"  IsChecked="{Binding Selection, Converter={StaticResource RadioCheckedConverter},ConverterParameter={x:Reference chinese},Mode=OneWay}"></RadioButton>
            </StackLayout>
            <ImageButton BackgroundColor="Transparent" Source="icon_close.png" HeightRequest="30" WidthRequest="30"
                     AbsoluteLayout.LayoutFlags="PositionProportional" 
                     AbsoluteLayout.LayoutBounds="1,0,-1,-1" Command="{Binding CloseCommand}"></ImageButton>
        </AbsoluteLayout>
    </Grid>
</pages:PopupPage>