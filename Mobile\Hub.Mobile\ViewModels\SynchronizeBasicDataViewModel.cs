﻿using Hub.Mobile.ApiClient;
using Hub.Mobile.Model;
using Hub.Mobile.Resources;
using Hub.Mobile.Services;
using NLog;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using WebApiClient;
using Xamarin.Forms;

namespace Hub.Mobile.ViewModels
{
    public class SynchronizeBasicDataViewModel : BasePageViewModel
    {
        ILogger logger = LogManager.GetCurrentClassLogger();
        IHubApiClient iHubApiClient;
        SynchronizeDataService synchronizeDataService;
        public SynchronizeBasicDataViewModel()
        {
            CloseCommand = new Command(OnCloseClicked);
            iHubApiClient = HttpApi.Resolve<IHubApiClient>();
            synchronizeDataService = DependencyService.Get<SynchronizeDataService>();
            SynchronizeData();
        }
        private async Task SynchronizeData()
        {
            try
            {
                DateTime? dateTime = await synchronizeDataService.GetLastSynchronizeTime();
                LastSynchronizationTime = $"{UIResources.LastSynchronizationTime} {dateTime}";
                Tip = Resources.UIResources.Downloading;
                var response = await synchronizeDataService.SynchronizeData();
                if (response.Item1)
                {
                    Tip = Resources.UIResources.Finished;
                    dateTime = await synchronizeDataService.GetLastSynchronizeTime();
                    LastSynchronizationTime = $"{UIResources.LastSynchronizationTime} {dateTime}";
                }
                else
                {
                    Tip = !string.IsNullOrWhiteSpace(response.Item2) ? response.Item2 : Resources.UIResources.DownloadFailed;
                }

            }
            catch (Exception ex)
            {
                logger.Error(ex);
                Tip = Resources.UIResources.DownloadFailed;
            }
            BtnCloseEnabled = true;

        }
        private async void OnCloseClicked(object obj)
        {
            await Navigation.PopPopupAsync();
        }

        public string tip;
        public string Tip
        {
            get { return tip; }
            set { SetProperty(ref tip, value); }
        }
        public Command CloseCommand { get; }
        private bool btnCloseEnabled;
        public bool BtnCloseEnabled
        {
            get
            {
                return btnCloseEnabled;
            }
            set
            {
                SetProperty(ref btnCloseEnabled, value);
            }
        }
        private string lastsynchronizationtime;
        public string LastSynchronizationTime { get { return lastsynchronizationtime; } set { SetProperty(ref lastsynchronizationtime, value); } }
    }
}
