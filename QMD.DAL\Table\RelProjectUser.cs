﻿using Common.DAL;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace QMD.DAL.Table
{
    [Index(nameof(ProjectID), nameof(UserEmail), IsUnique = false)]
    public class RelProjectUser : EntityBase
    {
        [MaxLength(32)]
        [Required]
        public string ProjectID { get; set; }
        [MaxLength(128)]
        [Required]
        public string UserEmail { get; set; }
        [MaxLength(128)]
        public string UserName { get; set; }
    }
}
