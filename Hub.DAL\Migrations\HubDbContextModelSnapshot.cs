﻿// <auto-generated />
using System;
using Hub.DAL;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace Hub.DAL.Migrations
{
    [DbContext(typeof(HubDbContext))]
    partial class HubDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("Relational:MaxIdentifierLength", 64)
                .HasAnnotation("ProductVersion", "5.0.17");

            modelBuilder.Entity("Hub.DAL.Table.App", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("AppCode")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int>("AppMode")
                        .HasColumnType("int");

                    b.Property<int>("AuthType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("DisplayName")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("HomeAddress")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("HybridAppVersion")
                        .HasColumnType("int");

                    b.Property<string>("IconData")
                        .HasColumnType("text");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<string>("URL")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.HasKey("ID");

                    b.ToTable("Apps");
                });
#pragma warning restore 612, 618
        }
    }
}
