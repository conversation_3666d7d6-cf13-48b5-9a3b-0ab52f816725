﻿using Acr.UserDialogs;
using Hub.Mobile.ApiClient;
using Hub.Mobile.ApiClient.Login;
using Hub.Mobile.Interface;
using Hub.Mobile.Model;
using Hub.Mobile.Resources;
using NLog;
using System;
using System.Collections.Generic;
using System.Text;
using WebApiClient;
using Xamarin.Forms;

namespace Hub.Mobile.ViewModels
{
    public class ChangePasswordViewModel : BasePageViewModel
    {
        private ILogger logger = LogManager.GetCurrentClassLogger();
        private bool hidden = true;
        public bool Hidden
        {
            get { return hidden; }
            set
            {
                SetProperty(ref hidden, value);
            }
        }
        private string visualImage = "visibility_off.xml";
        public string VisualImage
        {
            get { return visualImage; }
            set
            {
                SetProperty(ref visualImage, value);
            }
        }
        private string oldPassword;
        public string OldPassword
        {
            get { return oldPassword; }
            set
            {
                SetProperty(ref oldPassword, value);
            }
        }
        private string newPassword;
        public string NewPassword
        {
            get { return newPassword; }
            set
            {
                SetProperty(ref newPassword, value);
            }
        }
        private string confirmPassword;
        public string ConfirmPassword
        {
            get { return confirmPassword; }
            set
            {
                SetProperty(ref confirmPassword, value);
            }
        }
        public Command HiddenCommand { get; }
        public Command SubmitCommand { get; }
        public ChangePasswordViewModel()
        {
            HiddenCommand = new Command(() =>
            {
                Hidden = !Hidden;
                VisualImage = Hidden ? "visibility_off.xml" : "visibility.xml";
            });
            SubmitCommand = new Command(OnSubmittClicked);
        }
        private async void OnSubmittClicked()
        {
            if (!IsBusy)
            {
                try
                {
                    IsBusy = true;
                    if (string.IsNullOrWhiteSpace(OldPassword) || string.IsNullOrWhiteSpace(NewPassword) || string.IsNullOrWhiteSpace(ConfirmPassword))
                    {
                        await UserDialogs.Instance.AlertAsync(string.Format(UIResources.RequiredFormat, UIResources.Password), okText: UIResources.Ok);
                        return;
                    }
                    if (NewPassword.Trim() != ConfirmPassword.Trim())
                    {
                        await UserDialogs.Instance.AlertAsync(UIResources.InconsistentPassword, okText: UIResources.Ok);
                        return;
                    }
                    string token =(await DependencyService.Get<IAccountService>().GetCurrentUser())?.Token;
                    var response = await HttpApi.Resolve<ILoginApiClient>().ChangePassword(new ChangePasswordRequest() { OriginalPassword = this.OldPassword.Trim(), NewPassword = this.NewPassword.Trim(), ConfirmPassword = this.ConfirmPassword.Trim() }, $"Bearer {token}");
                    if (response.Flag)
                    {
                        await UserDialogs.Instance.AlertAsync(UIResources.Success, okText: UIResources.Ok);
                        await Navigation.PopAsync();
                    }
                    else
                    {
                        await UserDialogs.Instance.AlertAsync(response.Message, okText: UIResources.Ok);
                    }
                }
                catch (Exception ex)
                {
                    logger.Error(ex);
                    await UserDialogs.Instance.AlertAsync($"{UIResources.ChangePassword} {UIResources.Failed}", okText: UIResources.Ok);
                }
                finally
                {
                    IsBusy = false;
                }
            }
        }
    }
}
