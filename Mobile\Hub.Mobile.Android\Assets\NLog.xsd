﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="NLog" targetNamespace="http://www.nlog-project.org/schemas/NLog.xsd" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.nlog-project.org/schemas/NLog.xsd">
  <xs:element name="nlog" type="NLogConfiguration" />
  <xs:complexType name="NLogConfiguration">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="extensions" type="NLogExtensions" />
      <xs:element name="include" type="NLogInclude" />
      <xs:element name="variable" type="NLogVariable" />
      <xs:element name="targets" type="NLogTargets" />
      <xs:element name="rules" type="NLogRules" />
      <xs:element name="time" type="TimeSource" />
    </xs:choice>
    <xs:attribute name="autoReload" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Watch config file for changes and reload automatically.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="internalLogToConsole" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Print internal NLog messages to the console. Default value is: false</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="internalLogToConsoleError" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Print internal NLog messages to the console error output. Default value is: false</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="internalLogFile" type="xs:string">
      <xs:annotation>
        <xs:documentation>Write internal NLog messages to the specified file.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="internalLogLevel" type="NLogLevel">
      <xs:annotation>
        <xs:documentation>Log level threshold for internal log messages. Default value is: Info.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="globalThreshold" type="NLogLevel">
      <xs:annotation>
        <xs:documentation>Global log level threshold for application log messages. Messages below this level won't be logged.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="throwExceptions" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Throw an exception when there is an internal error. Default value is: false. Not recommend to set to true in production!</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="throwConfigExceptions" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Throw an exception when there is a configuration error. If not set, determined by throwExceptions.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="keepVariablesOnReload" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Gets or sets a value indicating whether Variables should be kept on configuration reload. Default value is: false.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="internalLogToTrace" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Write internal NLog messages to the System.Diagnostics.Trace. Default value is: false.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="internalLogIncludeTimestamp" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Write timestamps for internal NLog messages. Default value is: true.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="useInvariantCulture" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Use InvariantCulture as default culture instead of CurrentCulture.  Default value is: false.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="parseMessageTemplates" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Perform message template parsing and formatting of LogEvent messages (true = Always, false = Never, empty = Auto Detect). Default value is: empty.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="NLogTargets">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="default-wrapper" type="WrapperTargetBase" />
      <xs:element name="default-target-parameters" type="Target" />
      <xs:element name="target" type="Target" />
      <xs:element name="wrapper-target" type="WrapperTargetBase" />
      <xs:element name="compound-target" type="CompoundTargetBase" />
    </xs:choice>
    <xs:attribute name="async" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Make all targets within this section asynchronous (creates additional threads but the calling thread isn't blocked by any target writes).</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="NLogRules">
    <xs:sequence minOccurs="0" maxOccurs="unbounded">
      <xs:element name="logger" type="NLogLoggerRule" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="NLogExtensions">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="add" type="NLogExtensionsAdd" />
    </xs:choice>
  </xs:complexType>
  <xs:complexType name="NLogExtensionsAdd">
    <xs:attribute name="prefix" type="xs:string">
      <xs:annotation>
        <xs:documentation>Prefix for targets/layout renderers/filters/conditions loaded from this assembly.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="assemblyFile" type="xs:string">
      <xs:annotation>
        <xs:documentation>Load NLog extensions from the specified file (*.dll)</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="assembly" type="xs:string">
      <xs:annotation>
        <xs:documentation>Load NLog extensions from the specified assembly. Assembly name should be fully qualified.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="NLogLoggerRule">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="filters" type="NLogFilters" />
    </xs:choice>
    <xs:attribute name="name" use="optional">
      <xs:annotation>
        <xs:documentation>Filter on the name of the logger. May include wildcard characters ('*' or '?').</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="levels" type="NLogLevelList">
      <xs:annotation>
        <xs:documentation>Comma separated list of levels that this rule matches.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="minlevel" type="NLogLevel">
      <xs:annotation>
        <xs:documentation>Minimum level that this rule matches.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="maxlevel" type="NLogLevel">
      <xs:annotation>
        <xs:documentation>Maximum level that this rule matches.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="level" type="NLogLevel">
      <xs:annotation>
        <xs:documentation>Level that this rule matches.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="writeTo" type="NLogTargetIDList">
      <xs:annotation>
        <xs:documentation>Comma separated list of target names.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="final" type="xs:boolean" default="false">
      <xs:annotation>
        <xs:documentation>Ignore further rules if this one matches.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="enabled" type="xs:boolean" default="true">
      <xs:annotation>
        <xs:documentation>Enable this rule. Note: disabled rules aren't available from the API.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="ruleName" type="xs:string" use="optional">
      <xs:annotation>
        <xs:documentation>Rule identifier to allow rule lookup with Configuration.FindRuleByName and Configuration.RemoveRuleByName.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="NLogFilters">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="when" type="when" />
      <xs:element name="whenContains" type="whenContains" />
      <xs:element name="whenEqual" type="whenEqual" />
      <xs:element name="whenNotContains" type="whenNotContains" />
      <xs:element name="whenNotEqual" type="whenNotEqual" />
      <xs:element name="whenRepeated" type="whenRepeated" />
    </xs:choice>
    <xs:attribute name="defaultAction" type="FilterResult">
      <xs:annotation>
        <xs:documentation>Default action if none of the filters match.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:simpleType name="NLogLevel">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Off" />
      <xs:enumeration value="Trace" />
      <xs:enumeration value="Debug" />
      <xs:enumeration value="Info" />
      <xs:enumeration value="Warn" />
      <xs:enumeration value="Error" />
      <xs:enumeration value="Fatal" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="LineEndingMode">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Default" />
      <xs:enumeration value="CRLF" />
      <xs:enumeration value="CR" />
      <xs:enumeration value="LF" />
      <xs:enumeration value="None" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="NLogLevelList">
    <xs:restriction base="xs:string">
      <xs:pattern value="(|Trace|Debug|Info|Warn|Error|Fatal)(,(Trace|Debug|Info|Warn|Error|Fatal))*" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="NLogInclude">
    <xs:attribute name="file" type="SimpleLayoutAttribute" use="required">
      <xs:annotation>
        <xs:documentation>Name of the file to be included. You could use * wildcard. The name is relative to the name of the current config file.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="ignoreErrors" type="xs:boolean" use="optional" default="false">
      <xs:annotation>
        <xs:documentation>Ignore any errors in the include file.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="NLogVariable">
    <xs:choice minOccurs="0" maxOccurs="1">
      <xs:element name="value" minOccurs="0" maxOccurs="1" type="xs:string">
        <xs:annotation>
          <xs:documentation>Variable value. Note, the 'value' attribute has precedence over this one.</xs:documentation>
        </xs:annotation>
      </xs:element>
    </xs:choice>
    <xs:attribute name="name" type="xs:string" use="required">
      <xs:annotation>
        <xs:documentation>Variable name.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="value" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Variable value.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:simpleType name="NLogTargetIDList">
    <xs:restriction base="xs:string">
      <xs:pattern value="(|([a-zA-Z][a-zA-Z0-9_\-]*))(,([a-zA-Z][a-zA-Z0-9_\-]*))*" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="Target" abstract="true"></xs:complexType>
  <xs:complexType name="TargetRef">
    <xs:attribute name="name" type="xs:string" use="required" />
  </xs:complexType>
  <xs:complexType name="WrapperTargetBase" abstract="true">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="target" type="Target" minOccurs="1" maxOccurs="1" />
          <xs:element name="wrapper-target" type="WrapperTargetBase" minOccurs="1" maxOccurs="1" />
          <xs:element name="compound-target" type="CompoundTargetBase" minOccurs="1" maxOccurs="1" />
          <xs:element name="target-ref" type="TargetRef" minOccurs="1" maxOccurs="1" />
          <xs:element name="wrapper-target-ref" type="TargetRef" minOccurs="1" maxOccurs="1" />
          <xs:element name="compound-target-ref" type="TargetRef" minOccurs="1" maxOccurs="1" />
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="CompoundTargetBase" abstract="true">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="target" type="Target" minOccurs="1" maxOccurs="unbounded" />
          <xs:element name="wrapper-target" type="WrapperTargetBase" minOccurs="1" maxOccurs="1" />
          <xs:element name="compound-target" type="CompoundTargetBase" minOccurs="1" maxOccurs="1" />
          <xs:element name="target-ref" type="TargetRef" minOccurs="1" maxOccurs="1" />
          <xs:element name="wrapper-target-ref" type="TargetRef" minOccurs="1" maxOccurs="1" />
          <xs:element name="compound-target-ref" type="TargetRef" minOccurs="1" maxOccurs="1" />
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="Filter" abstract="true"></xs:complexType>
  <xs:complexType name="TimeSource" abstract="true"></xs:complexType>
  <xs:simpleType name="SimpleLayoutAttribute">
    <xs:restriction base="xs:string">
      <xs:pattern value=".*" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="Condition">
    <xs:restriction base="xs:string">
      <xs:minLength value="1" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="AsyncWrapper">
    <xs:complexContent>
      <xs:extension base="WrapperTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="batchSize" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="forceLockingQueue" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="fullBatchSizeWriteLimit" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="overflowAction" minOccurs="0" maxOccurs="1" type="NLog.Targets.Wrappers.AsyncTargetWrapperOverflowAction" />
          <xs:element name="queueLimit" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="timeToSleepBetweenBatches" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="batchSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Number of log events that should be processed in a batch by the lazy writer thread.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="forceLockingQueue" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Whether to use the locking queue, instead of a lock-free concurrent queue The locking queue is less concurrent when many logger threads, but reduces memory allocation</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="fullBatchSizeWriteLimit" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Limit of full s to write before yielding into  Performance is better when writing many small batches, than writing a single large batch</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="overflowAction" type="NLog.Targets.Wrappers.AsyncTargetWrapperOverflowAction">
          <xs:annotation>
            <xs:documentation>Action to be taken when the lazy writer thread request queue count exceeds the set limit.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="queueLimit" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Limit on the number of requests in the lazy writer thread request queue.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="timeToSleepBetweenBatches" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Time in milliseconds to sleep between batches. (1 or less means trigger on new activity)</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="NLog.Targets.Wrappers.AsyncTargetWrapperOverflowAction">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Grow" />
      <xs:enumeration value="Discard" />
      <xs:enumeration value="Block" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="AutoFlushWrapper">
    <xs:complexContent>
      <xs:extension base="WrapperTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="asyncFlush" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="condition" minOccurs="0" maxOccurs="1" type="Condition" />
          <xs:element name="flushOnConditionOnly" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="asyncFlush" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Delay the flush until the LogEvent has been confirmed as written</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="condition" type="Condition">
          <xs:annotation>
            <xs:documentation>Condition expression. Log events who meet this condition will cause a flush on the wrapped target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="flushOnConditionOnly" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Only flush when LogEvent matches condition. Ignore explicit-flush, config-reload-flush and shutdown-flush</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BufferingWrapper">
    <xs:complexContent>
      <xs:extension base="WrapperTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="bufferSize" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="flushTimeout" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="overflowAction" minOccurs="0" maxOccurs="1" type="NLog.Targets.Wrappers.BufferingTargetWrapperOverflowAction" />
          <xs:element name="slidingTimeout" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="bufferSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Number of log events to be buffered.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="flushTimeout" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Timeout (in milliseconds) after which the contents of buffer will be flushed if there's no write in the specified period of time. Use -1 to disable timed flushes.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="overflowAction" type="NLog.Targets.Wrappers.BufferingTargetWrapperOverflowAction">
          <xs:annotation>
            <xs:documentation>Action to take if the buffer overflows.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="slidingTimeout" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to use sliding timeout.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="NLog.Targets.Wrappers.BufferingTargetWrapperOverflowAction">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Flush" />
      <xs:enumeration value="Discard" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="Chainsaw">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="encoding" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="lineEnding" minOccurs="0" maxOccurs="1" type="LineEndingMode" />
          <xs:element name="maxMessageSize" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="newLine" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="sslProtocols" minOccurs="0" maxOccurs="1" type="System.Security.Authentication.SslProtocols" />
          <xs:element name="address" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="connectionCacheSize" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="keepAliveTimeSeconds" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="maxQueueSize" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="maxConnections" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="onConnectionOverflow" minOccurs="0" maxOccurs="1" type="NLog.Targets.NetworkTargetConnectionsOverflowAction" />
          <xs:element name="onOverflow" minOccurs="0" maxOccurs="1" type="NLog.Targets.NetworkTargetOverflowAction" />
          <xs:element name="keepConnection" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="parameter" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.NLogViewerParameterInfo" />
          <xs:element name="ndlcItemSeparator" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="includeSourceInfo" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="loggerName" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="includeNLogData" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeNdlc" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeNdc" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeMdlc" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeMdc" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeCallSite" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeAllProperties" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="appInfo" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="ndcItemSeparator" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="encoding" type="xs:string">
          <xs:annotation>
            <xs:documentation>Encoding to be used.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Instance of  that is used to format log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="lineEnding" type="LineEndingMode">
          <xs:annotation>
            <xs:documentation>End of line value if a newline is appended at the end of log message .</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxMessageSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum message size in bytes.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="newLine" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to append newline at the end of log message.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="sslProtocols" type="System.Security.Authentication.SslProtocols">
          <xs:annotation>
            <xs:documentation>Get or set the SSL/TLS protocols. Default no SSL/TLS is used. Currently only implemented for TCP.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="address" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Network address.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="connectionCacheSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Size of the connection cache (number of connections which are kept alive).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="keepAliveTimeSeconds" type="xs:integer">
          <xs:annotation>
            <xs:documentation>The number of seconds a connection will remain idle before the first keep-alive probe is sent</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxQueueSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum queue size.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxConnections" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum current connections. 0 = no maximum.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="onConnectionOverflow" type="NLog.Targets.NetworkTargetConnectionsOverflowAction">
          <xs:annotation>
            <xs:documentation>Action that should be taken if the will be more connections than .</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="onOverflow" type="NLog.Targets.NetworkTargetOverflowAction">
          <xs:annotation>
            <xs:documentation>Action that should be taken if the message is larger than maxMessageSize.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="keepConnection" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to keep connection open whenever possible.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="ndlcItemSeparator" type="xs:string">
          <xs:annotation>
            <xs:documentation>NDLC item separator.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeSourceInfo" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include source info (file name and line number) in the information sent over the network.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="loggerName" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Renderer for log4j:event logger-xml-attribute (Default ${logger})</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeNLogData" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include NLog-specific extensions to log4j schema.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeNdlc" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include contents of the  stack.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeNdc" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include  stack contents.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeMdlc" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include  dictionary contents.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeMdc" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include  dictionary contents.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeCallSite" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include call site (class and method name) in the information sent over the network.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeAllProperties" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Option to include all properties from the log events</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="appInfo" type="xs:string">
          <xs:annotation>
            <xs:documentation>AppInfo field. By default it's the friendly name of the current AppDomain.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="ndcItemSeparator" type="xs:string">
          <xs:annotation>
            <xs:documentation>NDC item separator.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="System.Security.Authentication.SslProtocols">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="Ssl2" />
      <xs:enumeration value="Ssl3" />
      <xs:enumeration value="Tls" />
      <xs:enumeration value="Tls11" />
      <xs:enumeration value="Tls12" />
      <xs:enumeration value="Tls13" />
      <xs:enumeration value="Default" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="NLog.Targets.NetworkTargetConnectionsOverflowAction">
    <xs:restriction base="xs:string">
      <xs:enumeration value="AllowNewConnnection" />
      <xs:enumeration value="DiscardMessage" />
      <xs:enumeration value="Block" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="NLog.Targets.NetworkTargetOverflowAction">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Error" />
      <xs:enumeration value="Split" />
      <xs:enumeration value="Discard" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="NLog.Targets.NLogViewerParameterInfo">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
      <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
      <xs:element name="includeEmptyValue" minOccurs="0" maxOccurs="1" type="xs:boolean" />
    </xs:choice>
    <xs:attribute name="layout" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Layout that should be use to calculate the value for the parameter.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="name" type="xs:string">
      <xs:annotation>
        <xs:documentation>Viewer parameter name.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="includeEmptyValue" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Whether an attribute with empty value should be included in the output</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="ColoredConsole">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="header" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="footer" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="detectConsoleAvailable" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="enableAnsiOutput" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="encoding" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="errorStream" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="autoFlush" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="detectOutputRedirected" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="useDefaultRowHighlightingRules" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="highlight-row" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.ConsoleRowHighlightingRule" />
          <xs:element name="highlight-word" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.ConsoleWordHighlightingRule" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Text to be rendered.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="header" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Header.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="footer" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Footer.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="detectConsoleAvailable" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to auto-check if the console is available. - Disables console writing if Environment.UserInteractive = False (Windows Service) - Disables console writing if Console Standard Input is not available (Non-Console-App)</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="enableAnsiOutput" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Enables output using ANSI Color Codes</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="encoding" type="xs:string">
          <xs:annotation>
            <xs:documentation>The encoding for writing messages to the .</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="errorStream" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether the error stream (stderr) should be used instead of the output stream (stdout).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="autoFlush" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to auto-flush after </xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="detectOutputRedirected" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to auto-check if the console has been redirected to file - Disables coloring logic when System.Console.IsOutputRedirected = true</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="useDefaultRowHighlightingRules" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to use default row highlighting rules.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="NLog.Targets.ConsoleOutputColor">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Black" />
      <xs:enumeration value="DarkBlue" />
      <xs:enumeration value="DarkGreen" />
      <xs:enumeration value="DarkCyan" />
      <xs:enumeration value="DarkRed" />
      <xs:enumeration value="DarkMagenta" />
      <xs:enumeration value="DarkYellow" />
      <xs:enumeration value="Gray" />
      <xs:enumeration value="DarkGray" />
      <xs:enumeration value="Blue" />
      <xs:enumeration value="Green" />
      <xs:enumeration value="Cyan" />
      <xs:enumeration value="Red" />
      <xs:enumeration value="Magenta" />
      <xs:enumeration value="Yellow" />
      <xs:enumeration value="White" />
      <xs:enumeration value="NoChange" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="NLog.Targets.ConsoleRowHighlightingRule">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="condition" minOccurs="0" maxOccurs="1" type="Condition" />
      <xs:element name="backgroundColor" minOccurs="0" maxOccurs="1" type="NLog.Targets.ConsoleOutputColor" />
      <xs:element name="foregroundColor" minOccurs="0" maxOccurs="1" type="NLog.Targets.ConsoleOutputColor" />
    </xs:choice>
    <xs:attribute name="condition" type="Condition">
      <xs:annotation>
        <xs:documentation>Condition that must be met in order to set the specified foreground and background color.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="backgroundColor" type="NLog.Targets.ConsoleOutputColor">
      <xs:annotation>
        <xs:documentation>Background color.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="foregroundColor" type="NLog.Targets.ConsoleOutputColor">
      <xs:annotation>
        <xs:documentation>Foreground color.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="NLog.Targets.ConsoleWordHighlightingRule">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="compileRegex" minOccurs="0" maxOccurs="1" type="xs:boolean" />
      <xs:element name="condition" minOccurs="0" maxOccurs="1" type="Condition" />
      <xs:element name="ignoreCase" minOccurs="0" maxOccurs="1" type="xs:boolean" />
      <xs:element name="regex" minOccurs="0" maxOccurs="1" type="xs:string" />
      <xs:element name="text" minOccurs="0" maxOccurs="1" type="xs:string" />
      <xs:element name="wholeWords" minOccurs="0" maxOccurs="1" type="xs:boolean" />
      <xs:element name="backgroundColor" minOccurs="0" maxOccurs="1" type="NLog.Targets.ConsoleOutputColor" />
      <xs:element name="foregroundColor" minOccurs="0" maxOccurs="1" type="NLog.Targets.ConsoleOutputColor" />
    </xs:choice>
    <xs:attribute name="compileRegex" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Compile the ? This can improve the performance, but at the costs of more memory usage. If false, the Regex Cache is used.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="condition" type="Condition">
      <xs:annotation>
        <xs:documentation>Condition that must be met before scanning the row for highlight of words</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="ignoreCase" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Indicates whether to ignore case when comparing texts.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="regex" type="xs:string">
      <xs:annotation>
        <xs:documentation>Regular expression to be matched. You must specify either text or regex.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="text" type="xs:string">
      <xs:annotation>
        <xs:documentation>Text to be matched. You must specify either text or regex.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="wholeWords" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Indicates whether to match whole words only.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="backgroundColor" type="NLog.Targets.ConsoleOutputColor">
      <xs:annotation>
        <xs:documentation>Background color.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="foregroundColor" type="NLog.Targets.ConsoleOutputColor">
      <xs:annotation>
        <xs:documentation>Foreground color.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="Console">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="header" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="footer" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="autoFlush" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="detectConsoleAvailable" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="encoding" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="error" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="writeBuffer" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Text to be rendered.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="header" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Header.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="footer" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Footer.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="autoFlush" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to auto-flush after </xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="detectConsoleAvailable" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to auto-check if the console is available - Disables console writing if Environment.UserInteractive = False (Windows Service) - Disables console writing if Console Standard Input is not available (Non-Console-App)</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="encoding" type="xs:string">
          <xs:annotation>
            <xs:documentation>The encoding for writing messages to the .</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="error" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to send the log messages to the standard error instead of the standard output.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="writeBuffer" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Whether to enable batch writing using char[]-buffers, instead of using </xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="Database">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="useTransactions" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="keepConnection" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="dbProvider" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="dbPassword" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="dbHost" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="dbUserName" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="connectionStringName" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="connectionString" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="connectionproperty" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.DatabaseObjectPropertyInfo" />
          <xs:element name="commandproperty" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.DatabaseObjectPropertyInfo" />
          <xs:element name="dbDatabase" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="installConnectionString" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="install-command" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.DatabaseCommandInfo" />
          <xs:element name="uninstall-command" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.DatabaseCommandInfo" />
          <xs:element name="isolationLevel" minOccurs="0" maxOccurs="1" type="System.Data.IsolationLevel" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="commandText" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="commandType" minOccurs="0" maxOccurs="1" type="System.Data.CommandType" />
          <xs:element name="parameter" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.DatabaseParameterInfo" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="useTransactions" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Obsolete - value will be ignored! The logging code always runs outside of transaction. Gets or sets a value indicating whether to use database transactions. Some data providers require this.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="keepConnection" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to keep the database connection open between the log events.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="dbProvider" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the database provider.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="dbPassword" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Database password. If the ConnectionString is not provided this value will be used to construct the "Password=" part of the connection string.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="dbHost" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Database host name. If the ConnectionString is not provided this value will be used to construct the "Server=" part of the connection string.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="dbUserName" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Database user name. If the ConnectionString is not provided this value will be used to construct the "User ID=" part of the connection string.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="connectionStringName" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the connection string (as specified in &lt;connectionStrings&gt; configuration section.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="connectionString" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Connection string. When provided, it overrides the values specified in DBHost, DBUserName, DBPassword, DBDatabase.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="dbDatabase" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Database name. If the ConnectionString is not provided this value will be used to construct the "Database=" part of the connection string.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="installConnectionString" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Connection string using for installation and uninstallation. If not provided, regular ConnectionString is being used.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="isolationLevel" type="System.Data.IsolationLevel">
          <xs:annotation>
            <xs:documentation>Configures isolated transaction batch writing. If supported by the database, then it will improve insert performance.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="commandText" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Text of the SQL command to be run on each log level.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="commandType" type="System.Data.CommandType">
          <xs:annotation>
            <xs:documentation>Type of the SQL command to be run on each log level.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="System.Data.CommandType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Text" />
      <xs:enumeration value="StoredProcedure" />
      <xs:enumeration value="TableDirect" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="System.Data.IsolationLevel">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Unspecified" />
      <xs:enumeration value="Chaos" />
      <xs:enumeration value="ReadUncommitted" />
      <xs:enumeration value="ReadCommitted" />
      <xs:enumeration value="RepeatableRead" />
      <xs:enumeration value="Serializable" />
      <xs:enumeration value="Snapshot" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="NLog.Targets.DatabaseObjectPropertyInfo">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="format" minOccurs="0" maxOccurs="1" type="xs:string" />
      <xs:element name="culture" minOccurs="0" maxOccurs="1" type="xs:string" />
      <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
      <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
      <xs:element name="propertyType" minOccurs="0" maxOccurs="1" type="xs:string" />
    </xs:choice>
    <xs:attribute name="format" type="xs:string">
      <xs:annotation>
        <xs:documentation>Convert format of the property value</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="culture" type="xs:string">
      <xs:annotation>
        <xs:documentation>Culture used for parsing property string-value for type-conversion</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="layout" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Value to assign on the object-property</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="name" type="xs:string">
      <xs:annotation>
        <xs:documentation>Name for the object-property</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="propertyType" type="xs:string">
      <xs:annotation>
        <xs:documentation>Type of the object-property</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="NLog.Targets.DatabaseCommandInfo">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="commandType" minOccurs="0" maxOccurs="1" type="System.Data.CommandType" />
      <xs:element name="connectionString" minOccurs="0" maxOccurs="1" type="Layout" />
      <xs:element name="ignoreFailures" minOccurs="0" maxOccurs="1" type="xs:boolean" />
      <xs:element name="parameter" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.DatabaseParameterInfo" />
      <xs:element name="text" minOccurs="0" maxOccurs="1" type="Layout" />
    </xs:choice>
    <xs:attribute name="commandType" type="System.Data.CommandType">
      <xs:annotation>
        <xs:documentation>Type of the command.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="connectionString" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Connection string to run the command against. If not provided, connection string from the target is used.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="ignoreFailures" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Indicates whether to ignore failures.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="text" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Command text.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="NLog.Targets.DatabaseParameterInfo">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
      <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
      <xs:element name="dbType" minOccurs="0" maxOccurs="1" type="xs:string" />
      <xs:element name="size" minOccurs="0" maxOccurs="1" type="xs:integer" />
      <xs:element name="precision" minOccurs="0" maxOccurs="1" type="xs:byte" />
      <xs:element name="scale" minOccurs="0" maxOccurs="1" type="xs:byte" />
      <xs:element name="parameterType" minOccurs="0" maxOccurs="1" type="xs:string" />
      <xs:element name="allowDbNull" minOccurs="0" maxOccurs="1" type="xs:boolean" />
      <xs:element name="format" minOccurs="0" maxOccurs="1" type="xs:string" />
      <xs:element name="culture" minOccurs="0" maxOccurs="1" type="xs:string" />
    </xs:choice>
    <xs:attribute name="name" type="xs:string">
      <xs:annotation>
        <xs:documentation>Database parameter name.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="layout" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Layout that should be use to calculate the value for the parameter.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="dbType" type="xs:string">
      <xs:annotation>
        <xs:documentation>Database parameter DbType.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="size" type="xs:integer">
      <xs:annotation>
        <xs:documentation>Database parameter size.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="precision" type="xs:byte">
      <xs:annotation>
        <xs:documentation>Database parameter precision.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="scale" type="xs:byte">
      <xs:annotation>
        <xs:documentation>Database parameter scale.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="parameterType" type="xs:string">
      <xs:annotation>
        <xs:documentation>Type of the parameter.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="allowDbNull" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Whether empty value should translate into DbNull. Requires database column to allow NULL values.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="format" type="xs:string">
      <xs:annotation>
        <xs:documentation>Convert format of the database parameter value.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="culture" type="xs:string">
      <xs:annotation>
        <xs:documentation>Culture used for parsing parameter string-value for type-conversion</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="Debugger">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="header" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="footer" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Text to be rendered.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="header" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Header.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="footer" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Footer.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="Debug">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout used to format log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="EventLog">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="category" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="entryType" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="eventId" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="log" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="machineName" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="maxKilobytes" minOccurs="0" maxOccurs="1" type="xs:long" />
          <xs:element name="maxMessageLength" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="source" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="onOverflow" minOccurs="0" maxOccurs="1" type="NLog.Targets.EventLogTargetOverflowAction" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout used to format log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="category" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout that renders event Category.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="entryType" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Optional entry type. When not set, or when not convertible to  then determined by </xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="eventId" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout that renders event ID.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="log" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the Event Log to write to. This can be System, Application or any user-defined name.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="machineName" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the machine on which Event Log service is running.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxKilobytes" type="xs:long">
          <xs:annotation>
            <xs:documentation>Maximum Event log size in kilobytes.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxMessageLength" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Message length limit to write to the Event Log.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="source" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Value to be used as the event Source.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="onOverflow" type="NLog.Targets.EventLogTargetOverflowAction">
          <xs:annotation>
            <xs:documentation>Action to take if the message is larger than the  option.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="NLog.Targets.EventLogTargetOverflowAction">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Truncate" />
      <xs:enumeration value="Split" />
      <xs:enumeration value="Discard" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="FallbackGroup">
    <xs:complexContent>
      <xs:extension base="CompoundTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="returnToFirstOnSuccess" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="returnToFirstOnSuccess" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to return to the first target after any successful write.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="File">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="header" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="footer" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="encoding" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="lineEnding" minOccurs="0" maxOccurs="1" type="LineEndingMode" />
          <xs:element name="maxArchiveDays" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="enableArchiveFileCompression" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="archiveNumbering" minOccurs="0" maxOccurs="1" type="NLog.Targets.ArchiveNumberingMode" />
          <xs:element name="archiveFileName" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="archiveFileKind" minOccurs="0" maxOccurs="1" type="NLog.Targets.FilePathKind" />
          <xs:element name="archiveEvery" minOccurs="0" maxOccurs="1" type="NLog.Targets.FileArchivePeriod" />
          <xs:element name="archiveAboveSize" minOccurs="0" maxOccurs="1" type="xs:long" />
          <xs:element name="maxArchiveFiles" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="writeFooterOnArchivingOnly" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="maxLogFilenames" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="deleteOldFileOnStartup" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="fileAttributes" minOccurs="0" maxOccurs="1" type="NLog.Targets.Win32FileAttributes" />
          <xs:element name="createDirs" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="cleanupFileName" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="archiveOldFileOnStartupAboveSize" minOccurs="0" maxOccurs="1" type="xs:long" />
          <xs:element name="archiveOldFileOnStartup" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="archiveDateFormat" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="enableFileDelete" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="writeBom" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="replaceFileContentsOnEachWrite" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="forceMutexConcurrentWrites" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="forceManaged" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="fileNameKind" minOccurs="0" maxOccurs="1" type="NLog.Targets.FilePathKind" />
          <xs:element name="fileName" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="networkWrites" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="openFileCacheTimeout" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="openFileCacheSize" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="keepFileOpen" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="discardAll" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="concurrentWrites" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="concurrentWriteAttempts" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="concurrentWriteAttemptDelay" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="bufferSize" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="openFileFlushTimeout" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="autoFlush" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Text to be rendered.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="header" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Header.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="footer" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Footer.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="encoding" type="xs:string">
          <xs:annotation>
            <xs:documentation>File encoding.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="lineEnding" type="LineEndingMode">
          <xs:annotation>
            <xs:documentation>Line ending mode.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxArchiveDays" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum days of archive files that should be kept.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="enableArchiveFileCompression" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to compress archive files into the zip archive format.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="archiveNumbering" type="NLog.Targets.ArchiveNumberingMode">
          <xs:annotation>
            <xs:documentation>Way file archives are numbered.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="archiveFileName" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Name of the file to be used for an archive.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="archiveFileKind" type="NLog.Targets.FilePathKind">
          <xs:annotation>
            <xs:documentation>Is the  an absolute or relative path?</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="archiveEvery" type="NLog.Targets.FileArchivePeriod">
          <xs:annotation>
            <xs:documentation>Indicates whether to automatically archive log files every time the specified time passes.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="archiveAboveSize" type="xs:long">
          <xs:annotation>
            <xs:documentation>Size in bytes above which log files will be automatically archived. Warning: combining this with  isn't supported. We cannot create multiple archive files, if they should have the same name. Choose: </xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxArchiveFiles" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum number of archive files that should be kept.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="writeFooterOnArchivingOnly" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether the footer should be written only when the file is archived.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxLogFilenames" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum number of log file names that should be stored as existing.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="deleteOldFileOnStartup" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to delete old log file on startup.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="fileAttributes" type="NLog.Targets.Win32FileAttributes">
          <xs:annotation>
            <xs:documentation>File attributes (Windows only).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="createDirs" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to create directories if they do not exist.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="cleanupFileName" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Cleanup invalid values in a filename, e.g. slashes in a filename. If set to true, this can impact the performance of massive writes. If set to false, nothing gets written when the filename is wrong.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="archiveOldFileOnStartupAboveSize" type="xs:long">
          <xs:annotation>
            <xs:documentation>Value of the file size threshold to archive old log file on startup.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="archiveOldFileOnStartup" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to archive old log file on startup.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="archiveDateFormat" type="xs:string">
          <xs:annotation>
            <xs:documentation>Value specifying the date format to use when archiving files.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="enableFileDelete" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to enable log file(s) to be deleted.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="writeBom" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to write BOM (byte order mark) in created files</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="replaceFileContentsOnEachWrite" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to replace file contents on each write instead of appending log message at the end.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="forceMutexConcurrentWrites" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether file creation calls should be synchronized by a system global mutex.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="forceManaged" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Gets or set a value indicating whether a managed file stream is forced, instead of using the native implementation.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="fileNameKind" type="NLog.Targets.FilePathKind">
          <xs:annotation>
            <xs:documentation>Is the  an absolute or relative path?</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="fileName" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Name of the file to write to.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="networkWrites" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether concurrent writes to the log file by multiple processes on different network hosts.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="openFileCacheTimeout" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum number of seconds that files are kept open. If this number is negative the files are not automatically closed after a period of inactivity.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="openFileCacheSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Number of files to be kept open. Setting this to a higher value may improve performance in a situation where a single File target is writing to many files (such as splitting by level or by logger).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="keepFileOpen" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to keep log file open instead of opening and closing it on each logging event.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="discardAll" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Whether or not this target should just discard all data that its asked to write. Mostly used for when testing NLog Stack except final write</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="concurrentWrites" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether concurrent writes to the log file by multiple processes on the same host.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="concurrentWriteAttempts" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Number of times the write is appended on the file before NLog discards the log message.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="concurrentWriteAttemptDelay" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Delay in milliseconds to wait before attempting to write to the file again.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="bufferSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Log file buffer size in bytes.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="openFileFlushTimeout" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum number of seconds before open files are flushed. If this number is negative or zero the files are not flushed by timer.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="autoFlush" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to automatically flush the file buffers after each log message.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="NLog.Targets.ArchiveNumberingMode">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Sequence" />
      <xs:enumeration value="Rolling" />
      <xs:enumeration value="Date" />
      <xs:enumeration value="DateAndSequence" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="NLog.Targets.FilePathKind">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Unknown" />
      <xs:enumeration value="Relative" />
      <xs:enumeration value="Absolute" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="NLog.Targets.FileArchivePeriod">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="Year" />
      <xs:enumeration value="Month" />
      <xs:enumeration value="Day" />
      <xs:enumeration value="Hour" />
      <xs:enumeration value="Minute" />
      <xs:enumeration value="Sunday" />
      <xs:enumeration value="Monday" />
      <xs:enumeration value="Tuesday" />
      <xs:enumeration value="Wednesday" />
      <xs:enumeration value="Thursday" />
      <xs:enumeration value="Friday" />
      <xs:enumeration value="Saturday" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="NLog.Targets.Win32FileAttributes">
    <xs:restriction base="xs:string">
      <xs:enumeration value="ReadOnly" />
      <xs:enumeration value="Hidden" />
      <xs:enumeration value="System" />
      <xs:enumeration value="Archive" />
      <xs:enumeration value="Device" />
      <xs:enumeration value="Normal" />
      <xs:enumeration value="Temporary" />
      <xs:enumeration value="SparseFile" />
      <xs:enumeration value="ReparsePoint" />
      <xs:enumeration value="Compressed" />
      <xs:enumeration value="NotContentIndexed" />
      <xs:enumeration value="Encrypted" />
      <xs:enumeration value="WriteThrough" />
      <xs:enumeration value="NoBuffering" />
      <xs:enumeration value="DeleteOnClose" />
      <xs:enumeration value="PosixSemantics" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="FilteringWrapper">
    <xs:complexContent>
      <xs:extension base="WrapperTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="condition" minOccurs="0" maxOccurs="1" type="Condition" />
          <xs:element name="filter" minOccurs="0" maxOccurs="1" type="Filter" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="condition" type="Condition">
          <xs:annotation>
            <xs:documentation>Condition expression. Log events who meet this condition will be forwarded to the wrapped target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ImpersonatingWrapper">
    <xs:complexContent>
      <xs:extension base="WrapperTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="domain" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="impersonationLevel" minOccurs="0" maxOccurs="1" type="NLog.Targets.Wrappers.SecurityImpersonationLevel" />
          <xs:element name="logOnProvider" minOccurs="0" maxOccurs="1" type="NLog.Targets.Wrappers.LogOnProviderType" />
          <xs:element name="logOnType" minOccurs="0" maxOccurs="1" type="NLog.Targets.Wrappers.SecurityLogOnType" />
          <xs:element name="password" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="revertToSelf" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="userName" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="domain" type="xs:string">
          <xs:annotation>
            <xs:documentation>Windows domain name to change context to.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="impersonationLevel" type="NLog.Targets.Wrappers.SecurityImpersonationLevel">
          <xs:annotation>
            <xs:documentation>Required impersonation level.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="logOnProvider" type="NLog.Targets.Wrappers.LogOnProviderType">
          <xs:annotation>
            <xs:documentation>Type of the logon provider.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="logOnType" type="NLog.Targets.Wrappers.SecurityLogOnType">
          <xs:annotation>
            <xs:documentation>Logon Type.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="password" type="xs:string">
          <xs:annotation>
            <xs:documentation>User account password.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="revertToSelf" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to revert to the credentials of the process instead of impersonating another user.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="userName" type="xs:string">
          <xs:annotation>
            <xs:documentation>Username to change context to.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="NLog.Targets.Wrappers.SecurityImpersonationLevel">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Anonymous" />
      <xs:enumeration value="Identification" />
      <xs:enumeration value="Impersonation" />
      <xs:enumeration value="Delegation" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="NLog.Targets.Wrappers.LogOnProviderType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Default" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="NLog.Targets.Wrappers.SecurityLogOnType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Interactive" />
      <xs:enumeration value="Network" />
      <xs:enumeration value="Batch" />
      <xs:enumeration value="Service" />
      <xs:enumeration value="NetworkClearText" />
      <xs:enumeration value="NewCredentials" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="LimitingWrapper">
    <xs:complexContent>
      <xs:extension base="WrapperTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="interval" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="messageLimit" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="interval" type="xs:string">
          <xs:annotation>
            <xs:documentation>Interval in which messages will be written up to the  number of messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="messageLimit" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum allowed number of messages written per .</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="LogReceiverService">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="endpointAddress" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="endpointConfigurationName" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="useOneWayContract" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="clientId" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="includeEventProperties" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="parameter" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.MethodCallParameter" />
          <xs:element name="useBinaryEncoding" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="endpointAddress" type="xs:string">
          <xs:annotation>
            <xs:documentation>Endpoint address.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="endpointConfigurationName" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the endpoint configuration in WCF configuration file.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="useOneWayContract" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to use a WCF service contract that is one way (fire and forget) or two way (request-reply)</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="clientId" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Client ID.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeEventProperties" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include per-event properties in the payload sent to the server.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="useBinaryEncoding" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to use binary message encoding.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="NLog.Targets.MethodCallParameter">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
      <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
      <xs:element name="parameterType" minOccurs="0" maxOccurs="1" type="xs:string" />
      <xs:element name="type" minOccurs="0" maxOccurs="1" type="xs:string" />
    </xs:choice>
    <xs:attribute name="layout" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Layout that should be use to calculate the value for the parameter.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="name" type="xs:string">
      <xs:annotation>
        <xs:documentation>Name of the parameter.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="parameterType" type="xs:string">
      <xs:annotation>
        <xs:documentation>Type of the parameter.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="type" type="xs:string">
      <xs:annotation>
        <xs:documentation>Type of the parameter. Obsolete alias for </xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="Mail">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="header" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="footer" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="replaceNewlineWithBrTagInHtml" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="priority" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="encoding" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="bcc" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="cc" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="addNewLines" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="html" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="from" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="body" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="subject" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="to" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="timeout" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="smtpServer" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="smtpAuthentication" minOccurs="0" maxOccurs="1" type="NLog.Targets.SmtpAuthenticationMode" />
          <xs:element name="smtpUserName" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="smtpPassword" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="enableSsl" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="smtpPort" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="useSystemNetMailSettings" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="pickupDirectoryLocation" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="deliveryMethod" minOccurs="0" maxOccurs="1" type="System.Net.Mail.SmtpDeliveryMethod" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Text to be rendered.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="header" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Header.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="footer" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Footer.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="replaceNewlineWithBrTagInHtml" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether NewLine characters in the body should be replaced with  tags.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="priority" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Priority used for sending mails.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="encoding" type="xs:string">
          <xs:annotation>
            <xs:documentation>Encoding to be used for sending e-mail.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="bcc" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>BCC email addresses separated by semicolons (e.g. <EMAIL>;<EMAIL>).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="cc" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>CC email addresses separated by semicolons (e.g. <EMAIL>;<EMAIL>).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="addNewLines" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to add new lines between log entries.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="html" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to send message as HTML instead of plain text.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="from" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Sender's email address (e.g. <EMAIL>).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="body" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Mail message body (repeated for each log message send in one mail).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="subject" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Mail subject.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="to" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Recipients' email addresses separated by semicolons (e.g. <EMAIL>;<EMAIL>).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="timeout" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Indicates the SMTP client timeout.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="smtpServer" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>SMTP Server to be used for sending.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="smtpAuthentication" type="NLog.Targets.SmtpAuthenticationMode">
          <xs:annotation>
            <xs:documentation>SMTP Authentication mode.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="smtpUserName" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Username used to connect to SMTP server (used when SmtpAuthentication is set to "basic").</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="smtpPassword" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Password used to authenticate against SMTP server (used when SmtpAuthentication is set to "basic").</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="enableSsl" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether SSL (secure sockets layer) should be used when communicating with SMTP server.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="smtpPort" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Port number that SMTP Server is listening on.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="useSystemNetMailSettings" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether the default Settings from System.Net.MailSettings should be used.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="pickupDirectoryLocation" type="xs:string">
          <xs:annotation>
            <xs:documentation>Folder where applications save mail messages to be processed by the local SMTP server.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="deliveryMethod" type="System.Net.Mail.SmtpDeliveryMethod">
          <xs:annotation>
            <xs:documentation>Specifies how outgoing email messages will be handled.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="NLog.Targets.SmtpAuthenticationMode">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="Basic" />
      <xs:enumeration value="Ntlm" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="System.Net.Mail.SmtpDeliveryMethod">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Network" />
      <xs:enumeration value="SpecifiedPickupDirectory" />
      <xs:enumeration value="PickupDirectoryFromIis" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="Memory">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="maxLogsCount" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout used to format log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxLogsCount" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Max number of items to have in memory</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="MethodCall">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="className" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="methodName" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="parameter" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.MethodCallParameter" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="className" type="xs:string">
          <xs:annotation>
            <xs:documentation>Class name.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="methodName" type="xs:string">
          <xs:annotation>
            <xs:documentation>Method name. The method must be public and static. Use the AssemblyQualifiedName , https://msdn.microsoft.com/en-us/library/system.type.assemblyqualifiedname(v=vs.110).aspx e.g.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="Network">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="encoding" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="lineEnding" minOccurs="0" maxOccurs="1" type="LineEndingMode" />
          <xs:element name="maxMessageSize" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="newLine" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="address" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="connectionCacheSize" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="keepAliveTimeSeconds" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="keepConnection" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="maxConnections" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="maxQueueSize" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="onConnectionOverflow" minOccurs="0" maxOccurs="1" type="NLog.Targets.NetworkTargetConnectionsOverflowAction" />
          <xs:element name="onOverflow" minOccurs="0" maxOccurs="1" type="NLog.Targets.NetworkTargetOverflowAction" />
          <xs:element name="sslProtocols" minOccurs="0" maxOccurs="1" type="System.Security.Authentication.SslProtocols" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout used to format log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="encoding" type="xs:string">
          <xs:annotation>
            <xs:documentation>Encoding to be used.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="lineEnding" type="LineEndingMode">
          <xs:annotation>
            <xs:documentation>End of line value if a newline is appended at the end of log message .</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxMessageSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum message size in bytes.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="newLine" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to append newline at the end of log message.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="address" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Network address.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="connectionCacheSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Size of the connection cache (number of connections which are kept alive).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="keepAliveTimeSeconds" type="xs:integer">
          <xs:annotation>
            <xs:documentation>The number of seconds a connection will remain idle before the first keep-alive probe is sent</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="keepConnection" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to keep connection open whenever possible.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxConnections" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum current connections. 0 = no maximum.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxQueueSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum queue size.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="onConnectionOverflow" type="NLog.Targets.NetworkTargetConnectionsOverflowAction">
          <xs:annotation>
            <xs:documentation>Action that should be taken if the will be more connections than .</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="onOverflow" type="NLog.Targets.NetworkTargetOverflowAction">
          <xs:annotation>
            <xs:documentation>Action that should be taken if the message is larger than maxMessageSize.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="sslProtocols" type="System.Security.Authentication.SslProtocols">
          <xs:annotation>
            <xs:documentation>Get or set the SSL/TLS protocols. Default no SSL/TLS is used. Currently only implemented for TCP.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="NLogViewer">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="encoding" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="lineEnding" minOccurs="0" maxOccurs="1" type="LineEndingMode" />
          <xs:element name="maxMessageSize" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="newLine" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="sslProtocols" minOccurs="0" maxOccurs="1" type="System.Security.Authentication.SslProtocols" />
          <xs:element name="address" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="connectionCacheSize" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="keepAliveTimeSeconds" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="maxQueueSize" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="maxConnections" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="onConnectionOverflow" minOccurs="0" maxOccurs="1" type="NLog.Targets.NetworkTargetConnectionsOverflowAction" />
          <xs:element name="onOverflow" minOccurs="0" maxOccurs="1" type="NLog.Targets.NetworkTargetOverflowAction" />
          <xs:element name="keepConnection" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="parameter" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.NLogViewerParameterInfo" />
          <xs:element name="ndlcItemSeparator" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="includeSourceInfo" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="loggerName" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="includeNLogData" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeNdlc" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeNdc" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeMdlc" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeMdc" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeCallSite" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeAllProperties" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="appInfo" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="ndcItemSeparator" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="encoding" type="xs:string">
          <xs:annotation>
            <xs:documentation>Encoding to be used.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Instance of  that is used to format log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="lineEnding" type="LineEndingMode">
          <xs:annotation>
            <xs:documentation>End of line value if a newline is appended at the end of log message .</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxMessageSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum message size in bytes.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="newLine" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to append newline at the end of log message.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="sslProtocols" type="System.Security.Authentication.SslProtocols">
          <xs:annotation>
            <xs:documentation>Get or set the SSL/TLS protocols. Default no SSL/TLS is used. Currently only implemented for TCP.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="address" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Network address.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="connectionCacheSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Size of the connection cache (number of connections which are kept alive).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="keepAliveTimeSeconds" type="xs:integer">
          <xs:annotation>
            <xs:documentation>The number of seconds a connection will remain idle before the first keep-alive probe is sent</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxQueueSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum queue size.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxConnections" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Maximum current connections. 0 = no maximum.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="onConnectionOverflow" type="NLog.Targets.NetworkTargetConnectionsOverflowAction">
          <xs:annotation>
            <xs:documentation>Action that should be taken if the will be more connections than .</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="onOverflow" type="NLog.Targets.NetworkTargetOverflowAction">
          <xs:annotation>
            <xs:documentation>Action that should be taken if the message is larger than maxMessageSize.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="keepConnection" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to keep connection open whenever possible.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="ndlcItemSeparator" type="xs:string">
          <xs:annotation>
            <xs:documentation>NDLC item separator.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeSourceInfo" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include source info (file name and line number) in the information sent over the network.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="loggerName" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Renderer for log4j:event logger-xml-attribute (Default ${logger})</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeNLogData" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include NLog-specific extensions to log4j schema.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeNdlc" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include contents of the  stack.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeNdc" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include  stack contents.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeMdlc" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include  dictionary contents.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeMdc" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include  dictionary contents.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeCallSite" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include call site (class and method name) in the information sent over the network.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeAllProperties" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Option to include all properties from the log events</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="appInfo" type="xs:string">
          <xs:annotation>
            <xs:documentation>AppInfo field. By default it's the friendly name of the current AppDomain.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="ndcItemSeparator" type="xs:string">
          <xs:annotation>
            <xs:documentation>NDC item separator.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="Null">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="formatMessage" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout used to format log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="formatMessage" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to perform layout calculation.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="OutputDebugString">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout used to format log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="PerfCounter">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="autoCreate" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="categoryName" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="counterHelp" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="counterName" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="counterType" minOccurs="0" maxOccurs="1" type="System.Diagnostics.PerformanceCounterType" />
          <xs:element name="incrementValue" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="instanceName" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="autoCreate" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether performance counter should be automatically created.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="categoryName" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the performance counter category.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="counterHelp" type="xs:string">
          <xs:annotation>
            <xs:documentation>Counter help text.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="counterName" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the performance counter.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="counterType" type="System.Diagnostics.PerformanceCounterType">
          <xs:annotation>
            <xs:documentation>Performance counter type.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="incrementValue" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>The value by which to increment the counter.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="instanceName" type="xs:string">
          <xs:annotation>
            <xs:documentation>Performance counter instance name.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="System.Diagnostics.PerformanceCounterType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="NumberOfItems32" />
      <xs:enumeration value="NumberOfItems64" />
      <xs:enumeration value="NumberOfItemsHEX32" />
      <xs:enumeration value="NumberOfItemsHEX64" />
      <xs:enumeration value="RateOfCountsPerSecond32" />
      <xs:enumeration value="RateOfCountsPerSecond64" />
      <xs:enumeration value="CountPerTimeInterval32" />
      <xs:enumeration value="CountPerTimeInterval64" />
      <xs:enumeration value="RawFraction" />
      <xs:enumeration value="RawBase" />
      <xs:enumeration value="AverageTimer32" />
      <xs:enumeration value="AverageBase" />
      <xs:enumeration value="AverageCount64" />
      <xs:enumeration value="SampleFraction" />
      <xs:enumeration value="SampleCounter" />
      <xs:enumeration value="SampleBase" />
      <xs:enumeration value="CounterTimer" />
      <xs:enumeration value="CounterTimerInverse" />
      <xs:enumeration value="Timer100Ns" />
      <xs:enumeration value="Timer100NsInverse" />
      <xs:enumeration value="ElapsedTime" />
      <xs:enumeration value="CounterMultiTimer" />
      <xs:enumeration value="CounterMultiTimerInverse" />
      <xs:enumeration value="CounterMultiTimer100Ns" />
      <xs:enumeration value="CounterMultiTimer100NsInverse" />
      <xs:enumeration value="CounterMultiBase" />
      <xs:enumeration value="CounterDelta32" />
      <xs:enumeration value="CounterDelta64" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="PostFilteringWrapper">
    <xs:complexContent>
      <xs:extension base="WrapperTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="defaultFilter" minOccurs="0" maxOccurs="1" type="Condition" />
          <xs:element name="when" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.Wrappers.FilteringRule" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="defaultFilter" type="Condition">
          <xs:annotation>
            <xs:documentation>Default filter to be applied when no specific rule matches.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="NLog.Targets.Wrappers.FilteringRule">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="exists" minOccurs="0" maxOccurs="1" type="Condition" />
      <xs:element name="filter" minOccurs="0" maxOccurs="1" type="Condition" />
    </xs:choice>
    <xs:attribute name="exists" type="Condition">
      <xs:annotation>
        <xs:documentation>Condition to be tested.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="filter" type="Condition">
      <xs:annotation>
        <xs:documentation>Resulting filter to be applied when the condition matches.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="RandomizeGroup">
    <xs:complexContent>
      <xs:extension base="CompoundTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="RepeatingWrapper">
    <xs:complexContent>
      <xs:extension base="WrapperTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="repeatCount" minOccurs="0" maxOccurs="1" type="xs:integer" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="repeatCount" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Number of times to repeat each log message.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="RetryingWrapper">
    <xs:complexContent>
      <xs:extension base="WrapperTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="retryCount" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="retryDelayMilliseconds" minOccurs="0" maxOccurs="1" type="xs:integer" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="retryCount" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Number of retries that should be attempted on the wrapped target in case of a failure.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="retryDelayMilliseconds" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Time to wait between retries in milliseconds.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="RoundRobinGroup">
    <xs:complexContent>
      <xs:extension base="CompoundTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="SplitGroup">
    <xs:complexContent>
      <xs:extension base="CompoundTargetBase">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="Trace">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="enableTraceFail" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="rawWrite" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout used to format log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="enableTraceFail" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Forward  to  (Instead of )</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="rawWrite" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Always use  independent of </xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="WebService">
    <xs:complexContent>
      <xs:extension base="Target">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="parameter" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.MethodCallParameter" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeBOM" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="methodName" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="namespace" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="protocol" minOccurs="0" maxOccurs="1" type="NLog.Targets.WebServiceProtocol" />
          <xs:element name="proxyAddress" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="encoding" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="url" minOccurs="0" maxOccurs="1" type="xs:anyURI" />
          <xs:element name="escapeDataNLogLegacy" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="escapeDataRfc3986" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="preAuthenticate" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="xmlRoot" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="xmlRootNamespace" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="header" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.MethodCallParameter" />
          <xs:element name="proxyType" minOccurs="0" maxOccurs="1" type="NLog.Targets.WebServiceProxyType" />
        </xs:choice>
        <xs:attribute name="name" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the target.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Target supports reuse of internal buffers, and doesn't have to constantly allocate new buffers Required for legacy NLog-targets, that expects buffers to remain stable after Write-method exit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeBOM" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Should we include the BOM (Byte-order-mark) for UTF? Influences the  property. This will only work for UTF-8.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="methodName" type="xs:string">
          <xs:annotation>
            <xs:documentation>Web service method name. Only used with Soap.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="namespace" type="xs:string">
          <xs:annotation>
            <xs:documentation>Web service namespace. Only used with Soap.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="protocol" type="NLog.Targets.WebServiceProtocol">
          <xs:annotation>
            <xs:documentation>Protocol to be used when calling web service.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="proxyAddress" type="xs:string">
          <xs:annotation>
            <xs:documentation>Custom proxy address, include port separated by a colon</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="encoding" type="xs:string">
          <xs:annotation>
            <xs:documentation>Encoding.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="url" type="xs:anyURI">
          <xs:annotation>
            <xs:documentation>Web service URL.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="escapeDataNLogLegacy" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Value whether escaping be done according to the old NLog style (Very non-standard)</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="escapeDataRfc3986" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Value whether escaping be done according to Rfc3986 (Supports Internationalized Resource Identifiers - IRIs)</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="preAuthenticate" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to pre-authenticate the HttpWebRequest (Requires 'Authorization' in  parameters)</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="xmlRoot" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the root XML element, if POST of XML document chosen. If so, this property must not be null. (see  and ).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="xmlRootNamespace" type="xs:string">
          <xs:annotation>
            <xs:documentation>(optional) root namespace of the XML document, if POST of XML document chosen. (see  and ).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="proxyType" type="NLog.Targets.WebServiceProxyType">
          <xs:annotation>
            <xs:documentation>Proxy configuration when calling web service</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="NLog.Targets.WebServiceProtocol">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Soap11" />
      <xs:enumeration value="Soap12" />
      <xs:enumeration value="HttpPost" />
      <xs:enumeration value="HttpGet" />
      <xs:enumeration value="JsonPost" />
      <xs:enumeration value="XmlPost" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="NLog.Targets.WebServiceProxyType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="DefaultWebProxy" />
      <xs:enumeration value="AutoProxy" />
      <xs:enumeration value="NoProxy" />
      <xs:enumeration value="ProxyAddress" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="CompoundLayout">
    <xs:complexContent>
      <xs:extension base="Layout">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="layout" minOccurs="0" maxOccurs="unbounded" type="Layout" />
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="Layout">
    <xs:choice minOccurs="0" maxOccurs="unbounded" />
  </xs:complexType>
  <xs:complexType name="CsvLayout">
    <xs:complexContent>
      <xs:extension base="Layout">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="footer" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="header" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="column" minOccurs="0" maxOccurs="unbounded" type="NLog.Layouts.CsvColumn" />
          <xs:element name="customColumnDelimiter" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="delimiter" minOccurs="0" maxOccurs="1" type="NLog.Layouts.CsvColumnDelimiterMode" />
          <xs:element name="quoteChar" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="quoting" minOccurs="0" maxOccurs="1" type="NLog.Layouts.CsvQuotingMode" />
          <xs:element name="withHeader" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="footer" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Footer layout.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="header" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Header layout.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Body layout (can be repeated multiple times).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="customColumnDelimiter" type="xs:string">
          <xs:annotation>
            <xs:documentation>Custom column delimiter value (valid when ColumnDelimiter is set to 'Custom').</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="delimiter" type="NLog.Layouts.CsvColumnDelimiterMode">
          <xs:annotation>
            <xs:documentation>Column delimiter.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="quoteChar" type="xs:string">
          <xs:annotation>
            <xs:documentation>Quote Character.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="quoting" type="NLog.Layouts.CsvQuotingMode">
          <xs:annotation>
            <xs:documentation>Quoting mode.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="withHeader" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether CVS should include header.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="NLog.Layouts.CsvQuotingMode">
    <xs:restriction base="xs:string">
      <xs:enumeration value="All" />
      <xs:enumeration value="Nothing" />
      <xs:enumeration value="Auto" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="NLog.Layouts.CsvColumnDelimiterMode">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Auto" />
      <xs:enumeration value="Comma" />
      <xs:enumeration value="Semicolon" />
      <xs:enumeration value="Tab" />
      <xs:enumeration value="Pipe" />
      <xs:enumeration value="Space" />
      <xs:enumeration value="Custom" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="NLog.Layouts.CsvColumn">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
      <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
      <xs:element name="quoting" minOccurs="0" maxOccurs="1" type="NLog.Layouts.CsvQuotingMode" />
    </xs:choice>
    <xs:attribute name="layout" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Layout of the column.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="name" type="xs:string">
      <xs:annotation>
        <xs:documentation>Name of the column.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="quoting" type="NLog.Layouts.CsvQuotingMode">
      <xs:annotation>
        <xs:documentation>Override of Quoting mode</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="JsonLayout">
    <xs:complexContent>
      <xs:extension base="Layout">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="escapeForwardSlash" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="renderEmptyObject" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="suppressSpaces" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="attribute" minOccurs="0" maxOccurs="unbounded" type="NLog.Layouts.JsonAttribute" />
          <xs:element name="excludeEmptyProperties" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="excludeProperties" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="includeAllProperties" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeGdc" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeMdc" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeMdlc" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="maxRecursionLimit" minOccurs="0" maxOccurs="1" type="xs:integer" />
        </xs:choice>
        <xs:attribute name="escapeForwardSlash" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Should forward slashes be escaped? If true, / will be converted to \/</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="renderEmptyObject" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Option to render the empty object value {}</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="suppressSpaces" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Option to suppress the extra spaces in the output json</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="excludeEmptyProperties" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Option to exclude null/empty properties from the log event (as JSON)</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="excludeProperties" type="xs:string">
          <xs:annotation>
            <xs:documentation>List of property names to exclude when  is true</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeAllProperties" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Option to include all properties from the log event (as JSON)</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeGdc" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include contents of the  dictionary.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeMdc" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include contents of the  dictionary.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeMdlc" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include contents of the  dictionary.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxRecursionLimit" type="xs:integer">
          <xs:annotation>
            <xs:documentation>How far should the JSON serializer follow object references before backing off</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="NLog.Layouts.JsonAttribute">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
      <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
      <xs:element name="encode" minOccurs="0" maxOccurs="1" type="xs:boolean" />
      <xs:element name="escapeForwardSlash" minOccurs="0" maxOccurs="1" type="xs:boolean" />
      <xs:element name="escapeUnicode" minOccurs="0" maxOccurs="1" type="xs:boolean" />
      <xs:element name="includeEmptyValue" minOccurs="0" maxOccurs="1" type="xs:boolean" />
    </xs:choice>
    <xs:attribute name="layout" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Layout that will be rendered as the attribute's value.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="name" type="xs:string">
      <xs:annotation>
        <xs:documentation>Name of the attribute.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="encode" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Determines whether or not this attribute will be Json encoded.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="escapeForwardSlash" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Should forward slashes be escaped? If true, / will be converted to \/</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="escapeUnicode" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Indicates whether to escape non-ascii characters</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="includeEmptyValue" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Whether an attribute with empty value should be included in the output</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="LayoutWithHeaderAndFooter">
    <xs:complexContent>
      <xs:extension base="Layout">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="footer" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="header" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
        </xs:choice>
        <xs:attribute name="footer" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Footer layout.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="header" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Header layout.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Body layout (can be repeated multiple times).</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="Log4JXmlEventLayout">
    <xs:complexContent>
      <xs:extension base="Layout">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="includeAllProperties" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeCallSite" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeMdc" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeMdlc" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeNdc" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeNdlc" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeSourceInfo" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="parameter" minOccurs="0" maxOccurs="unbounded" type="NLog.Targets.NLogViewerParameterInfo" />
        </xs:choice>
        <xs:attribute name="includeAllProperties" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Option to include all properties from the log events</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeCallSite" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include call site (class and method name) in the information sent over the network.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeMdc" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include contents of the  dictionary.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeMdlc" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include contents of the  dictionary.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeNdc" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include contents of the  stack.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeNdlc" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include contents of the  stack.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeSourceInfo" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include source info (file name and line number) in the information sent over the network.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="SimpleLayout">
    <xs:complexContent>
      <xs:extension base="Layout">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="text" minOccurs="0" maxOccurs="1" type="xs:string" />
        </xs:choice>
        <xs:attribute name="text" type="xs:string">
          <xs:annotation>
            <xs:documentation>Layout text.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="XmlLayout">
    <xs:complexContent>
      <xs:extension base="Layout">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="excludeProperties" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="includeAllProperties" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeMdc" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="includeMdlc" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="maxRecursionLimit" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="propertiesCollectionItemName" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="propertiesElementKeyAttribute" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="propertiesElementName" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="propertiesElementValueAttribute" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="attribute" minOccurs="0" maxOccurs="unbounded" type="NLog.Layouts.XmlAttribute" />
          <xs:element name="elementName" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="element" minOccurs="0" maxOccurs="unbounded" type="NLog.Layouts.XmlElement" />
          <xs:element name="elementValue" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="includeEmptyValue" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="indentXml" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="elementEncode" minOccurs="0" maxOccurs="1" type="xs:boolean" />
        </xs:choice>
        <xs:attribute name="excludeProperties" type="xs:string">
          <xs:annotation>
            <xs:documentation>List of property names to exclude when  is true</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeAllProperties" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Option to include all properties from the log event (as XML)</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeMdc" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include contents of the  dictionary.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeMdlc" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to include contents of the  dictionary.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxRecursionLimit" type="xs:integer">
          <xs:annotation>
            <xs:documentation>How far should the XML serializer follow object references before backing off</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="propertiesCollectionItemName" type="xs:string">
          <xs:annotation>
            <xs:documentation>XML element name to use for rendering IList-collections items</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="propertiesElementKeyAttribute" type="xs:string">
          <xs:annotation>
            <xs:documentation>XML attribute name to use when rendering property-key When null (or empty) then key-attribute is not included</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="propertiesElementName" type="xs:string">
          <xs:annotation>
            <xs:documentation>XML element name to use when rendering properties</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="propertiesElementValueAttribute" type="xs:string">
          <xs:annotation>
            <xs:documentation>XML attribute name to use when rendering property-value When null (or empty) then value-attribute is not included and value is formatted as XML-element-value</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="elementName" type="xs:string">
          <xs:annotation>
            <xs:documentation>Name of the root XML element</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="elementValue" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Value inside the root XML element</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeEmptyValue" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Whether a ElementValue with empty value should be included in the output</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="indentXml" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Auto indent and create new lines</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="elementEncode" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Determines whether or not this attribute will be Xml encoded.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="NLog.Layouts.XmlAttribute">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
      <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
      <xs:element name="encode" minOccurs="0" maxOccurs="1" type="xs:boolean" />
      <xs:element name="includeEmptyValue" minOccurs="0" maxOccurs="1" type="xs:boolean" />
    </xs:choice>
    <xs:attribute name="layout" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Layout that will be rendered as the attribute's value.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="name" type="xs:string">
      <xs:annotation>
        <xs:documentation>Name of the attribute.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="encode" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Determines whether or not this attribute will be Xml encoded.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="includeEmptyValue" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Whether an attribute with empty value should be included in the output</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="NLog.Layouts.XmlElement">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element name="encode" minOccurs="0" maxOccurs="1" type="xs:boolean" />
      <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:string" />
      <xs:element name="value" minOccurs="0" maxOccurs="1" type="Layout" />
      <xs:element name="attribute" minOccurs="0" maxOccurs="unbounded" type="NLog.Layouts.XmlAttribute" />
      <xs:element name="element" minOccurs="0" maxOccurs="unbounded" type="NLog.Layouts.XmlElement" />
      <xs:element name="includeEmptyValue" minOccurs="0" maxOccurs="1" type="xs:boolean" />
      <xs:element name="indentXml" minOccurs="0" maxOccurs="1" type="xs:boolean" />
      <xs:element name="excludeProperties" minOccurs="0" maxOccurs="1" type="xs:string" />
      <xs:element name="includeAllProperties" minOccurs="0" maxOccurs="1" type="xs:boolean" />
      <xs:element name="includeMdc" minOccurs="0" maxOccurs="1" type="xs:boolean" />
      <xs:element name="includeMdlc" minOccurs="0" maxOccurs="1" type="xs:boolean" />
      <xs:element name="maxRecursionLimit" minOccurs="0" maxOccurs="1" type="xs:integer" />
      <xs:element name="propertiesCollectionItemName" minOccurs="0" maxOccurs="1" type="xs:string" />
      <xs:element name="propertiesElementKeyAttribute" minOccurs="0" maxOccurs="1" type="xs:string" />
      <xs:element name="propertiesElementName" minOccurs="0" maxOccurs="1" type="xs:string" />
      <xs:element name="propertiesElementValueAttribute" minOccurs="0" maxOccurs="1" type="xs:string" />
    </xs:choice>
    <xs:attribute name="encode" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Determines whether or not this attribute will be Xml encoded.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="name" type="xs:string">
      <xs:annotation>
        <xs:documentation>Name of the element</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="value" type="SimpleLayoutAttribute">
      <xs:annotation>
        <xs:documentation>Value inside the element</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="includeEmptyValue" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Whether a ElementValue with empty value should be included in the output</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="indentXml" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Auto indent and create new lines</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="excludeProperties" type="xs:string">
      <xs:annotation>
        <xs:documentation>List of property names to exclude when  is true</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="includeAllProperties" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Option to include all properties from the log event (as XML)</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="includeMdc" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Indicates whether to include contents of the  dictionary.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="includeMdlc" type="xs:boolean">
      <xs:annotation>
        <xs:documentation>Indicates whether to include contents of the  dictionary.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="maxRecursionLimit" type="xs:integer">
      <xs:annotation>
        <xs:documentation>How far should the XML serializer follow object references before backing off</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="propertiesCollectionItemName" type="xs:string">
      <xs:annotation>
        <xs:documentation>XML element name to use for rendering IList-collections items</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="propertiesElementKeyAttribute" type="xs:string">
      <xs:annotation>
        <xs:documentation>XML attribute name to use when rendering property-key When null (or empty) then key-attribute is not included</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="propertiesElementName" type="xs:string">
      <xs:annotation>
        <xs:documentation>XML element name to use when rendering properties</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="propertiesElementValueAttribute" type="xs:string">
      <xs:annotation>
        <xs:documentation>XML attribute name to use when rendering property-value When null (or empty) then value-attribute is not included and value is formatted as XML-element-value</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="when">
    <xs:complexContent>
      <xs:extension base="Filter">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="action" minOccurs="0" maxOccurs="1" type="FilterResult" />
          <xs:element name="condition" minOccurs="0" maxOccurs="1" type="Condition" />
        </xs:choice>
        <xs:attribute name="action" type="FilterResult">
          <xs:annotation>
            <xs:documentation>Action to be taken when filter matches.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="condition" type="Condition">
          <xs:annotation>
            <xs:documentation>Condition expression.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="FilterResult">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Neutral" />
      <xs:enumeration value="Log" />
      <xs:enumeration value="Ignore" />
      <xs:enumeration value="LogFinal" />
      <xs:enumeration value="IgnoreFinal" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="whenContains">
    <xs:complexContent>
      <xs:extension base="Filter">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="action" minOccurs="0" maxOccurs="1" type="FilterResult" />
          <xs:element name="ignoreCase" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="substring" minOccurs="0" maxOccurs="1" type="xs:string" />
        </xs:choice>
        <xs:attribute name="action" type="FilterResult">
          <xs:annotation>
            <xs:documentation>Action to be taken when filter matches.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="ignoreCase" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to ignore case when comparing strings.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout to be used to filter log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="substring" type="xs:string">
          <xs:annotation>
            <xs:documentation>Substring to be matched.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="whenEqual">
    <xs:complexContent>
      <xs:extension base="Filter">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="action" minOccurs="0" maxOccurs="1" type="FilterResult" />
          <xs:element name="compareTo" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="ignoreCase" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
        </xs:choice>
        <xs:attribute name="action" type="FilterResult">
          <xs:annotation>
            <xs:documentation>Action to be taken when filter matches.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="compareTo" type="xs:string">
          <xs:annotation>
            <xs:documentation>String to compare the layout to.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="ignoreCase" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to ignore case when comparing strings.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout to be used to filter log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="whenNotContains">
    <xs:complexContent>
      <xs:extension base="Filter">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="action" minOccurs="0" maxOccurs="1" type="FilterResult" />
          <xs:element name="ignoreCase" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="substring" minOccurs="0" maxOccurs="1" type="xs:string" />
        </xs:choice>
        <xs:attribute name="action" type="FilterResult">
          <xs:annotation>
            <xs:documentation>Action to be taken when filter matches.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="ignoreCase" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to ignore case when comparing strings.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout to be used to filter log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="substring" type="xs:string">
          <xs:annotation>
            <xs:documentation>Substring to be matched.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="whenNotEqual">
    <xs:complexContent>
      <xs:extension base="Filter">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="action" minOccurs="0" maxOccurs="1" type="FilterResult" />
          <xs:element name="compareTo" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="ignoreCase" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
        </xs:choice>
        <xs:attribute name="action" type="FilterResult">
          <xs:annotation>
            <xs:documentation>Action to be taken when filter matches.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="compareTo" type="xs:string">
          <xs:annotation>
            <xs:documentation>String to compare the layout to.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="ignoreCase" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Indicates whether to ignore case when comparing strings.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout to be used to filter log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="whenRepeated">
    <xs:complexContent>
      <xs:extension base="Filter">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
          <xs:element name="action" minOccurs="0" maxOccurs="1" type="FilterResult" />
          <xs:element name="defaultFilterCacheSize" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="includeFirst" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="layout" minOccurs="0" maxOccurs="1" type="Layout" />
          <xs:element name="maxFilterCacheSize" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="maxLength" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="timeoutSeconds" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="optimizeBufferDefaultLength" minOccurs="0" maxOccurs="1" type="xs:integer" />
          <xs:element name="optimizeBufferReuse" minOccurs="0" maxOccurs="1" type="xs:boolean" />
          <xs:element name="filterCountMessageAppendFormat" minOccurs="0" maxOccurs="1" type="xs:string" />
          <xs:element name="filterCountPropertyName" minOccurs="0" maxOccurs="1" type="xs:string" />
        </xs:choice>
        <xs:attribute name="action" type="FilterResult">
          <xs:annotation>
            <xs:documentation>Action to be taken when filter matches.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="defaultFilterCacheSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Default number of unique filter values to expect, will automatically increase if needed</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="includeFirst" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Applies the configured action to the initial logevent that starts the timeout period. Used to configure that it should ignore all events until timeout.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="layout" type="SimpleLayoutAttribute">
          <xs:annotation>
            <xs:documentation>Layout to be used to filter log messages.</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxFilterCacheSize" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Max number of unique filter values to expect simultaneously</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="maxLength" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Max length of filter values, will truncate if above limit</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="timeoutSeconds" type="xs:integer">
          <xs:annotation>
            <xs:documentation>How long before a filter expires, and logging is accepted again</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferDefaultLength" type="xs:integer">
          <xs:annotation>
            <xs:documentation>Default buffer size for the internal buffers</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="optimizeBufferReuse" type="xs:boolean">
          <xs:annotation>
            <xs:documentation>Reuse internal buffers, and doesn't have to constantly allocate new buffers</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="filterCountMessageAppendFormat" type="xs:string">
          <xs:annotation>
            <xs:documentation>Append FilterCount to the  when an event is no longer filtered</xs:documentation>
          </xs:annotation>
        </xs:attribute>
        <xs:attribute name="filterCountPropertyName" type="xs:string">
          <xs:annotation>
            <xs:documentation>Insert FilterCount value into  when an event is no longer filtered</xs:documentation>
          </xs:annotation>
        </xs:attribute>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="AccurateLocal">
    <xs:complexContent>
      <xs:extension base="TimeSource">
        <xs:choice minOccurs="0" maxOccurs="unbounded" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="AccurateUTC">
    <xs:complexContent>
      <xs:extension base="TimeSource">
        <xs:choice minOccurs="0" maxOccurs="unbounded" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="FastLocal">
    <xs:complexContent>
      <xs:extension base="TimeSource">
        <xs:choice minOccurs="0" maxOccurs="unbounded" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="FastUTC">
    <xs:complexContent>
      <xs:extension base="TimeSource">
        <xs:choice minOccurs="0" maxOccurs="unbounded" />
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
</xs:schema>