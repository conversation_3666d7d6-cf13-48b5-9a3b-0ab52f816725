﻿using Common.DAL;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace QMD.DAL.Table
{
    /// <summary>
    /// 供应商和用户的关系表
    /// </summary>
    [Index(nameof(ContractorID), IsUnique = false)]
    public class RelContractorUser : EntityBase
    {
        [MaxLength(32)]
        [Required]
        public string ContractorID { get; set; }
        [MaxLength(128)]
        [Required]
        public string UserEmail { get; set; }
        [MaxLength(128)]
        public string UserName { get; set; }
        /// <summary>
        /// 操作组人员类型
        /// </summary>
        public EnumContractorUserType UserType { get; set; }
    }
    public enum EnumContractorUserType
    {
        Operator = 1, //操作人
        Viewer = 2    //查看人
    }
}
