﻿using Hub.Mobile.ApiClient.Hub;
using System;
using System.Threading.Tasks;
using WebApiClient;
using WebApiClient.Attributes;
using WebApiClient.Parameterables;

namespace Hub.Mobile.ApiClient
{
    public interface IHubApiClient : IHttpApi
    {
        [HttpGet("Client/GetAppDownloadUrl")]
        Task<GetAppDownloadUrlResponse> GetAppDownloadUrlResponse([PathQuery] string fileName);
        [HttpPost("Client/UploadLog")]
        Task<BaseResponse> UploadLog(MulitpartFile file, [MulitpartContent] LogUploadRequest requst);
        [HttpGet("App/GetAllApps")]
        Task<GetAllAppsResponse> GetApps();
        [HttpGet("App/CheckLocalVersion")]
        Task<GetAppVersionResponse> GetAppVersion([PathQuery] string appCode);
    }
}
