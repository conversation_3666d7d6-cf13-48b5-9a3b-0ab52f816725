﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>netstandard2.0</TargetFramework>
		<ProduceReferenceAssembly>true</ProduceReferenceAssembly>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<DebugType>portable</DebugType>
		<DebugSymbols>true</DebugSymbols>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Acr.UserDialogs" Version="7.2.0.564" />
		<PackageReference Include="dotMorten.Xamarin.Forms.AutoSuggestBox" Version="1.1.1" />
		<PackageReference Include="FluentValidation" Version="11.0.2" />
		<PackageReference Include="Rg.Plugins.Popup" Version="2.1.0" />
		<PackageReference Include="Xamarin.Forms" Version="5.0.0.2401" />
		<PackageReference Include="Xamarin.Essentials" Version="1.6.1" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Hub.Mobile.ApiClient\Hub.Mobile.ApiClient.csproj" />
		<ProjectReference Include="..\Hub.Mobile.CommonControl\Hub.Mobile.CommonControl.csproj" />
		<ProjectReference Include="..\Hub.Mobile.Const\Hub.Mobile.Const.csproj" />
		<ProjectReference Include="..\Hub.Mobile.Converter\Hub.Mobile.Converter.csproj" />
		<ProjectReference Include="..\Hub.Mobile.DAL\Hub.Mobile.DAL.csproj" />
		<ProjectReference Include="..\Hub.Mobile.Interface\Hub.Mobile.Interface.csproj" />
		<ProjectReference Include="..\Hub.Mobile.Model\Hub.Mobile.Model.csproj" />
		<ProjectReference Include="..\Hub.Mobile.Utility\Hub.Mobile.Utility.csproj" />
	</ItemGroup>
	<ItemGroup>
		<Compile Update="Resources\ExceptionResources.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>ExceptionResources.resx</DependentUpon>
		</Compile>
		<Compile Update="Resources\UIResources.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>UIResources.resx</DependentUpon>
		</Compile>
	</ItemGroup>
	<ItemGroup>
		<EmbeddedResource Update="TestPage.xaml">
			<Generator>MSBuild:UpdateDesignTimeXaml</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Views\ChangePasswordPage.xaml">
			<Generator>MSBuild:UpdateDesignTimeXaml</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Views\HybridAppUpgrade.xaml">
			<Generator>MSBuild:UpdateDesignTimeXaml</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Views\LoginPage.xaml">
			<Generator>MSBuild:UpdateDesignTimeXaml</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Views\PersonalInformationPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Views\SettingPage.xaml">
			<Generator>MSBuild:UpdateDesignTimeXaml</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Views\SwitchLanguagePopupPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Views\SynchronizeBasicDataPopupPage.xaml">
			<Generator>MSBuild:UpdateDesignTimeXaml</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Views\UpgradePage.xaml">
			<Generator>MSBuild:UpdateDesignTimeXaml</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Views\UploadLogPage.xaml">
			<Generator>MSBuild:UpdateDesignTimeXaml</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Resources\ExceptionResources.resx">
			<Generator>ResXFileCodeGenerator</Generator>
			<LastGenOutput>ExceptionResources.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="Resources\UIResources.resx">
			<Generator>ResXFileCodeGenerator</Generator>
			<LastGenOutput>UIResources.Designer.cs</LastGenOutput>
		</EmbeddedResource>
	</ItemGroup>
</Project>