﻿using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Views;
using Android.Widget;
using Hub.Mobile.Const;
using Hub.Mobile.Interface;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using Xamarin.Forms;
[assembly:Dependency(typeof(Hub.Mobile.Droid.Imps.FileService))]
namespace Hub.Mobile.Droid.Imps
{
    public class FileService : IFile
    {
        public string SaveToLocal(string path)
        {
            string fileName = Path.GetFileName(path);
            string fileShortName = Path.GetFileNameWithoutExtension(path);
            string extension = Path.GetExtension(path);
            string saveToLocalDir = GetSaveToLocalDir();
            if (!Directory.Exists(saveToLocalDir))
            {
                Directory.CreateDirectory(saveToLocalDir);
            }
            string newFileFullName = Path.Combine(saveToLocalDir, fileName);
            int number = 1;
            while (File.Exists(newFileFullName))
            {
                newFileFullName = Path.Combine(saveToLocalDir, $"{fileShortName}_{number}{extension}");
                number++;
            }
            File.Copy(path, newFileFullName);
            return newFileFullName;
        }
        public string GetSaveToLocalDir()
        {
            //return Path.Combine(DependencyService.Get<IPathService>().GetDirectoryDownloads(), MobileCommonConsts.DownloadFileRootDir);
            return DependencyService.Get<IPathService>().GetDirectoryDownloads();
        }
    }
}