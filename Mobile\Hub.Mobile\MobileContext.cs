﻿using Acr.UserDialogs;
using Hub.Mobile.Interface;
using Hub.Mobile.Resources;
using Hub.Mobile.Services;
using Hub.Mobile.Views;
using Rg.Plugins.Popup.Services;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Xamarin.Forms;

namespace Hub.Mobile
{
    public static class MobileContext
    {
        static NLog.Logger log = NLog.LogManager.GetCurrentClassLogger();
        public static void InitAsync()
        {
            Task.Factory.StartNew(async () =>
            {
                try
                {
                    #region 升级检测
                    var checkResult = await Upgrade.Check();
                    if (checkResult.Success && checkResult.NeedUpgrade)
                    {
                        if (await UserDialogs.Instance.ConfirmAsync(UIResources.UpgradeConfirmMsg, okText: UIResources.Ok, cancelText: UIResources.Cancel))
                        {
                            await PopupNavigation.Instance.PushAsync(new UpgradePage(checkResult.FileName));
                        }
                        else
                        {
                            if (await DependencyService.Get<IAccountService>().SubmitCheckAsync(true, false))
                            {
                                await DependencyService.Get<SynchronizeDataService>().SynchronizeData();
                            }
                        }
                    }
                    else
                    {
                        if (await DependencyService.Get<IAccountService>().SubmitCheckAsync(true, false))
                        {
                            await DependencyService.Get<SynchronizeDataService>().SynchronizeData();
                        }
                    }
                    #endregion
                }
                catch (Exception ex)
                {
                    log.Error(ex, ex.Message);
                }
            }
            );
        }
    }
}
