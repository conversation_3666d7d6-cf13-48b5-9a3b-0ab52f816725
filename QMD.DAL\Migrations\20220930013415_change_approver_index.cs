﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class change_approver_index : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Approvers_UserEmail_AllowLevel",
                table: "Approvers");

            migrationBuilder.CreateIndex(
                name: "IX_Approvers_UserEmail_AllowLevel_ProjectID",
                table: "Approvers",
                columns: new[] { "UserEmail", "AllowLevel", "ProjectID" },
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Approvers_UserEmail_AllowLevel_ProjectID",
                table: "Approvers");

            migrationBuilder.CreateIndex(
                name: "IX_Approvers_UserEmail_AllowLevel",
                table: "Approvers",
                columns: new[] { "UserEmail", "AllowLevel" },
                unique: true);
        }
    }
}
