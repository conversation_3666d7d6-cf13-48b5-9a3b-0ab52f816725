﻿using Common.DAL;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace QMD.DAL.Table
{
    /// <summary>
    /// 任务类型和供应商的关系表
    /// </summary>
    [Index(nameof(ContractorID), nameof(TaskItemID), nameof(TplID), IsUnique = false)]
    public class RelContractorTaskItemTpl : EntityBase
    {
        [MaxLength(32)]
        [Required]
        public string ContractorID { get; set; }
        [MaxLength(32)]
        [Required]
        public string TaskItemID { get; set; }
        [Required]
        [MaxLength(32)]
        public string TplID { get; set; }
    }
}
