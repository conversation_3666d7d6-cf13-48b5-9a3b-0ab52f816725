﻿using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using System.IO;
using Newtonsoft.Json;
using System.ComponentModel;

namespace QMD.Env
{
    public static class ConfigEnvValues
    {
        private static IConfiguration _config;
        private static IConfiguration _dataConfig;
        public static void Init(IConfiguration configure, IConfiguration dataConfigure = null)
        {
            _config = configure;
            _dataConfig = dataConfigure;
        }

        public static string UserResourceServerBaseUrl { get { return _config["UserResourceServerBaseUrl"]; } }

        public static string DomainURL { get { return _config["DomainURL"]; } }
        public static string CDNRootUrl { get { return _config["CDNRootURL"]; } }
        public static string CDNProjectName { get { return _config["CDNProjectName"]; } }
        public static string CDNPhysicalFolderPath { get { return _config["CDNPhysicalFolderPath"]; } }
        public static string CDNStepValuesFolderName { get { return _config["CDNStepValuesFolderName"]; } }
        public static string CDNStepSamplesFolderName { get { return _config["CDNStepSamplesFolderName"]; } }
        public static string CDNStepValuesThumbFolderName { get { return _config["CDNStepValuesThumbFolderName"]; } }
        public static string CDNStepSamplesThumbFolderName { get { return _config["CDNStepSamplesThumbFolderName"]; } }
        public static string CDNExecuteTaskAttachmentName { get { return _config["CDNExecuteTaskAttachmentName"]; } }
        public static string CDNStepValuesFolderFullPath { get { return Path.Combine(CDNPhysicalFolderPath, CDNStepValuesFolderName); } }
        public static string CDNStepSamplesFolderFullPath { get { return Path.Combine(CDNPhysicalFolderPath, CDNStepSamplesFolderName); } }
        public static string CDNStepValuesThumbFolderFullPath { get { return Path.Combine(CDNPhysicalFolderPath, CDNStepValuesThumbFolderName); } }
        public static string CDNStepSamplesThumbFolderFullPath { get { return Path.Combine(CDNPhysicalFolderPath, CDNStepSamplesThumbFolderName); } }
        public static List<string> MilestoneOptions
        {
            get
            {
                var list = new List<string>();
                _config.GetSection("MilestoneOptions").Bind(list);
                return list;
            }
        }

        public static ImportConfig ImportConfig
        {
            get
            {
                var config = new ImportConfig();
                _config.GetSection("ImportConfig").Bind(config);
                return config;
            }
        }

        public static ExportConfig ExportConfig
        {
            get
            {
                var config = new ExportConfig();
                _config.GetSection("ExportConfig").Bind(config);
                if (!string.IsNullOrEmpty(config.FolderName))
                {
                    config.FolderFullPath = Path.Combine(CDNPhysicalFolderPath, config.FolderName);
                    if (!Directory.Exists(config.FolderFullPath))
                    {
                        Directory.CreateDirectory(config.FolderFullPath);
                    }
                }
                return config;
            }
        }

        public static ReportConfig ReportConfig
        {
            get
            {
                var config = new ReportConfig();
                _config.GetSection("ReportConfig").Bind(config);
                if (!string.IsNullOrEmpty(config.FolderName))
                {
                    config.FolderFullPath = Path.Combine(CDNPhysicalFolderPath, config.FolderName);
                    if (!Directory.Exists(config.FolderFullPath))
                    {
                        Directory.CreateDirectory(config.FolderFullPath);
                    }
                }
                return config;
            }
        }

        public static SimplingDefaultRate SimplingDefaultRate
        {
            get
            {
                var config = new SimplingDefaultRate();
                _config.GetSection("SimplingDefaultRate").Bind(config);
                return config;
            }
        }

        #region 第三方数据库信息
        /// <summary>
        /// ITR Oracle数据库dblink
        /// </summary>
        public static string OraItrDblink
        {
            get
            {
                string result = _config["ThirdPartyDatabases:OraItrDblink"];
                if (string.IsNullOrEmpty(result))
                {
                    result = "User ID=fiberhome;Password=Fiberhome$18736;Data Source=(DESCRIPTION = (ADDRESS_LIST= (ADDRESS = (PROTOCOL = TCP)(HOST = **************)(PORT = 11521))) (CONNECT_DATA = (SID = ORCL)))";
                }
                return result;
            }
        }
        #endregion

        #region 默认固定数据配置
        /// <summary>
        /// 默认服务线ID
        /// </summary>
        public static string ConstServiceLineID { get { return _config["ConstDefaultValues:ServiceLineID"]; } }
        /// <summary>
        /// 默认项目ID
        /// </summary>
        public static string ConstProjectID { get { return _config["ConstDefaultValues:ProjectID"]; } }
        /// <summary>
        /// 默认任务模板ID
        /// </summary>
        public static string ConstTplID { get { return _config["ConstDefaultValues:TplID"]; } }
        /// <summary>
        /// 同步ITR工单几天内的数据
        /// </summary>
        public static int ConstSyncItrOrderDays
        {
            get
            {
                int result = 7;
                if (!int.TryParse(_config["ConstDefaultValues:SyncItrOrderDays"], out result))
                {
                    result = 7;
                }
                return result;
            }
        }

        /// <summary>
        /// ITR获取QA跳转过来的连接的时效，单位（分钟）
        /// </summary>
        public static int LinkEffectTime
        {
            get
            {
                int result = 5;
                if (!int.TryParse(_config["ConstDefaultValues:LinkEffectTime"], out result))
                {
                    result = 5;
                }
                return result;
            }
        }

        /// <summary>
        /// 设置是否根据上传状态来判断QMD工单是否关闭，如果为false，则根据工单一级审批工序数量等于总共必填工序数量来判断关闭
        /// </summary>
        public static bool UseSubmitStatus
        {
            get
            {
                bool result = true;
                if (!bool.TryParse(_config["ConstDefaultValues:UseSubmitStatus"], out result))
                {
                    result = false;
                }
                return result;
            }
        }

        /// <summary>
        /// 是否允许QA项目审批
        /// </summary>
        public static bool AllowQaApproval
        {
            get
            {
                bool result = false;
                if (!bool.TryParse(_config["ConstDefaultValues:AllowQaApproval"], out result))
                {
                    result = false;
                }
                return result;
            }
        }

        /// <summary>
        /// ITR工单实施对应的项目ID
        /// </summary>
        public static string ITRProjectId
        {
            get
            {
                string result = string.Empty;
                try
                {
                    result = _config["ConstDefaultValues:ITRProjectId"];
                }
                catch
                {

                }
                return result;
            }
        }

        /// <summary>
        /// 是否发送邮件
        /// </summary>
        public static bool AllowSendMail
        {
            get
            {
                bool result = false;
                try
                {
                    if (!bool.TryParse(_config["ConstDefaultValues:AllowSendMail"], out result))
                    {
                        result = false;
                    }
                }
                catch 
                {
                }
                return result;
            }
        }

        public static List<string> SendMails
        {
            get
            {
                return new List<string> { "<EMAIL>" };
                //return new List<string>() {  "<EMAIL>", "<EMAIL>" };//"<EMAIL>", "<EMAIL>",
            }
        }

        /// <summary>
        /// 钉钉额外执行人unionID，用于监控待办信息是否发送成功
        /// </summary>
        public static List<string> DingtalkTodoExutorIds
        {
            get
            {
                //彭卓unionID（pFt0D85MKk8gpHz0X5xUJQiEiE），吴君unionID(vBqgLCelhTBmO1wyGSspiPwiEiE)，孙unionId(TMRrL2hmnAUiiGcj1uhC79QiEiE)
                return new List<string>() { "vBqgLCelhTBmO1wyGSspiPwiEiE" };
            }
        }

        ///// <summary>
        ///// 试点技服中心
        ///// </summary>
        //public static List<string> TestServiceCenters
        //{
        //    get
        //    {
        //        //TestServiceCenters
        //        return new List<string>() { };
        //    }
        //}

        /// <summary>
        /// 是否试点
        /// </summary>
        public static bool IsTestSite
        {
            get
            {
                bool result = true;
                try
                {
                    if (!bool.TryParse(_config["ConstDefaultValues:IsTestSite"], out result))
                    {
                        result = false;
                    }
                }
                catch
                {
                    result = true;
                }
                return result;
            }
        }

        /// <summary>
        /// 试点地市
        /// </summary>
        public static List<string> TestCities
        {
            get
            {
                List<string> result = new List<string>();
                try
                {
                    result = _config["ConstDefaultValues:TestCities"].Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries).ToList();
                }
                catch
                {
                }
                return result;
            }
        }


        /// <summary>
        /// QA质量评估实施对应的项目ID
        /// </summary>
        public static string QAProjectId
        {
            get
            {
                string result = string.Empty;
                try
                {
                    result = _config["ConstDefaultValues:QAProjectId"];
                }
                catch 
                {

                }
                return result;
            }
        }

        /// <summary>
        /// 重要的对象级别ID,配置文件中逗号隔开
        /// </summary>
        public static List<string> ImportantObjTypeLevelId
        {
            get
            {
                List<string> result = new List<string>();
                try
                {
                    result = _config["ConstDefaultValues:ImportantObjTypeLevelId"].Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries).ToList();
                }
                catch
                {

                }
                return result;
            }
        }


        /// <summary>
        /// 重要的对象级别ID,配置文件中逗号隔开
        /// </summary>
        public static string ImportantObjTypeLevelId_1
        {
            get
            {
                string result = string.Empty;
                try
                {
                    result = _config["ConstDefaultValues:ImportantObjTypeLevelId"];
                }
                catch 
                {

                }
                return result;
            }
        }


        /// <summary>
        /// QA的三大运营商
        /// </summary>
        public static List<string> ThreeQaOperators
        {
            get
            {
                List<string> result = new List<string>();
                try
                {
                    result = _config["ConstDefaultValues:ThreeQaOperators"].Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries).ToList();
                }
                catch 
                {
                    result = new List<string>() { "中国移动", "中国电信", "中国联通", "移动", "联通", "电信" };
                }
                return result;
            }
        }

        /// <summary>
        /// 集团技服中心
        /// </summary>
        public static List<string> GroupDepartments
        {
            get
            {
                List<string> result = new List<string>();
                try
                {
                    result = _config["ConstDefaultValues:GroupDepartments"].Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries).ToList();
                }
                catch 
                {
                    result = new List<string>() { "电信集团技术服务中心", "移动集团技术服务中心", "联通集团技术服务中心", "北京技术服务中心" };
                }
                return result;
            }
        }

        /// <summary>
        /// QA看板巡检工单搜索条件：整改期限
        /// </summary>
        public static List<string> QaDeadLines
        {
            get
            {
                List<string> result = new List<string>();
                try
                {
                    //result = _config["ConstDefaultValues:QaDeadLines"].Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries).ToList();
                    result = new List<string>() { "1天","2天","3天","4天","5天","6天","7天","8天","9天","10天","11天","12天","13天","14天","15天","16天","17天","18天","19天","20天","21天","22天","23天","24天","25天","26天","27天","28天","29天","30天",
                        "31天","32天","33天","34天","35天","36天","37天","38天","39天","40天","41天","42天","43天","44天","45天","46天","47天","48天","49天","50天","51天","52天","53天","54天","55天","56天","57天","58天","59天","60天","61天","62天","63天",
                        "64天","65天","66天","67天","68天","69天","70天","71天","72天","73天","74天","75天","76天","77天","78天","79天","80天","81天","82天","83天","84天","85天","86天","87天","88天","89天","90天","91天","92天","93天","94天","95天","96天",
                        "97天","98天","99天","100天","101天","102天","103天","104天","105天","106天","107天","108天","109天","110天","111天","112天","113天","114天","115天","116天","117天","118天","119天","120天" };
                }
                catch
                {
                    result = new List<string>() { "1天","2天","3天","4天","5天","6天","7天","8天","9天","10天","11天","12天","13天","14天","15天","16天","17天","18天","19天","20天","21天","22天","23天","24天","25天","26天","27天","28天","29天","30天",
                        "31天","32天","33天","34天","35天","36天","37天","38天","39天","40天","41天","42天","43天","44天","45天","46天","47天","48天","49天","50天","51天","52天","53天","54天","55天","56天","57天","58天","59天","60天","61天","62天","63天",
                        "64天","65天","66天","67天","68天","69天","70天","71天","72天","73天","74天","75天","76天","77天","78天","79天","80天","81天","82天","83天","84天","85天","86天","87天","88天","89天","90天","91天","92天","93天","94天","95天","96天",
                        "97天","98天","99天","100天","101天","102天","103天","104天","105天","106天","107天","108天","109天","110天","111天","112天","113天","114天","115天","116天","117天","118天","119天","120天" };
                }
                return result;
            }
        }

        #endregion

        #region Redis 配置
        /// <summary>
        /// RedisConnection链接服务地址
        /// </summary>
        public static string RedisConnection
        {
            get
            {
                string result = string.Empty;
                try
                {
                    result = _config["Redis:Default:Connection"];
                    if (string.IsNullOrEmpty(result))
                    {
                        result = "r-bp1ankfzek8dtj1qk4.redis.rds.aliyuncs.com:6379,password=Nmosp903904_";
                    }
                }
                catch (Exception)
                {
                    result = "r-bp1ankfzek8dtj1qk4.redis.rds.aliyuncs.com:6379,password=Nmosp903904_";
                }
                return result;
            }
        }
        public static int RedisDbIndex
        {
            get
            {
                int result = 0;
                try
                {
                    result = int.Parse(_config["Redis:Default:DefaultDB"]);
                }
                catch (Exception)
                {
                    
                }
                return result;
            }
        }
        #endregion

        #region Dify配置
        public static string DataSetApiKey
        {
            get
            {
                string result = string.Empty;
                try
                {
                    result = _config["Dify:DataSetApiKey"];
                }
                catch (Exception)
                {
                }
                return result;
            }
        }
        public static string TechNoticeWorkFlowApiKey
        {
            get
            {
                string result = string.Empty;
                try
                {
                    result = _config["Dify:TechNoticeWorkFlowApiKey"];
                }
                catch (Exception)
                {
                }
                return result;
            }
        }
        public static string DifyBaseUrl
        {
            get
            {
                string result = string.Empty;
                try
                {
                    result = _config["Dify:BaseUrl"];
                }
                catch (Exception)
                {
                }
                return result;
            }
        }
        public static string TechNoticeDataSetName
        {
            get
            {
                string result = string.Empty;
                try
                {
                    result = _config["Dify:TechNoticeDataSetName"];
                }
                catch (Exception)
                {
                }
                return result;
            }
        }
        public static string WorkFlowName
        {
            get
            {
                string result = string.Empty;
                try
                {
                    result = _config["Dify:WorkFlowName"];
                }
                catch (Exception)
                {
                }
                return result;
            }
        }
        #endregion


        /// <summary>
        /// 烽火通配置
        /// </summary>
        public static DingDingConfig FengHuoTongConfig
        {
            get
            {
                var config = new DingDingConfig();
                _config.GetSection("FengHuoTongConfig").Bind(config);
                if (string.IsNullOrEmpty(config.DomainUrl))
                {
                    config.DomainUrl = "https://api.dingtalk.com/";
                }
                return config;
            }
        }


        /// <summary>
        /// 钉钉配置
        /// </summary>
        public static DingDingConfig DingDingConfig
        {
            get
            {
                var config = new DingDingConfig();
                _config.GetSection("DingDingConfig").Bind(config);
                if (string.IsNullOrEmpty(config.DomainUrl))
                {
                    config.DomainUrl = "https://api.dingtalk.com/";
                }
                return config;
            }
        }

        /// <summary>
        /// 文件存储服务器配置
        /// </summary>
        public static MinIOConfig MinIOConfig
        {
            get
            {
                var config = new MinIOConfig();
                _config.GetSection("Minio").Bind(config);
                if (string.IsNullOrEmpty(config.FileURL))
                {
                    config.FileURL = "http://127.0.0.1:9000/testminio";
                }
                return config;
            }
        }

        #region QMOV2 CONFIG

        /// <summary>
        /// 二期配置
        /// </summary>
        public static class QmoV2Cfg
        {
            ///// <summary>
            ///// 派发工单-工单类别
            ///// </summary>
            //public static List<string> DisOrderKinds = new List<string>() { "总部派发", "一线派发","其他" };
            ///// <summary>
            ///// 派发工单-工单类型
            ///// </summary>
            //public static List<string> DisOrderTypes = new List<string>() { "割接", "通知", "风险预警", "网络整改", "网络检查", "改造", "升级", "测试", "重保", "搬迁", "数据变更", "其他", "技术通知" };
            /// <summary>
            /// 派发工单-风险级别
            /// </summary>
            public static List<string> DisRiskLevels = new List<string>() { "高级", "中级", "低级" };

            /// <summary>
            /// 技术通知类型
            /// </summary>
            public static List<string> DisTechNoticeTypes = new List<string> { "A类", "B类", "C类" };

            /// <summary>
            /// 二线部门
            /// </summary>
            public static List<string> SecondLineDepartments = new List<string>() { "TAC中心一部", "TAC中心二部", "TAC中心三部", "服务支撑部", "服务技术部", "技术支持部" };
            /// <summary>
            /// 报备工单-风险级别
            /// </summary>
            public static List<string> RepRiskLevels = new List<string>() { "一级(紧急)", "一级", "二级(紧急)", "二级", "三级(紧急)", "三级", "四级(紧急)", "四级" };

            /// <summary>
            /// 对象名称
            /// </summary>
            public static List<string> RepUnits = new List<string>() { "网络", "站点", "网元", "网管", "单盘", "节点", "端口", "拓扑", "其他" };

            public static List<string> SoftCharaters = new List<string> { "ECN软件包（影响业务）","热补丁（不影响业务）","网管全量/增量补丁","网管大版本" };

            public static List<string> UpgradeReasons = new List<string> { "基线版本", "工程问题解bug", "技术通知要求（非基线）", "客户业务变更" };

            /// <summary>
            /// 报备工单级别工单提前申请时间配置
            /// </summary>
            public static List<RepRiskLevelTimeOffSetConfig> RepTimeOffSetConfig = new List<RepRiskLevelTimeOffSetConfig>()
            {
                new RepRiskLevelTimeOffSetConfig(){ RiskLevel="一级(紧急)", OffSetHours=0 },
                new RepRiskLevelTimeOffSetConfig(){ RiskLevel="一级", OffSetHours=72 },
                new RepRiskLevelTimeOffSetConfig(){ RiskLevel="二级(紧急)", OffSetHours=0 },
                new RepRiskLevelTimeOffSetConfig(){ RiskLevel="二级", OffSetHours=48 },
                new RepRiskLevelTimeOffSetConfig(){ RiskLevel="三级(紧急)", OffSetHours=0 },
                new RepRiskLevelTimeOffSetConfig(){ RiskLevel="三级", OffSetHours=24 },
                new RepRiskLevelTimeOffSetConfig(){ RiskLevel="四级(紧急)", OffSetHours=0 },
                new RepRiskLevelTimeOffSetConfig(){ RiskLevel="四级", OffSetHours=2 }
            };
            /// <summary>
            /// 报备工单模板配置
            /// </summary>
            public static List<QmoEmtModuleConfig> EmtModuleConfigs = new List<QmoEmtModuleConfig>();
            /// <summary>
            /// 风险等级模版列表
            /// </summary>
            public static List<QmoEmtModuleConfig> EmtRiskModuleConfigs = new List<QmoEmtModuleConfig>();
            /// <summary>
            /// 设备导入模版列表
            /// </summary>
            public static List<QmoEmtModuleConfig> EmtDeviceModuleConfigs = new List<QmoEmtModuleConfig>();
            /// <summary>
            /// 实施计划表模版列表
            /// </summary>
            public static List<QmoEmtModuleConfig> EmtTaskModuleConfigs = new List<QmoEmtModuleConfig>();
            /// <summary>
            /// 操作指南列表
            /// </summary>
            public static List<QmoEmtModuleConfig> EmtHelpModuleConfigs = new List<QmoEmtModuleConfig>();

            /// <summary>
            /// 公司涉密文件标识图片
            /// </summary>
            public static string CompanyAuthImage = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Statics", "images", "company-auth.png");

            /// <summary>
            /// 公司涉密文件标识图片-高度
            /// </summary>
            public static short CompanyAuthImageHeight = 100;
            /// <summary>
            /// 二线部门ID（TAC中心）
            /// </summary>
            public static List<long> DingSecLineDeptIds = new List<long> { *********, ********* };
            /// <summary>
            /// 国内销售ID（国内市场总部）
            /// </summary>
            public static List<long> DingSaleInLineDeptIds = new List<long> { *********, ********* };
            /// <summary>
            /// 国际销售ID（武汉烽火国际技术有限责任公司）
            /// </summary>
            public static List<long> DingSaleOutLineDeptIds = new List<long> { ********* };
            /// <summary>
            /// 获取QMO域名
            /// </summary>
            public static string QmoDomainUrl
            {
                get
                {
                    string result = "https://qmo.fiberhome.work/"; 
                    if (IsTestSite)
                    {
                        result = "https://qmo-test.fiberhome.work/";
                    }
                    else
                    {
                        result = "https://qmo.fiberhome.work/";
                    }
                    return result;
                }
            }

            /// <summary>
            /// 公司高管邮箱
            /// </summary>
            public static List<string> CompanyLeaderEmails = new List<string> {
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
             };

            /// <summary>
            /// 产线与专业的关联关系
            /// </summary>
            public static List<ProductSpeOutputLineRelDto> RelProductLineAndProductSpe = new List<ProductSpeOutputLineRelDto> {
                new ProductSpeOutputLineRelDto{ DingDeptId = 839312472, ProductSpe = "OTN" },
                new ProductSpeOutputLineRelDto{ DingDeptId = 839312472, ProductSpe = "MSTP" },
                new ProductSpeOutputLineRelDto{ DingDeptId = 839312472, ProductSpe = "PTN" },
                new ProductSpeOutputLineRelDto{ DingDeptId = 839312472, ProductSpe = "SPN" },
                new ProductSpeOutputLineRelDto{ DingDeptId = 839312472, ProductSpe = "IPRAN" },
                new ProductSpeOutputLineRelDto{ DingDeptId = 839312472, ProductSpe = "STN" },
                new ProductSpeOutputLineRelDto{ DingDeptId = 839312472, ProductSpe = "智网" },
                new ProductSpeOutputLineRelDto{ DingDeptId = 839312472, ProductSpe = "交换机路由器" },
                new ProductSpeOutputLineRelDto{ DingDeptId = 839312472, ProductSpe = "网管" },
                new ProductSpeOutputLineRelDto{ DingDeptId = 839400405, ProductSpe = "接入" },
                new ProductSpeOutputLineRelDto{ DingDeptId = 839400405, ProductSpe = "终端" },
                new ProductSpeOutputLineRelDto{ DingDeptId = 916224589, ProductSpe = "CDN" },
                new ProductSpeOutputLineRelDto{ DingDeptId = 916224589, ProductSpe = "服务器存储" }
            };

            /// <summary>
            /// 预报通知字段显示配置，按照部门定制
            /// </summary>
            public static List<NoticeTaskTodoDto> NoticeTaskTodoConfig
            {
                get
                {
                    var config = new List<NoticeTaskTodoDto>();
                    _dataConfig.GetSection("NoticeTaskTodo").Bind(config);
                    return config;
                }
            }

            /// <summary>
            /// 预报通知字段显示默认配置
            /// </summary>
            public static List<NoticeTaskTodoDetDto> NoticeTaskTodoConfigDefault
            {
                get
                {
                    var config = new List<NoticeTaskTodoDetDto>();
                    _dataConfig.GetSection("NoticeTaskTodoDefault").Bind(config);
                    return config;
                }
            }

            /// <summary>
            /// 通报通知字段显示配置，按照部门定制
            /// </summary>
            public static List<NoticeTaskTodoDto> NoticeTaskResultConfig
            {
                get
                {
                    var config = new List<NoticeTaskTodoDto>();
                    _dataConfig.GetSection("NoticeTaskResult").Bind(config);
                    return config;
                }
            }

            /// <summary>
            /// 通报通知字段显示默认配置
            /// </summary>
            public static List<NoticeTaskTodoDetDto> NoticeTaskResultConfigDefault
            {
                get
                {
                    var config = new List<NoticeTaskTodoDetDto>();
                    _dataConfig.GetSection("NoticeTaskResultDefault").Bind(config);
                    return config;
                }
            }

            /// <summary>
            /// 集团技术服务中心
            /// </summary>
            public static List<string> CompanyDepNames = new List<string> { "电信集团技术服务中心", "联通集团技术服务中心", "移动集团技术服务中心", "行业服务一部", "行业服务二部", "行业服务三部" };

            /// <summary>
            /// 网管专业名称
            /// </summary>
            public static string NmpProductSpe = "网管";

            /// <summary>
            /// 工作表保护密码
            /// </summary>
            public static string ProtectSheetPwd = "Fiberhome@2025";

            /// <summary>
            /// PPIT 的模版ID
            /// </summary>
            public static string PPITTaskTplId = "a3a5002262934f609274720cb6254b5a";

            /// <summary>
            /// QA工单及时处理有效天数（天）
            /// </summary>
            public static int QaValidDateTotal = 90;

            /// <summary>
            /// QA巡检工具配置
            /// </summary>
            public static List<QaAppConfig> QaAppConfig = new List<QaAppConfig> {
                new QaAppConfig{ ProductSpe = "PTN", AppCode = "PTN_XJ",  AppName = "PTN巡检工具", TplIds = new List<string>{"dee29d6005d011eebe5800155d7a9514" }, RelateAppCodes = new List<string>{ "ipran_xj", "ipran_xj_single", "ipran_xj_offline" } },
                new QaAppConfig{ ProductSpe = "PTN",AppCode = "PTN PG", AppName = "PTN评估工具", TplIds = new List<string>{ "ded2a80b05d011eebe5800155d7a9514"},RelateAppCodes = new List<string>{"ptn pg" }},
                new QaAppConfig{ ProductSpe = "OTN",AppCode = "OTN_XJ", AppName = "OTN巡检工具", TplIds = new List<string>{ "dd78b85b05d011eebe5800155d7a9514"},RelateAppCodes = new List<string>{"otn_inspect","otn_inspect_v3" }},
                new QaAppConfig{ ProductSpe = "IPRAN",AppCode = "IPRAN_XJ", AppName = "IPRAN巡检工具", TplIds = new List<string>{"de31c08d05d011eebe5800155d7a9514" },RelateAppCodes = new List<string>{"ipran_xj","ipran_xj_single","ipran_xj_offline","stn_xj" }},
                new QaAppConfig{ ProductSpe = "SPN",AppCode = "SPN_XJ", AppName = "SPN巡检工具", TplIds = new List<string>{ "df12f56705d011eebe5800155d7a9514"},RelateAppCodes = new List<string>{"spn_xj","spn_xj_single","spn_xj_offline","spn_inspect_offline","spn_inspect_single" }},
                new QaAppConfig{ ProductSpe = "PON",AppCode = "OLT_YW", AppName = "OLW运维工具", TplIds = new List<string>{ "ddb68bdf05d011eebe5800155d7a9514"},RelateAppCodes = new List<string>{"olt_yw" }},
                new QaAppConfig{ ProductSpe = "IPRAN,PTN,SPN",AppCode = "PPIT_INSPECT", AppName = "PPIT巡检工具", TplIds = new List<string>{ "a3a5002262934f609274720cb6254b5a"},RelateAppCodes = new List<string>{"ppit_inspect" }}
            };
            /// qa核减人员设置
            /// </summary>
            public static List<string> QaReduceAdminUsers = new List<string> { "<EMAIL>" };
        }
        public static TimeSpan TaskDelayTimeSpan { get { return new TimeSpan(int.Parse(_config["TaskDelayConfirmTimeSpan:Hour"]), int.Parse(_config["TaskDelayConfirmTimeSpan:Minute"]), int.Parse(_config["TaskDelayConfirmTimeSpan:Second"])); }}
        #endregion

        #region 特殊工序（实施任务特殊工序的判断）
        public static SpecialStep SpecialSteps = new() 
        { 
            GroupZhName = "事中",
            StepZhName = "操作实施",
            GroupEnName = "During-Operation",
            StepEnName = "Operation period"
        };
        
        #endregion
    }

    #region 配置对象
    public class SimplingDefaultRate
    {
        public float Level2 { get; set; }
    }

    public class ImportConfig
    {
        public string RelativeTempFolder { get; set; }
    }

    public class ExportConfig
    {
        public string FolderFullPath { get; set; }
        public string FolderName { get; set; }
    }
    public class ReportConfig
    {
        public string FolderFullPath { get; set; }
        public string FolderName { get; set; }
    }

    /// <summary>
    /// 钉钉配置
    /// </summary>
    public class DingDingConfig
    {
        /// <summary>
        /// 钉钉开放平台接口地址，结尾带/，例如：https://api.dingtalk.com/
        /// </summary>
        public string DomainUrl { get; set; }
        /// <summary>
        /// 钉钉开放平台秘钥Key，Client ID (原 AppKey 和 SuiteKey)
        /// </summary>
        public string AppKey { get; set; }
        /// <summary>
        /// 钉钉开放平台秘钥Secret,Client Secret (原 AppSecret 和 SuiteSecret)
        /// </summary>
        public string AppSecret { get; set; }
        /// <summary>
        /// 钉钉开放平台企业Id，CorpId
        /// </summary>
        public string CorpId { get; set; }

        /// <summary>
        /// 原企业内部应用AgentId，用于jsapi请求使用
        /// </summary>
        public string AgentId { get; set; }

        /// <summary>
        /// 自定义固定字符串，用于jsapi请求使用
        /// </summary>
        public string NonceStr { get; set; }

        /// <summary>
        /// 获取钉钉AccessToken接口地址，https://api.dingtalk.com/v1.0/oauth2/accessToken
        /// </summary>
        public string GetAccessTokenUrl { get; set; }
        /// <summary>
        /// 获取钉钉用户Id（userid）接口地址，https://oapi.dingtalk.com/topapi/v2/user/getuserinfo
        /// </summary>
        public string GetUserInfoUrl { get; set; }
        /// <summary>
        /// 查询用户详情接口，接口地址：https://oapi.dingtalk.com/topapi/v2/user/get
        /// </summary>
        public string GetUserGetUrl { get; set; }
        /// <summary>
        /// 创建待办接口，接口地址：https://api.dingtalk.com/v1.0/todo/users/{unionId}/tasks
        /// </summary>
        public string SetUserTaskUrl { get; set; }

        /// <summary>
        /// 更新钉钉待办任务,接口地址：https://api.dingtalk.com/v1.0/todo/users/{unionId}/tasks/{taskId}
        /// </summary>
        public string SetUpdateUserTaskUrl { get; set; }
        /// <summary>
        /// 删除钉钉待办任务,接口地址：https://api.dingtalk.com/v1.0/todo/users/{unionId}/tasks/{taskId}
        /// </summary>
        public string SetDeleteUserTaskUrl { get; set; }

        /// <summary>
        /// 获取子部门ID列表,接口地址：https://oapi.dingtalk.com/topapi/v2/department/listsubid
        /// </summary>
        public string GetDeptSubListIdUrl { get; set; }

        /// <summary>
        /// 获取子部门列表,接口地址：https://oapi.dingtalk.com/topapi/v2/department/listsub
        /// </summary>
        public string GetDeptSubListUrl { get; set; }

        /// <summary>
        /// 获取部门详情接口，接口地址：https://oapi.dingtalk.com/topapi/v2/department/get
        /// </summary>
        public string GetGetDeptDetailUrl { get; set; }

        /// <summary>
        /// 获取部门下用户完整信息列表，接口地址：https://oapi.dingtalk.com/topapi/v2/user/list
        /// </summary>
        public string GetDeptUserDetailListIdUrl { get; set; }

        /// <summary>
        /// 部门Id集合，以逗号隔开，钉钉中部门以树状结构存储，此属性不区分哪一级，直接冲上往下获取子孙节点，然后根据deptid获取用户列表
        /// </summary>
        public string LevelFirstDeptIds { get; set; }

        /// <summary>
        /// 将LevelFirstDeptIds转成集合形式
        /// </summary>
        public List<long> LevelFirstDeptIdList
        {
            get
            {
                List<long> result = new List<long>();
                try
                {
                    result = this.LevelFirstDeptIds.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries).ToList().Select(x => long.Parse(x)).ToList();
                }
                catch
                {
                    //result = new List<long>() { 839384414 };
                    result = new List<long>() { 1 };
                }
                return result;
            }
        }

        /// <summary>
        /// 异步发送工作通知消息接口，接口地址：https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2
        /// </summary>
        public string SendDingWorkNoticeUrl { get; set; }

        /// <summary>
        /// 更新钉钉代办执行者状态，接口地址：https://api.dingtalk.com/v1.0/todo/users/{unionId}/tasks/{taskId}/executorStatus?operatorId={operatorId}
        /// </summary>
        public string ExecutorStatusUrl { get; set; }
    }

    /// <summary>
    /// 文件存储服务器配置
    /// </summary>
    public class MinIOConfig
    {
        public string Endpoint { get; set; }
        public string Region { get; set; }
        public string AccessKey { get; set; }
        public string SecretKey { get; set; }
        public string BucketName { get; set; }
        public string FileURL { get; set; }
    }

    /// <summary>
    /// 报备工单级别工单提前申请时间配置（小时）
    /// </summary>
    public class RepRiskLevelTimeOffSetConfig
    {
        /// <summary>
        /// 报备工单级别
        /// </summary>
        public string RiskLevel { get; set; }
        /// <summary>
        /// 至少提前申请时间(小时)
        /// </summary>
        public int OffSetHours { get; set; }
    }

    /// <summary>
    /// 维护计划模版信息列表
    /// </summary>
    public class QmoEmtModuleConfig
    {
        /// <summary>
        /// 所属专业
        /// </summary>
        public string ProductSpe { get; set; }

        /// <summary>
        /// 存在cdn中的文件夹名称
        /// </summary>
        public string ValueId { get; set; }

        /// <summary>
        /// 模板文件名称
        /// </summary>
        public string DocFileName { get; set; }

        /// <summary>
        /// cdn原地址
        /// </summary>
        public string CdnOriginalUrl { get; set; }

        /// <summary>
        /// Cdn地址
        /// </summary>
        public string CdnUrl { get; set; }

        /// <summary>
        /// 所属类别
        /// </summary>
        public EnumEmtOperateKind EmtOperateKind { get; set; }

    }

    public class ProductSpeOutputLineRelDto
    {
        public string ProductSpe { get; set; }

        public long DingDeptId { get; set; }
    }

    #endregion

    #region QMO_V2 配置对象实体

    /// <summary>
    /// 预报通知字段配置
    /// </summary>
    public class NoticeTaskTodoDto
    {
        /// <summary>
        /// 部门信息
        /// </summary>
        public string DepName { get; set; }

        /// <summary>
        /// 要显示的字段信息
        /// </summary>
        public List<NoticeTaskTodoDetDto> ShowColumns { get; set; }
    }

    /// <summary>
    /// 预报通知显示字段配置
    /// </summary>
    public class NoticeTaskTodoDetDto
    {
        public string ColumnName { get; set; }

        /// <summary>
        /// NET代码中反射属性名
        /// </summary>
        public string PropertyName { get; set; }

        public string DisplayName { get; set; }
    }


    public class QaAppConfig
    {
        /// <summary>
        /// 专业名称
        /// </summary>
        public string ProductSpe { get; set; }
        public string AppCode { get; set; }

        public string AppName { get; set; }

        public List<string> TplIds { get; set; }

        public List<string> RelateAppCodes { get; set; }
    }

    #endregion

    /// <summary>
    /// 所属类别
    /// </summary>
    public enum EnumEmtOperateKind
    {
        派发工单 = 1,
        派发网络 = 2,
        变更工单 = 3,
        实施任务 = 4,
        定时任务 = 5
    }
    /// <summary>
    /// 特殊的工序（用来实施任务完成的时候的特殊判断）
    /// </summary>
    public class SpecialStep
    {
        public string GroupZhName { get; set; }
        public string StepZhName { get; set; }
        public string GroupEnName { get; set; }
        public string StepEnName { get; set; }
    }


}
