﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class modify_table_executetask : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "ManagerRemindeCount",
                table: "emt_executetask",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SupervisorRemindCount",
                table: "emt_executetask",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<DateTime>(
                name: "SupervisorRemindTime",
                table: "emt_executetask",
                type: "datetime(6)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ManagerRemindeCount",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "SupervisorRemindCount",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "SupervisorRemindTime",
                table: "emt_executetask");
        }
    }
}
