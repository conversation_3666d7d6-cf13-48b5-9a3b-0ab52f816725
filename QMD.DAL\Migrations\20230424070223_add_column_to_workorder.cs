﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_column_to_workorder : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "Level1LastApprovalTime",
                table: "TaskItemAndTplRels",
                type: "datetime(6)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "Level2LastApprovalTime",
                table: "TaskItemAndTplRels",
                type: "datetime(6)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "SubmitTime",
                table: "TaskItemAndTplRels",
                type: "datetime(6)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "City",
                table: "ItrWorkOrders",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "Operator",
                table: "ItrWorkOrders",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "Province",
                table: "ItrWorkOrders",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "ItrWorkOrders",
                type: "longtext",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<DateTime>(
                name: "Level1LastApprovalTime",
                table: "CycleTaskRecords",
                type: "datetime(6)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "Level2LastApprovalTime",
                table: "CycleTaskRecords",
                type: "datetime(6)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "SubmitTime",
                table: "CycleTaskRecords",
                type: "datetime(6)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "UserType",
                table: "ContractorUserRels",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Level1LastApprovalTime",
                table: "TaskItemAndTplRels");

            migrationBuilder.DropColumn(
                name: "Level2LastApprovalTime",
                table: "TaskItemAndTplRels");

            migrationBuilder.DropColumn(
                name: "SubmitTime",
                table: "TaskItemAndTplRels");

            migrationBuilder.DropColumn(
                name: "City",
                table: "ItrWorkOrders");

            migrationBuilder.DropColumn(
                name: "Operator",
                table: "ItrWorkOrders");

            migrationBuilder.DropColumn(
                name: "Province",
                table: "ItrWorkOrders");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "ItrWorkOrders");

            migrationBuilder.DropColumn(
                name: "Level1LastApprovalTime",
                table: "CycleTaskRecords");

            migrationBuilder.DropColumn(
                name: "Level2LastApprovalTime",
                table: "CycleTaskRecords");

            migrationBuilder.DropColumn(
                name: "SubmitTime",
                table: "CycleTaskRecords");

            migrationBuilder.DropColumn(
                name: "UserType",
                table: "ContractorUserRels");
        }
    }
}
