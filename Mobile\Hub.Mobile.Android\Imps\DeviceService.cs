﻿using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Views;
using Android.Widget;
using Hub.Mobile.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Xamarin.Forms;

[assembly: Dependency(typeof(Hub.Mobile.Droid.Imps.DeviceService))]
namespace Hub.Mobile.Droid.Imps
{
    public class DeviceService : IDeviceService
    {
        /// <summary>
        /// 获取手机厂商
        /// </summary>
        /// <returns></returns>
        public string GetDeviceBrand()
        {
            return Build.Brand;
        }
        /// <summary>
        /// 获取手机型号
        /// </summary>
        /// <returns></returns>
        public string GetDeviceModel()
        {
            return Build.Model;
        }
        /// <summary>
        /// 获取手机系统版本号
        /// </summary>
        /// <returns></returns>
        public string GetSystemVersion()
        {
            return Build.VERSION.Release;
        }
    }
}