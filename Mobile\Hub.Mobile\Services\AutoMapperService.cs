﻿using AutoMapper;
using Hub.Mobile.DAL.Tables;
using Hub.Mobile.Interface;
using Hub.Mobile.Model;
using System;
using System.Collections.Generic;
using System.Text;
[assembly: Xamarin.Forms.Dependency(typeof(Hub.Mobile.Services.AutoMapperService))]
namespace Hub.Mobile.Services
{
    public class AutoMapperService : IAutoMapperService
    {
        private static IMapper autoMapper;
        static AutoMapperService()
        {
            var mapperConfiguration = new MapperConfiguration(
                                cfg =>
                                {
                                    cfg.CreateMap<LoginUser, UserInfo>();
                                    cfg.CreateMap<AppItem, ModuleInfo>();
                                });
            autoMapper = mapperConfiguration.CreateMapper();
        }
        public IMapper AutoMapper
        {
            get { return autoMapper; }
        }
    }
}
