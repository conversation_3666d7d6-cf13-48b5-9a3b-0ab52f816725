﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>%Title%</title>
    <style>
        ::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
/*            background-color: #FDF2E9;*/
            color: #333;
            margin: 0;
            padding: 0;
        }

        .container {
            margin: 0 auto;
            padding: 20px; 
            background-color: #E9EBE7;
            border: 1px solid #E9EBE7;
            box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);  
        }

        h2 {
            text-align: center;
            color: #D35400;
        }

        .section {
            margin-bottom: 20px;  
        }
        #section-table {
            padding-bottom: 10px; 
        }

            .section p {
                margin: 5px 0;
            }

        table {
            /*width: 100%;*/
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        table, th, td {
            border: 1px solid #d2d1ce;
        }

        th, td {
            padding: 8px;
            text-align: left;
        }

        .highlight tbody td:first-child {
            background-color: #EDEDEF;
            width:200px;
        }
        .highlight tbody td.first-child-nobg {
            background: none;
        }

        .sub-header {
            background-color: #3598DB;
            color: white;
            font-weight: bold;
            text-align: center;
        }
        table.width-auto {
            table-layout: auto; /* 默认值，即自适应 */
            white-space: nowrap; /* 防止内容换行 */
            overflow: hidden; /* 隐藏溢出的内容 */
            text-overflow: ellipsis; /* 显示省略号 */
            width: 1200px;
            height: 460px;
            overflow: auto;
            display: inline-block;
        }
            table.width-auto th, table.width-auto td {
                table-layout: auto; /* 默认值，即自适应 */
                width: auto; /* 自适应 */
                white-space: nowrap; /* 防止内容换行 */
                overflow: hidden; /* 隐藏溢出的内容 */
                text-overflow: ellipsis; /* 显示省略号 */
            }
    </style>
</head>
<body>
    <div style="padding: 30px;  display: inline-block;overflow:hidden">
        <div class="container">
            <!--<div class="section">
            <p>尊敬的客户，您好！</p>
            <p>请您在邮件正文中回复“同意”或者“不同意”审批烽火授权服务申请。出于安全考虑，回复邮件时请勿删除邮件主题中的申请单号以及源邮件，请勿输入密码，详细授权申请内容请见下表；</p>
            <p>非常感谢，谨致问候！</p>
        </div>-->

            <h2>%Title%</h2>

            <div class="section">
                <div>%CustomerName%，你好,%Description%</div>
                <p>以下是工单详细信息，请注意查收.</p>
            </div>

            <div class="section" id="section-table">
                %table%
            </div>
        </div>
        <!--英文部分-->
        <div class="container">
            <h2>%TitleEn%</h2>
            <div class="section">
                <div>Hello, %CustomerNameEn%.%DescriptionEn%</div>
                <p>Here are the details of the work order. Please acknowledge receipt.</p>
            </div>

            <div class="section" id="section-table">
                %tableEn%
            </div>
        </div>
    </div>
</body>
</html>
