(function () {
    function Position() {
    }
    //success {"Latitude":xxx,"Longitude":xxx,"Altitude":xxx}
    //error    错误提示
    Position.prototype.getGPS = function (successCallback, errorCallback) {
      if (typeof errorCallback != "function") {
          console.error("parameter [errorCallback] should be function");
          return;
        }
        if (typeof successCallback != "function") {
          console.error("parameter [successCallback] should be function");
          return;
        }
        hub_mobile_drive.exec(
        function (result) {
          successCallback(result);
        },
        function (error) {
          errorCallback(error);
        },
        "Position",
        "GetGPS",
        null
      );
    };
    //添加插件
    hub_mobile_drive.addPlugin("position", new Position());
  })();
  