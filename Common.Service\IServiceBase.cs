﻿using Common.DAL;
using Common.DAL.Methods;
using Common.Repository;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace Common.Service
{
    public interface IServiceBase
    {
        X Clone<X>(X entity, bool newID) where X : EntityBase, new();
        List<X> Clone<X>(List<X> entities, bool newID) where X : EntityBase, new();
    }

    public interface IServiceBase<S, T> : IServiceBase where S : class, IPrimaryKeyEntity, new() where T : EntityBase
    {
        S Find(string id);
        S Create();
        S Update(S entity);
        S Update(T entity);
        S Insert(S entity);
        S Insert(T entity);
        void Delete(string id, bool indeed = false);
        void DeleteAll();
        void Delete(T entity, bool indeed = false);
        void Delete(List<T> entities, bool indeed = false);
        List<S> FindAll();
        List<S> GetPageData(Expression<Func<T, bool>> filter, PageCriteria criteriaBase, out int totalCount);
        S TransferToDto(T entity);
        List<S> TransferToDto(IList<T> entities);
    }
}
