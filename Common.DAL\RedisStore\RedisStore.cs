﻿using StackExchange.Redis;
using System;

namespace Common.DAL.RedisStore
{
    public class RedisStore : IDisposable
    {
        //private Lazy<ConnectionMultiplexer> LazyConnection =
        //    new Lazy<ConnectionMultiplexer>(() =>
        //        {
        //            return ConnectionMultiplexer.Connect(AppSetting.Instance["Redis_Cache_Address"]);
        //        });
        private string _address;
        private int _dbIndex;
        private ConnectionMultiplexer _connection;
        private IDatabase _db;
        public RedisStore(string address, int dbIndex)
        {
            _address = address;
            _dbIndex = dbIndex;
        }

        public IDatabase RedisCache
        {
            get
            {
                if (_connection == null)
                {
                    _connection = ConnectionMultiplexer.Connect(_address);
                }
                if (_db == null)
                {
                    _db = _connection.GetDatabase(_dbIndex);
                }
                return _db;
            }
        }

        public void Dispose()
        {
            if (_connection != null && _connection.IsConnected)
            {
                _db = null;
                _connection.Close();
                _connection.Dispose();
                _connection = null;
            }
        }
    }
}
