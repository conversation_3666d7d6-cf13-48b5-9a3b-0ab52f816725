﻿using Hub.Mobile.Const;
using Hub.Mobile.DAL;
using Hub.Mobile.DAL.Tables;
using System;
using System.Collections.Generic;
using System.Text;
using Xamarin.Forms;

namespace Hub.Mobile
{
    public class UIResourceManager : ResourceDictionary
    {
        private static UIResourceManager resourceManager;
        public static UIResourceManager GetInstance()
        {
            return resourceManager;
        }
        public static Dictionary<string, string> SupporttedCultureNames = new Dictionary<string, string>
        {
            { "","English" },{ "zh","中文" }
        };

        public UIResourceManager()
        {
            string cultureName = null;
            #region db加载cultrueName
            DataCache dataCache = MobileSQLiteHelper.Current.DbContext.Table<DataCache>().Where(p => p.Key == MobileCommonConsts.LanguageCacheKey).FirstOrDefaultAsync().Result;
            if (dataCache != null)
            {
                cultureName = dataCache.Value;
            }

            #endregion
            resourceManager = this;
            LoadResource(cultureName);
        }
        public void LoadResource(string cultureName)
        {
            if (cultureName == null)
            {
                //默认语言选择 英文
                cultureName = "";
            }
            if (cultureName == "en")
            {
                cultureName = "";
            }
            Resources.UIResources.Culture = new System.Globalization.CultureInfo(cultureName);
            Resources.ExceptionResources.Culture = new System.Globalization.CultureInfo(cultureName);

            var culture = Resources.UIResources.Culture;
            var set = Resources.UIResources.ResourceManager.GetResourceSet(culture, true, false);

            var dic = set.GetEnumerator();
            while (dic.MoveNext())
            {
                if (this.ContainsKey(dic.Key.ToString()))
                {
                    this[dic.Key.ToString()] = dic.Value;
                }
                else
                {
                    this.Add(dic.Key.ToString(), dic.Value);
                }
            }
        }
    }
}
