﻿using Hub.Mobile.Interface;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using WebApiClient;
using WebApiClient.Contexts;
using Xamarin.Forms;

namespace Hub.Mobile.ApiClient
{
    public class ApiTokenFilter : IApiActionFilter
    {
        private static ApiTokenFilter instance;
        static ApiTokenFilter()
        {
            instance = new ApiTokenFilter();
        }
        public static ApiTokenFilter GetInstance()
        {
            return instance;
        }
        public async Task OnBeginRequestAsync(ApiActionContext context)
        {
            string token = (await DependencyService.Get<IAccountService>().GetCurrentUser())?.Token;
            context.RequestMessage.Headers.Add("Authorization", $"Bearer {token ?? "empty"}");
            return ;
        }
        public Task OnEndRequestAsync(ApiActionContext context)
        {
            return Task.FromResult<object>(null);
        }
    }
}
