﻿using Hub.Mobile.Const;
using Hub.Mobile.Droid.CustomRenders;
using Hub.Mobile.Interface;
using NLog;
using System;
using System.IO;
using Xamarin.Forms;
using Xamarin.Forms.Platform.Android;
using AWebKit = Android.Webkit;

namespace Hub.Mobile.Droid
{
    public class JavascriptWebViewClient : FormsWebViewClient
    {
        ILogger logger= LogManager.GetCurrentClassLogger();
        string _javascript;
        IApp appService;

        public JavascriptWebViewClient(HybridWebViewRenderer renderer, string javascript) : base(renderer)
        {
            _javascript = javascript;
            appService = DependencyService.Get<IApp>();
        }

        public override void OnPageFinished(AWebKit.WebView view, string url)
        {
            base.OnPageFinished(view, url);
            view.EvaluateJavascript(_javascript, null);
        }
        public override AWebKit.WebResourceResponse ShouldInterceptRequest(AWebKit.WebView view, AWebKit.IWebResourceRequest request)
        {
            try
            {
                string host = request.Url.Host;
                var hybridIndex = host.IndexOf(MobileCommonConsts.HybridRootDomain);
                if (hybridIndex ==0)
                {
                    //hybrid
                    string appCode = host.Substring(MobileCommonConsts.HybridRootDomain.Length+1);
                    string requestPath = request.Url.Path;
                    if (string.IsNullOrWhiteSpace(requestPath))
                    {
                        requestPath = "index.html";
                    }
                    String mimeType = AWebKit.MimeTypeMap.Singleton.GetMimeTypeFromExtension(AWebKit.MimeTypeMap.GetFileExtensionFromUrl(requestPath));
                    Stream fileInputStream = File.OpenRead(Path.Combine(appService.GetHybridAppPhysicalPath(appCode), requestPath.Trim('/')));
                    return new AWebKit.WebResourceResponse(mimeType, null, fileInputStream);
                }
                else
                {
                    return base.ShouldInterceptRequest(view, request);
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return base.ShouldInterceptRequest(view, request);
            }
        }
    }
}