﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_taskstepvalue_bigdata : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "BigDataId",
                table: "TaskStepValues",
                type: "varchar(512)",
                maxLength: 512,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ExtValue6",
                table: "TaskStepValues",
                type: "longtext",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<DateTime>(
                name: "InspectTime",
                table: "TaskStepValues",
                type: "datetime(6)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "BigDataId",
                table: "TaskStepValues");

            migrationBuilder.DropColumn(
                name: "ExtValue6",
                table: "TaskStepValues");

            migrationBuilder.DropColumn(
                name: "InspectTime",
                table: "TaskStepValues");
        }
    }
}
