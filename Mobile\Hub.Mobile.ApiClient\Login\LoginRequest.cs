﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Hub.Mobile.ApiClient.Login
{
    public class LoginRequest: BaseRequest
    {
        public string Email { get; set; }
        public string Verifycode { get; set; }
        public string Password { get; set; }
        /// <summary>
        /// App都是通过邮箱登录的，服务器端账号登录需要字段是UserName
        /// </summary>
        public string UserName { get { return Email; } }
        public bool PasswordLogin { get; set; }
        public string Grant_Type { get { return PasswordLogin ? "password" : "email"; } }
    }
}
