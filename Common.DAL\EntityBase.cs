﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Common.DAL
{
    public abstract class EntityBase : IPrimaryKeyEntity
    {
        [Display(Name = "ID")]
        [MaxLength(128)]
        [Key]
        public string ID { get; set; }

        [Display(Name = "是否有效")]
        public bool IsActived { get; set; }
        /// <summary>
        /// UTC时间
        /// </summary>
        [Display(Name = "修改时间")]
        public DateTime? ModifiedDateTime { get; set; }
        /// <summary>
        /// UTC时间
        /// </summary>
        [Display(Name = "创建时间")]
        public DateTime? CreatedDateTime { get; set; }
        [MaxLength(128)]
        [Display(Name = "创建人")]
        public string CreatedUser { get; set; }
        [MaxLength(128)]
        [Display(Name = "修改人")]
        public string ModifiedUser { get; set; }
         
    }
}
