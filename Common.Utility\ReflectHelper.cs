﻿using Common.Model;
using Npoi.Mapper.Attributes;
using NPOI.SS.Formula.Functions;
using OfficeOpenXml.Interfaces.Drawing.Text;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Dynamic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Common.Utility
{
    /// <summary>
    /// 反射
    /// </summary>
    public static class ReflectHelper
    {
        public static string GetPropertyName<T>(Expression<Func<T, object>> expression)
        {
            var rtn = string.Empty;

            if (expression.Body is UnaryExpression)
            {
                rtn = ((MemberExpression)((UnaryExpression)expression.Body).Operand).Member.Name;
            }
            else if (expression.Body is MemberExpression)
            {
                rtn = ((MemberExpression)expression.Body).Member.Name;
            }
            else if (expression.Body is ParameterExpression)
            {
                rtn = ((ParameterExpression)expression.Body).Type.Name;
            }
            else
                throw new MessageException("Cannot get the propert name");

            return rtn;
        }

        public static List<string> GetPropertyName<T>(params Expression<Func<T, object>>[] expressions)
        {
            var list = new List<string>();

            foreach (var exp in expressions)
            {
                list.Add(GetPropertyName<T>(exp));
            }

            return list; 
        }

        public static string GetDescriptionByEnum(Enum enumValue)
        {
            string value = enumValue.ToString();
            System.Reflection.FieldInfo field = enumValue.GetType().GetField(value);
            var objs = field.GetCustomAttributes(typeof(DescriptionAttribute),false);
            if(objs.Length == 0)
                return value;
            return ((DescriptionAttribute)objs[0]).Description;
        }
        public static int GetPropertyCount<T>()
        {
            Type type = typeof(T);
            PropertyInfo[] properties = type.GetProperties(BindingFlags.Instance | BindingFlags.Public);
            return properties.Length;
        }

        public static List<PropertyInfo> GetProperties<T>()
        {
            Type type = typeof(T);
            PropertyInfo[] properties = type.GetProperties(BindingFlags.Instance | BindingFlags.Public);
            return properties.ToList();
        }

        public static List<string> GetPropertyName<T>()
        {
            List<string> result = new List<string>();
            Type type = typeof(T);
            PropertyInfo[] properties = type.GetProperties(BindingFlags.Instance | BindingFlags.Public);
            foreach (var item in properties)
            {
                result.Add(item.Name);
            }
            return result;
        }

        public static List<string> GetColumnName<T>()
        {
            List<string> result = new List<string>();
            Type type = typeof(T);
            PropertyInfo[] properties = type.GetProperties(BindingFlags.Instance | BindingFlags.Public);
            foreach (var item in properties)
            {
                var attr = item.GetCustomAttribute<ColumnAttribute>();
                if (attr != null)
                {
                    result.Add(attr.Name);
                }
                else
                    result.Add(item.Name);
            }
            return result;
        }

        public static List<DropdownItem> GetDropdownData<TEnum>(bool valueIsInt = false) where TEnum : Enum
        {
            Type enumType = typeof(TEnum);
            var dropdownData = Enum.GetValues(enumType)
                                   .Cast<Enum>()
                                   .Select(e => new DropdownItem
                                   {
                                       //Value = e.ToString(),
                                       Value = valueIsInt? Convert.ToInt32(e): Convert.ToInt32(e),//改用字符数字
                                       Text = ReflectHelper.GetDescriptionByEnum(e)
                                   })
                                   .ToList();
            return dropdownData;
        }

        /// <summary>
        /// 将集合转成datatble
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public static DataTable ToDataTable<T>(this List<T> data) where T : class
        {
            //PropertyDescriptorCollection properties = TypeDescriptor.GetProperties(typeof(T));
            T entity = default(T);
            if (data != null && data.Count > 0)
                entity = data[0];
            var properties = entity.GetType().GetProperties();
            DataTable dt = new DataTable();
            for (int i = 0; i < properties.Length; i++)
            {
                PropertyInfo property = properties[i];
                var orgType = property.PropertyType;
                if (orgType.Name.Contains("Nullable"))
                {
                    System.ComponentModel.NullableConverter nullableConverter = new System.ComponentModel.NullableConverter(property.PropertyType);
                    var type = nullableConverter.UnderlyingType;
                    dt.Columns.Add(property.Name, type);
                }
                else
                    dt.Columns.Add(property.Name, orgType);//property.PropertyType
            }
            object[] values = new object[properties.Length];
            foreach (T item in data)
            {
                for (int i = 0; i < values.Length; i++)
                {
                    values[i] = properties[i].GetValue(item);
                }
                dt.Rows.Add(values);
            }
            return dt;
        }
    }
    public class DropdownItem
    {
        public dynamic Value { get; set; }//可能为int,也有可能为string
        public string Text { get; set; }
    }

    public class DynamicObjectDto : DynamicObject
    {
        public override bool TryGetMember(System.Dynamic.GetMemberBinder binder, out object result)
        {
            if (map != null)
            {
                string name = binder.Name;
                object value;
                if (map.TryGetValue(name, out value))
                {
                    result = value;
                    return true;
                }
            }
            return base.TryGetMember(binder, out result);
        }

        System.Collections.Generic.Dictionary<string, object> map;

        public override bool TryInvokeMember(System.Dynamic.InvokeMemberBinder binder, object[] args, out object result)
        {
            if (binder.Name == "set" && binder.CallInfo.ArgumentCount == 2)
            {
                string name = args[0] as string;
                if (name == null)
                {
                    //throw new ArgumentException("name");  
                    result = null;
                    return false;
                }
                if (map == null)
                {
                    map = new System.Collections.Generic.Dictionary<string, object>();
                }
                object value = args[1];
                map.Add(name, value);
                result = value;
                return true;

            }
            return base.TryInvokeMember(binder, args, out result);
        }
    }
}
