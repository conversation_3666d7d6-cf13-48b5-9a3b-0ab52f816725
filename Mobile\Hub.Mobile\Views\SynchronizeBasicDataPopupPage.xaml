﻿<?xml version="1.0" encoding="utf-8" ?>
<pages:PopupPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:pages="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup"
                    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Animations;assembly=Rg.Plugins.Popup"
             x:Class="Hub.Mobile.Views.SynchronizeBasicDataPopupPage" CloseWhenBackgroundIsClicked="False">
    <pages:PopupPage.Animation>
        <animations:ScaleAnimation DurationIn="400"
                                   DurationOut="300"
                                   EasingIn="SinOut"
                                   EasingOut="SinIn"
                                   HasBackgroundAnimation="True"
                                   PositionIn="Center"
                                   PositionOut="Center"
                                   ScaleIn="1.2"
                                   ScaleOut="0.8" />
    </pages:PopupPage.Animation>
    <Grid HorizontalOptions="Center" VerticalOptions="Center" BackgroundColor="White">
        <AbsoluteLayout>
            <Grid Padding="5,30,5,30">
                <Grid.RowDefinitions>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition></ColumnDefinition>
                </Grid.ColumnDefinitions>
                <Label Grid.Row="0" FontSize="Subtitle" TextColor="Green" VerticalOptions="Center" HorizontalOptions="Center" Text="{Binding LastSynchronizationTime}"></Label>
                <Label Grid.Row="1" FontSize="Subtitle" Text="{Binding Tip}" VerticalOptions="Center" HorizontalOptions="Center" Margin="10" TextColor="Red"></Label>
            </Grid>
            <ImageButton BackgroundColor="Transparent" Source="icon_close.png" HeightRequest="30" WidthRequest="30"
                     AbsoluteLayout.LayoutFlags="PositionProportional" 
                     AbsoluteLayout.LayoutBounds="1,0,-1,-1" Command="{Binding CloseCommand}" IsEnabled="{Binding BtnCloseEnabled}"></ImageButton>
        </AbsoluteLayout>
    </Grid>
</pages:PopupPage>