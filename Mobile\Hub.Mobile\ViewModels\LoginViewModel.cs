﻿using FluentValidation;
using Hub.Mobile.ApiClient;
using Hub.Mobile.ApiClient.Login;
using Hub.Mobile.Const;
using Hub.Mobile.DAL;
using Hub.Mobile.DAL.Tables;
using Hub.Mobile.Interface;
using Hub.Mobile.Model;
using Hub.Mobile.Resources;
using Hub.Mobile.Services;
using NLog;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using WebApiClient;
using Xamarin.Forms;

namespace Hub.Mobile.ViewModels
{
    public class LoginViewModel : BasePageViewModel
    {
        private ILogger loggger = LogManager.GetCurrentClassLogger();
        public Command LoginCommand { get; }
        public Command ImageCodeCommand { get; }
        public Command EmailCodeCommand { get; }
        public Command CloseCommand { get; }
        private string sid = string.Empty;
        private const int keepEmailCount = 5;
        ILoginApiClient loginApiClient;
        public List<DataCache> EmailDataCache = new List<DataCache>();
        private bool passwordLogin = true;
        private string placeHolderText = UIResources.EmailOrUserName;
        public bool PasswordLogin
        {
            get
            {
                return passwordLogin;
            }
            set
            {
                SetProperty(ref passwordLogin, value);
                PlaceHolderText = passwordLogin ? UIResources.EmailOrUserName: UIResources.Email;
            }
        }
        public string PlaceHolderText
        {
            get { return placeHolderText; }
            set { SetProperty(ref placeHolderText, value); }
        }
        public Command SwitchLoginCommand { get; }
        public LoginViewModel()
        {
            LoginCommand = new Command(OnLoginClicked);
            ImageCodeCommand = new Command(OnImageCodeClicked);
            EmailCodeCommand = new Command(OnEmailCodeClicked);
            CloseCommand = new Command(OnCloseClicked);
            loginApiClient = HttpApi.Resolve<ILoginApiClient>();
            GenerateImageCode();
            LoadEmailDataCacheFromDb();
            SwitchLoginCommand = new Command(() => { PasswordLogin = !PasswordLogin; ValidateMsg = string.Empty; });
        }
        private async void LoadEmailDataCacheFromDb()
        {
            try
            {
                EmailDataCache = await MobileSQLiteHelper.Current.QueryList<DataCache, DateTime?>(p => p.Key == MobileCommonConsts.LoginEmailCacheKey, p => p.ModifiedTime, keepEmailCount);
            }
            catch (Exception ex)
            {
                loggger.Error(ex, ex?.Message);
            }
        }
        private async Task SaveEmailToDb(string email)
        {
            try
            {
                var dataCache = EmailDataCache.Where(p => p.Value == email).FirstOrDefault();
                if (dataCache != null)
                {
                    dataCache.ModifiedTime = DateTime.UtcNow;
                    await MobileSQLiteHelper.Current.Update(dataCache);
                }
                else
                {
                    int emailCacheCount = EmailDataCache.Count();
                    while (emailCacheCount >= keepEmailCount)
                    {
                        await MobileSQLiteHelper.Current.Delete(EmailDataCache[emailCacheCount - 1]);
                        emailCacheCount--;
                    }
                    //根据modifyedTime排序的，所以新增ModifiedTime赋值
                    dataCache = new DataCache() { Key = MobileCommonConsts.LoginEmailCacheKey, Value = email, ModifiedTime = DateTime.UtcNow };
                    await MobileSQLiteHelper.Current.Insert(dataCache);
                    EmailDataCache.Add(dataCache);
                }
            }
            catch (Exception ex)
            {
                loggger.Error(ex, ex?.Message);
            }
        }

        private async void OnCloseClicked(object obj)
        {
            await Navigation.PopPopupAsync();
        }

        private async void OnEmailCodeClicked(object obj)
        {
            BtnEmailCodeEnabled = false;
            ValidateMsg = string.Empty;
            try
            {
                LoginRequest LoginUserIns = new LoginRequest();
                LoginUserIns.Email = Email;
                LoginUserIns.Verifycode = ImageCode;
                var validationResult = GenValidator().Validate(LoginUserIns);
                if (validationResult.IsValid)
                {
                    BtnEmailCodeText = UIResources.Sending;
                    var response = await loginApiClient.SendEmailCode(new SendEmailCodeRequest() { Email = LoginUserIns.Email, Code = LoginUserIns.Verifycode }, sid);
                    if (response.Result == "failed")
                    {
                        ValidateMsg = response.Message;
                    }
                    if (response.Result == "success")
                    {
                        int timeNum = response.Datenum;
                        await SaveEmailToDb(Email);
                        await Task.Factory.StartNew(() =>
                        {
                            while (timeNum > 0)
                            {
                                BtnEmailCodeText = $"{timeNum}";
                                Thread.Sleep(1000);
                                timeNum--;
                            }
                        });
                    }
                }
                else
                {
                    if (validationResult.Errors.Count > 0)
                    {
                        ValidateMsg = validationResult.Errors[0].ErrorMessage;
                    }
                    else
                    {
                        ValidateMsg = UIResources.ValidatorFailed;
                    }
                }
            }
            catch (Exception ex)
            {
                loggger.Error(ex, ex?.Message);
                ValidateMsg = UIResources.AcquireVerificationCodeFailed;
            }
            BtnEmailCodeEnabled = true;
            BtnEmailCodeText = UIResources.AcquireVerificationCode;
        }

        private void OnImageCodeClicked(object obj)
        {
            GenerateImageCode();
        }

        private void GenerateImageCode()
        {
            sid = System.Guid.NewGuid().ToString();
            ImageCodeUrl = Path.Combine(ApiAddresses.LoginApiBaseUrl, $"api/Image/GetWXRegisterVerifyImage?sid={sid}");
        }
        private string validateMsg;
        public string ValidateMsg
        {
            get
            {
                return validateMsg;
            }
            set
            {
                SetProperty(ref validateMsg, value);
            }
        }
        string email = string.Empty;
        public string Email
        {
            get { return email; }
            set { SetProperty(ref email, value); }
        }

        string imageCode = string.Empty;
        public string ImageCode
        {
            get { return imageCode; }
            set { SetProperty(ref imageCode, value); }
        }
        string imageCodeUrl = string.Empty;
        public string ImageCodeUrl
        {
            get { return imageCodeUrl; }
            set { SetProperty(ref imageCodeUrl, value); }
        }
        string emailCode = string.Empty;
        public string EmailCode
        {
            get { return emailCode; }
            set { SetProperty(ref emailCode, value); }
        }
        string password = string.Empty;
        public string Password
        {
            get { return password; }
            set { SetProperty(ref password, value); }
        }
        private string btnEmailCodeText = UIResources.AcquireVerificationCode;
        public string BtnEmailCodeText
        {
            get { return btnEmailCodeText; }
            set { SetProperty(ref btnEmailCodeText, value); }
        }
        public bool btnEmailCodeEnabled = true;
        public bool BtnEmailCodeEnabled { get { return btnEmailCodeEnabled; } set { SetProperty(ref btnEmailCodeEnabled, value); } }
        public bool btnLoginEnabled = true;
        public bool BtnLoginEnabled { get { return btnLoginEnabled; } set { SetProperty(ref btnLoginEnabled, value); } }
        private LoginValidator GenValidator()
        {
            LoginValidator validator = new LoginValidator();
            if (this.PasswordLogin)
            {
                validator.RuleFor(x => x.Email).NotEmpty().WithMessage(UIResources.EmailOrUserNameRequired);
                validator.RuleFor(x => x.Password).NotEmpty().WithMessage(UIResources.PasswordRequired);
            }
            else
            {
                validator.RuleFor(x => x.Email).NotEmpty().WithMessage(UIResources.EmailRequired)
.EmailAddress().WithMessage(UIResources.EmailIncorrect);
                validator.RuleFor(x => x.Verifycode).NotEmpty().WithMessage(UIResources.VerificationCodeRequired);
            }
            return validator;
        }
        private async void OnLoginClicked(object obj)
        {
            BtnLoginEnabled = false;
            ValidateMsg = string.Empty;
            try
            {
                // Prefixing with `//` switches to a different navigation stack instead of pushing to the active one
                //await Shell.Current.GoToAsync($"//{nameof(AboutPage)}");
                LoginRequest LoginUserIns = new LoginRequest();
                LoginUserIns.PasswordLogin = this.PasswordLogin;
                LoginUserIns.Email = email;
                LoginUserIns.Verifycode = emailCode;
                LoginUserIns.Password = Password;
                var validationResult = GenValidator().Validate(LoginUserIns);
                if (validationResult.IsValid)
                {
                    var response = await loginApiClient.Login(LoginUserIns, sid);
                    if (!string.IsNullOrEmpty(response.Error))
                    {
                        ValidateMsg = response.Error;
                    }
                    else
                    {
                        var userInfo = await loginApiClient.GetUserInfo($"Bearer {response.Access_Token}");
                        if (string.IsNullOrEmpty(userInfo.Email))
                        {
                            userInfo.Email = LoginUserIns.Email?.Trim();
                        }
                        await SaveEmailToDb(userInfo.Email);
                        UserInfo loginUser = new UserInfo() { Email = userInfo.Email, UserId = userInfo.Id, NickName = userInfo.NickName, Position = userInfo.Position, Areas = userInfo.Areas, Token = response.Access_Token };
                        await DependencyService.Get<IAccountService>().LoginAsync(loginUser);
                        await Navigation.PopPopupAsync();
                        Task.Run(async () =>
                        {
                            try
                            {
                                await DependencyService.Get<SynchronizeDataService>().SynchronizeData();
                            }
                            catch (Exception ex)
                            {
                                loggger.Error(ex, ex?.Message);
                            }
                        });
                    }
                }
                else
                {
                    if (validationResult.Errors.Count > 0)
                    {
                        ValidateMsg = validationResult.Errors[0].ErrorMessage;
                    }
                    else
                    {
                        ValidateMsg = UIResources.ValidatorFailed;
                    }
                }
            }
            catch (Exception ex)
            {
                loggger.Error(ex, ex?.Message);
                ValidateMsg = UIResources.LogInFailed;
            }
            BtnLoginEnabled = true;
        }
    }
    public class LoginValidator : AbstractValidator<LoginRequest>
    {
    }
}
