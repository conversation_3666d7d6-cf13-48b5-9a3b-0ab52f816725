﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Common.DAL.Methods
{
    public class BaseRes
    {
        public BaseRes(bool flag, string msg)
        {
            Flag = flag;
            Message = msg;
        }

        public BaseRes()
        {
            Flag = true;
        }
        /// <summary>
        /// Flag false 返回的消息
        /// </summary>
        public string Message { get; set; }
        /// <summary>
        /// true 成功,false 失败
        /// </summary>
        public bool Flag { get; set; }
    }

    public class BaseRes<T> : BaseRes
    { 
        public BaseRes(T data, bool flag, string msg) : base(flag, msg)
        {
            Data = data;
        }
        public BaseRes(bool flag, string msg) : base(flag, msg)
        {
            Flag = flag;
            Message = msg;
        }
        public BaseRes(T data) { Data = data; Flag = true; Message = "success"; }
        /// <summary>
        /// flag true 接口返回的数据
        /// </summary>
        public T Data { get; set; }

    }
}
