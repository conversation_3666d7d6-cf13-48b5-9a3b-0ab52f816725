﻿using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Hub.Mobile.Plugin
{
    public class PluginService
    {
        static ILogger logger = LogManager.GetCurrentClassLogger();
        private static Dictionary<string, IPlugin> plugins = new Dictionary<string, IPlugin>();
        static PluginService()
        {
            var types = Assembly.GetExecutingAssembly().GetTypes().Where(t => t.IsClass && !t.IsAbstract && t.GetInterface("IPlugin") != null);
            foreach (var type in types)
            {
                IPlugin instance = Activator.CreateInstance(type) as IPlugin;
                if (instance != null)
                {
                    if (plugins.ContainsKey(type.Name))
                    {
                        Console.WriteLine($"plugin {type.Name} already register");
                    }
                    else
                    {
                        plugins.Add(type.Name, instance);
                    }
                }
            }
        }
        public static async Task<ExecResult> Exec(string serverName, string action, string args)
        {
            try
            {
                if (!plugins.ContainsKey(serverName))
                {
                    return new ExecResult() { Success = false, Message = $"server:{serverName} unregistered" };
                }
                var service = plugins[serverName];
                MethodInfo method = service.GetType().GetMethod(action);
                if (method == null)
                {
                    return new ExecResult() { Success = false, Message = $"server:{serverName} action:{action} not existed" };
                }
                var param = method.GetParameters().Count() == 0 ? null : new object[] { args };
                return await (Task<ExecResult>)method.Invoke(service, param);
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return new ExecResult() { Success = false, Message = ex.Message };
            }

        }
    }
}
