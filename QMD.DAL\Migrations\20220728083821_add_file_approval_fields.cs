﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_file_approval_fields : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "Level1PassedFileCount",
                table: "TaskItemAndTplRels",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "Level2PassedFileCount",
                table: "TaskItemAndTplRels",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "Level3PassedFileCount",
                table: "TaskItemAndTplRels",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Level1PassedFileCount",
                table: "TaskItemAndTplRels");

            migrationBuilder.DropColumn(
                name: "Level2PassedFileCount",
                table: "TaskItemAndTplRels");

            migrationBuilder.DropColumn(
                name: "Level3PassedFileCount",
                table: "TaskItemAndTplRels");
        }
    }
}
