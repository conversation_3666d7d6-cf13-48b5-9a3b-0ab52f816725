﻿using Hub.Mobile.Const;
using Hub.Mobile.Model;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Hub.Mobile.Interface
{
    public interface IApp
    {
        bool SaveAppConfig(List<AppItem> apps);
        List<AppItem> GetAppConfig();
        EnumAppVersionState GetAppVersionState(string appCode);
        string GetHybridStartUrl(string appCode, string startPage);
        string GetHybridAppPhysicalPath(string appCode);
        string GetHybridAppBasePath();
        void UpdateAppVersion(HybridAppVersion newAppVersion);
    }
}
