﻿<?xml version="1.0" encoding="utf-8" ?>
<pages:PopupPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Hub.Mobile.Views.HybridAppUpgrade"
                        xmlns:animations="clr-namespace:Rg.Plugins.Popup.Animations;assembly=Rg.Plugins.Popup"
                 xmlns:pages="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" CloseWhenBackgroundIsClicked="False">
    <pages:PopupPage.Animation>
        <animations:ScaleAnimation DurationIn="400"
                                   DurationOut="300"
                                   EasingIn="SinOut"
                                   EasingOut="SinIn"
                                   HasBackgroundAnimation="True"
                                   PositionIn="Center"
                                   PositionOut="Center"
                                   ScaleIn="1.2"
                                   ScaleOut="0.8" />
    </pages:PopupPage.Animation>
    <Grid HorizontalOptions="Center" VerticalOptions="Center" BackgroundColor="White">
        <AbsoluteLayout>
            <StackLayout HorizontalOptions="Center" VerticalOptions="Center" Padding="5,45,5,15">
                <ProgressBar  WidthRequest="300" Progress="{Binding Progress}" ProgressColor="Green" ScaleY="8"></ProgressBar>
                <CollectionView WidthRequest="300" HeightRequest="250" ItemsSource="{Binding Tips}" VerticalScrollBarVisibility="Always" Margin="0,10,0,0">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Label FontSize="16"  Text="{Binding Message}" TextColor="{Binding Color}" HorizontalTextAlignment="Center"></Label>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
            </StackLayout>
            <ImageButton BackgroundColor="Transparent" Source="icon_close.png" HeightRequest="30" WidthRequest="30"
                     AbsoluteLayout.LayoutFlags="PositionProportional" 
                     AbsoluteLayout.LayoutBounds="1,0,-1,-1" Command="{Binding CloseCommand}" IsEnabled="{Binding BtnCloseEnabled}"></ImageButton>
        </AbsoluteLayout>
    </Grid>
</pages:PopupPage>