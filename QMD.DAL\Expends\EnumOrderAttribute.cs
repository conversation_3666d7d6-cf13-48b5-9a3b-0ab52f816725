﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace QMD.DAL.Expends
{
    public class EnumOrderAttribute: Attribute
    {
        public int Order { get; }

        public EnumOrderAttribute(int order)
        {
            Order = order;
        }
    }

    /// <summary>
    /// 工单种类，主要用于过滤工单的种类
    /// </summary>
    public class EnumOrderKindAttribute : Attribute
    {
        public int OrderKind { get; set; }
        public EnumOrderKindAttribute(int orderKind)
        {
            OrderKind = orderKind;
        }
    }
}
