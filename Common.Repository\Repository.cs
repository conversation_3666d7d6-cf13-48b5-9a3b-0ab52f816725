﻿using Common.DAL;
using Common.DAL.Methods;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using System.Linq.Dynamic.Core;
using Common.Utility;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.SqlClient;
using System.Reflection;
using Z.EntityFramework.Plus;
using Common.Model;

namespace Common.Repository
{
    public class Repository<T, D> : IRepository<T> where D : DbContext where T : EntityBase, new()
    {
        private readonly D _context;
        public D DbContext { get { return _context; } }
        public Repository(D context)
        {
            _context = context;
        }
        public virtual bool CheckValidation(T entity, out string msg)
        {
            msg = string.Empty;
            return true;
        }
        public virtual T Create()
        {
            T t = new T();
            t.ID = Guid.NewGuid().ToString("N");
            t.CreatedDateTime = DateTime.UtcNow;
            t.CreatedUser = EntityBaseHelper.GetCurrentUser();
            return t;
        }

        public virtual void Delete(T entity, bool indeed = false)
        {
            if (indeed)
            {
                _context.Remove<T>(entity);
            }
            else
            {
                entity.IsActived = false;
                entity.ModifiedUser = EntityBaseHelper.GetCurrentUser();
                entity.ModifiedDateTime = DateTime.UtcNow;
            }
            _context.SaveChanges();
        }

        public void Delete(IList<T> entities, bool indeed = false)
        {
            foreach (var entity in entities)
            {
                if (indeed)
                {
                    _context.Remove<T>(entity);
                }
                else
                {
                    entity.IsActived = false;
                    entity.ModifiedUser = EntityBaseHelper.GetCurrentUser();
                    entity.ModifiedDateTime = DateTime.UtcNow;
                }
            }
            _context.SaveChanges();
        }

        public virtual void DeleteAll()
        {
            _context.Set<T>().Delete();
        }

        public virtual void Delete(string id, bool indeed = false)
        {
            if (indeed)
            {
                _context.Set<T>().Where(x => x.ID == id).Delete();
            }
            else
            {
                _context.Set<T>().Where(x => x.ID == id)
                    .Update(x => new T
                    {
                        ModifiedDateTime = DateTime.UtcNow,
                        ModifiedUser = EntityBaseHelper.GetCurrentUser(),
                        IsActived = false
                    });
            }
        }

        public virtual void Delete(IList<string> ids, bool indeed = false)
        {
            if (indeed)
            {
                _context.Set<T>().Where(x => ids.Contains(x.ID)).Delete();
            }
            else
            {
                _context.Set<T>().Where(x => ids.Contains(x.ID))
                    .Update(x => new T
                    {
                        ModifiedDateTime = DateTime.UtcNow,
                        ModifiedUser = EntityBaseHelper.GetCurrentUser(),
                        IsActived = false
                    });
            }
        }


        public virtual void Delete(Expression<Func<T,bool>> predicate, bool indeed = false)
        {
            if (indeed)
            {
                _context.Set<T>().Where(predicate).Delete();
            }
            else
            {
                _context.Set<T>().Where(predicate)
                    .Update(x => new T
                    {
                        ModifiedDateTime = DateTime.UtcNow,
                        ModifiedUser = EntityBaseHelper.GetCurrentUser(),
                        IsActived = false
                    });
            }
        }


        public virtual List<T> FindAll()
        {
            return _context.Set<T>().Where(i => i.IsActived).OrderByDescending(i => i.ModifiedDateTime).ToList();
        }

        public virtual T Insert(T entity)
        {
            if (string.IsNullOrWhiteSpace(entity.ID))
            {
                entity.ID = Guid.NewGuid().ToString("N");
            }
            if (!entity.CreatedDateTime.HasValue)
            {
                entity.CreatedDateTime = DateTime.UtcNow;
                entity.CreatedUser = EntityBaseHelper.GetCurrentUser();
            }
            entity.IsActived = true;
            if (!CheckValidation(entity, out string msg))
            {
                throw new MessageException(msg);
            }
            _context.Add<T>(entity);
            _context.SaveChanges();
            return entity;
        }



        public virtual List<T> Insert(IList<T> entities)
        {
            List<T> list = new List<T>();
            entities.ToList().ForEach(entity =>
            {
                if (!CheckValidation(entity, out string msg))
                {
                    throw new MessageException(msg);
                }
                if (string.IsNullOrWhiteSpace(entity.ID))
                {
                    entity.ID = Guid.NewGuid().ToString("N");
                }
                if (!entity.CreatedDateTime.HasValue)
                {
                    entity.CreatedDateTime = DateTime.UtcNow;
                    entity.CreatedUser = EntityBaseHelper.GetCurrentUser();
                }
                entity.IsActived = true;
                list.Add(entity);
            });
            _context.Set<T>().AddRange(entities);
            _context.SaveChanges();
            return list;
        }


        public virtual List<T> Inserts(IList<T> entities)
        {
            _context.ChangeTracker.AutoDetectChangesEnabled = false;
            _context.Set<T>().AddRange(entities);
            _context.SaveChanges();
            return entities.ToList();
        }

        public virtual IQueryable<T> Query(Expression<Func<T, bool>> filter)
        {
            return _context.Set<T>().Where(filter);
        }

        public virtual bool Any(Expression<Func<T, bool>> filter)
        {
            return _context.Set<T>().Any(filter);
        }

        public virtual bool All(Expression<Func<T, bool>> filter)
        {
            return _context.Set<T>().All(filter);
        }

        public virtual IQueryable<T> Query()
        {
            return _context.Set<T>().AsQueryable();
        }

        public virtual int BulkDelete(Expression<Func<T, bool>> filter)
        {
            return _context.Set<T>().Where(filter).Delete();
        }

        public virtual int BulkUpdate(Expression<Func<T, bool>> filter, Expression<Func<T, T>> updates)
        {
            _context.Database.SetCommandTimeout(1800);
            return _context.Set<T>().Where(filter).Update(updates);
        }

        public virtual List<T> GetPageData(Expression<Func<T, bool>> filter, PageCriteria criteriaBase, out int totalCount)
        {
            var query = _context.Set<T>().AsQueryable();
            if (filter != null)
            {
                query = query.Where(filter);
            }
            return PageAndSort(query, criteriaBase, out totalCount);
        }

        public virtual List<T> GetPageData(PageCriteria criteriaBase, out int totalCount)
        {
            var query = _context.Set<T>().AsQueryable();
            return PageAndSort(query, criteriaBase, out totalCount);
        }

        public virtual List<T> GetPageData(IQueryable<T> query, PageCriteria criteriaBase, out int totalCount)
        {
            return PageAndSort(query, criteriaBase, out totalCount);
        }

        public virtual T Update(T entity, bool autoChangeModifiedInfo = true)
        {
            if (!CheckValidation(entity, out string msg))
            {
                throw new MessageException(msg);
            }
            if (autoChangeModifiedInfo)
            {
                entity.ModifiedDateTime = DateTime.UtcNow;
                entity.ModifiedUser = EntityBaseHelper.GetCurrentUser();
            }
            _context.Update(entity);
            _context.SaveChanges();
            return entity;
        }

        public virtual List<T> Update(IList<T> entities, bool autoChangeModifiedInfo = true)
        {
            _context.Database.SetCommandTimeout(1800);
            List<T> list = new List<T>();
            entities.ToList().ForEach(entity =>
            {
                if (!CheckValidation(entity, out string msg))
                {
                    throw new MessageException(msg);
                }
                if (autoChangeModifiedInfo)
                {
                    entity.ModifiedDateTime = DateTime.UtcNow;
                    entity.ModifiedUser = EntityBaseHelper.GetCurrentUser();
                }
                _context.Update(entity);
                list.Add(entity);
            });
            _context.SaveChanges();
            return list;
        }

        protected static List<T> PageAndSort(IQueryable<T> query, PageCriteria criteria, out int totalCount)
        {
            string orderCondition = string.Empty;
            if (criteria.Sort?.Count() > 0)
            {
                orderCondition = string.Join(',', criteria.Sort.OrderBy(p => p.SortOrder).Select(p => { return p.IsDescending ? $"{p.SortField} desc" : $"{p.SortField} asc"; }));
            }
            else
            {
                if (criteria.GetDefaultSort()?.Count() > 0)
                {
                    orderCondition = string.Join(',', criteria.GetDefaultSort().OrderBy(p => p.SortOrder).Select(p => { return p.IsDescending ? $"{p.SortField} desc" : $"{p.SortField} asc"; }));
                }
            }
            if (!string.IsNullOrEmpty(orderCondition))
            {
                query = query.OrderBy(orderCondition);
            }
            var count = query.Count();
            if (criteria.PageNumber.GetValueOrDefault(0) > 0)
            {
                if (criteria.PageNumber.GetValueOrDefault(0) > 1)
                {
                    int skipCount = criteria.PageNumber.Value - 1;
                    query = query.Skip(skipCount * criteria.PageSize.Value).Take(criteria.PageSize.Value);
                }
                else
                {
                    query = query.Take(criteria.PageSize.Value);
                }
            }

            totalCount = count;
            return query.ToList();
        }

        public void Dispose()
        {
            if (_context != null)
                _context.Dispose();
        }

        public T Find(string id)
        {
            return _context.Find<T>(id);
        }

        public T Find(Expression<Func<T,bool>> predicate)
        {
            return _context.Set<T>().Where(predicate).FirstOrDefault();
        }

        public bool Exist(string id)
        {
            return _context.Set<T>().Any(x => x.ID == id);
        }

        public void Active(string id)
        {
            var entity = _context.Set<T>().Find(id);
            if (entity != null)
            {
                entity.IsActived = true;
                _context.Update(entity);
                _context.SaveChanges();
            }
        }

        private string GetTableName()
        {
            var type = typeof(T);
            var table_attr_obj = type.GetCustomAttributes(typeof(TableAttribute), false).FirstOrDefault();
            if (table_attr_obj != null)
            {
                TableAttribute attr = table_attr_obj as TableAttribute;
                return attr.Name;
            }
            return type.Name;
        }
    }
}
