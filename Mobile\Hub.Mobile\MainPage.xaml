﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:model="clr-namespace:Hub.Mobile.Model;assembly=Hub.Mobile.Model"
             x:Class="Hub.Mobile.MainPage" Title="FTS hub">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="auto"></RowDefinition>
            <RowDefinition Height="auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
        </Grid.RowDefinitions>
        <Grid Grid.Row="0" BackgroundColor="#BEBDBB"  HeightRequest="300" Margin="0,0,0,0">
            <!--图片背景-->
            <Image Source="cover.png"  Aspect="AspectFill"/>
            <Grid BackgroundColor="White" Opacity="0.4"></Grid>
            <!--导航栏-->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="auto"/>
                </Grid.ColumnDefinitions>
                <Grid Grid.Column="0">
                    <Label Margin="10,30,0,0" Text="FTS hub"  FontAttributes="Bold" TextColor="Black" FontSize="18"  HorizontalTextAlignment="Start" VerticalTextAlignment="Start"></Label>
                </Grid>
                <Grid Grid.Column="1" Margin="0,30,0,0">
                    <Path HorizontalOptions="Center" StrokeThickness="1"  Stroke="Transparent"  Fill="black"   Data="M12,8c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM12,10c-1.1,0 -2,0.9 -2,2s0.9,2 2,2 2,-0.9 2,-2 -0.9,-2 -2,-2zM12,16c-1.1,0 -2,0.9 -2,2s0.9,2 2,2 2,-0.9 2,-2 -0.9,-2 -2,-2z"/>
                    <Button BackgroundColor="Transparent" Margin="0,0,0,0" Command="{Binding SetCommand}" HorizontalOptions="Center" IsVisible="true" VerticalOptions="Start" WidthRequest="30" HeightRequest="40" ></Button>
                </Grid>
            </Grid>
        </Grid>
        <Label Padding="15,0,0,0" Grid.Row="1"  Text="Software Center"  FontAttributes="Bold" TextColor="Black" FontSize="18"  HorizontalTextAlignment="Start" VerticalTextAlignment="Start"></Label>
        <CollectionView Grid.Row="2" ItemsSource="{Binding Modules}"  ItemsLayout="VerticalGrid, 3">
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <Grid Padding="10">
                        <Frame BackgroundColor="{Binding BackgroundColor}" Padding="5,5,5,5" Margin="5,5,5,5" HasShadow="False" CornerRadius="10" x:DataType="model:ModuleInfo">
                            <Grid WidthRequest="100" HeightRequest="80">
                                <Grid HorizontalOptions="Center" VerticalOptions="Center">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="auto"></RowDefinition>
                                        <RowDefinition Height="auto"></RowDefinition>
                                    </Grid.RowDefinitions>
                                    <Image Grid.Row="0" HorizontalOptions="Center" Source="{Binding ImageSource}" Aspect="AspectFit" HeightRequest="50" WidthRequest="50"></Image>
                                    <Label Grid.Row="1" HorizontalTextAlignment="Center" TextColor="Black" FontSize="14" FontAttributes="Bold" Text="{Binding DisplayName}"></Label>
                                </Grid>
                            </Grid>
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type ContentPage}},Path=BindingContext.NavigationCommand}" CommandParameter="{Binding .}">
                                </TapGestureRecognizer>
                            </Frame.GestureRecognizers>
                        </Frame>
                    </Grid>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
    </Grid>
</ContentPage>