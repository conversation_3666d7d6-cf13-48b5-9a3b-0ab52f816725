﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_userrole_index : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_per_relroleuser_RoleId",
                table: "per_relroleuser",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_per_relroleuser_UserEmail",
                table: "per_relroleuser",
                column: "UserEmail");

            migrationBuilder.CreateIndex(
                name: "IX_NetUserPositions_UserEmail",
                table: "NetUserPositions",
                column: "UserEmail");

            migrationBuilder.CreateIndex(
                name: "IX_Netproviders_ChargePersonEmail",
                table: "Netproviders",
                column: "ChargePersonEmail");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_per_relroleuser_RoleId",
                table: "per_relroleuser");

            migrationBuilder.DropIndex(
                name: "IX_per_relroleuser_UserEmail",
                table: "per_relroleuser");

            migrationBuilder.DropIndex(
                name: "IX_NetUserPositions_UserEmail",
                table: "NetUserPositions");

            migrationBuilder.DropIndex(
                name: "IX_Netproviders_ChargePersonEmail",
                table: "Netproviders");
        }
    }
}
