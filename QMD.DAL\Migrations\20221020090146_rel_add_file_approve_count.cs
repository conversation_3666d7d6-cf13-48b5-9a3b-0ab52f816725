﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class rel_add_file_approve_count : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "WaitLevel1ApproveFileCount",
                table: "TaskItemAndTplRels",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "WaitLevel2ApproveFileCount",
                table: "TaskItemAndTplRels",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "WaitLevel1ApproveFileCount",
                table: "TaskItemAndTplRels");

            migrationBuilder.DropColumn(
                name: "WaitLevel2ApproveFileCount",
                table: "TaskItemAndTplRels");
        }
    }
}
