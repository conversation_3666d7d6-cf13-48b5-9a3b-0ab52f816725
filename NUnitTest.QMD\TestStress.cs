﻿using NUnit.Framework;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using QMD.DAL;
using Microsoft.EntityFrameworkCore;
using System;
using QMD.Repository;
using QMD.Service;
using QMD.Env;
using Common.Service;
using AutoMapper;
using QMD.Model;
using Common.DAL.Methods;
using Common.Utility;
using QMD.DAL.Table;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http.Internal;
using System.IO;
using System.Collections.Generic;


namespace NUnitTest.QMD
{
    public class TestStress
    {
        public IServiceCollection _services = null;
        public ServiceProvider _serviceProvider = null;
        private IConfiguration _configuration = null;

        [SetUp]
        public void Setup()
        {
            _services = new ServiceCollection();

            var builder = new ConfigurationBuilder()
               .SetBasePath(AppContext.BaseDirectory)
               .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
               .AddEnvironmentVariables();
            _configuration = builder.Build();

            _services.AddSingleton<IConfiguration>(_configuration);

            _services.AddDbContext<QmdDbContext>(
                options =>
         options.UseMySql(
             _configuration.GetConnectionString("DefaultConnection"),
             ServerVersion.AutoDetect(_configuration.GetConnectionString("DefaultConnection")
                 ))
             );

            _services.RegisterRepositories();
            _services.RegisterServices();

            ConfigEnvValues.Init(_configuration);
            AutoMapperConfig.Configure(new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<AutoMapperProfile>();
            }));

            _serviceProvider = _services.BuildServiceProvider();

            HttpContextHelper.InitHttpContext(_serviceProvider);
            NLog.LogManager.LoadConfiguration("nlog.config");

            CdnAddressHelper.SetCDNRootUrl(ConfigEnvValues.CDNRootUrl, ConfigEnvValues.CDNProjectName, ConfigEnvValues.DomainURL);
        }

        [Test]
        public void TestCreateSite()
        {
            ServiceLineService serviceLineService = _serviceProvider.GetService<ServiceLineService>();
            RegionService regionService = _serviceProvider.GetService<RegionService>();
            ProjectService projectService = _serviceProvider.GetService<ProjectService>();
            TaskItemService taskItemService = _serviceProvider.GetService<TaskItemService>();
            TaskTplService tplService = _serviceProvider.GetService<TaskTplService>();
            TaskStepValueService valueService = _serviceProvider.GetService<TaskStepValueService>();
            ApprovalService approvalService = _serviceProvider.GetService<ApprovalService>();
            RelTaskItemAndTplService relService = _serviceProvider.GetService<RelTaskItemAndTplService>();
            ExportDataService exportService = _serviceProvider.GetService<ExportDataService>();

            System.Diagnostics.Debug.WriteLine("新增服务线");
            var serviceLine = serviceLineService.Create();
            serviceLine.Code = "test";
            serviceLine = serviceLineService.Insert(serviceLine);
            System.Diagnostics.Debug.WriteLine("新增区域");
            var region = regionService.Create();
            region.Code = "001";
            regionService.Insert(region);

            var projects = new List<ProjectDto>();

            for (int i = 0; i < 20; i++)
            {
                System.Diagnostics.Debug.WriteLine($"新增项目:{i}");
                var project = projectService.Create();
                project.ServiceLineID = serviceLine.ID;
                project.RegionCode = region.Code;
                project.DisplayName = "测试项目" + i;
                project.Code = i.ToString();

                projectService.Insert(project);
                projects.Add(project);
            }

            var random = new Random(DateTime.Now.Millisecond);

            for (int i = 0; i < 5; i++)
            {
                var projID = projects[random.Next(0, projects.Count - 1)].ID;
                var taskItems = new List<TaskItemDto>();
                var tpls = new List<TaskTplDto>();
                var steps = new List<TaskStepTplDto>();

                System.Diagnostics.Debug.WriteLine($"循环:{i + 1}/5,新增任务类型:{i}");
                var tpl = tplService.Create();

                tpl.DisplayName = "测试模板" + i;
                tpl.ProjectID = projID;
                tpl.ServiceLineID = serviceLine.ID;
                tpl.RegionCode = region.Code;
                tpl = tplService.Insert(tpl);
                tplService.AddOrUpdateTpl(tpl);
                tpls.Add(tpl);

                System.Diagnostics.Debug.WriteLine($"循环:{i + 1}/5,新增group");
                var group1 = tplService.AddOrUpdateTplGroup(new TaskGroupTplDto
                {
                    DisplayName = "分组1",
                    Order = 1,
                    TplID = tpl.ID
                });

                for (int k = 0; k < 200; k++)
                {
                    System.Diagnostics.Debug.WriteLine($"循环:{i + 1}/5,新增工序:{k}");
                    steps.Add(tplService.AddOrUpdateTplStep(new TaskStepTplDto
                    {
                        TplID = tpl.ID,
                        AllowMany = true,
                        CountMaxLimit = 5,
                        IsRequired = true,
                        Order = k,
                        SupportFileTypes = StepFileType.NoLimit,
                        DataType = StepDataType.File,
                        AllowSelect = true,
                        DisplayName = "工序" + k,
                        GroupID = group1.ID
                    }));
                }
                System.Diagnostics.Debug.WriteLine($"循环:{i + 1}/5,发布分组");
                tplService.PublishGroup(tpl.ID);
                System.Diagnostics.Debug.WriteLine($"循环:{i + 1}/5,发布工序");
                tplService.PublishStep(tpl.ID);
                System.Diagnostics.Debug.WriteLine($"循环:{i + 1}/5,发布任务类型");
                tplService.PublishTpl(tpl.ID);

                var tmp = new List<TaskItemDto>();
                for (int k = 0; k < 10000; k++)
                {
                    System.Diagnostics.Debug.WriteLine($"循环:{i + 1}/5,新增站点:{k + 1}/10000");
                    var taskItem = taskItemService.Create();
                    taskItem.Code = "TEST" + k;
                    taskItem.DisplayName = "武汉烽火技术服务有限公司";
                    taskItem.ProjectID = projID;
                    taskItem.ServiceLineID = serviceLine.ID;

                    tmp.Add(taskItem);

                    if (tmp.Count % 10 == 0)
                    {
                        taskItemService.Insert(tmp);
                        tmp.Clear();
                    }
                    taskItems.Add(taskItem);
                    taskItemService.AssignTaskItemTpls(new List<string> { taskItem.ID }, new List<string>() { tpl.ID });
                }
            }
        }

        [Test]
        public void TestExport()
        {
            ExportDataService exportService = _serviceProvider.GetService<ExportDataService>();
            System.Diagnostics.Debug.WriteLine($"测试导出预览excel");
            var previews_path = exportService.ExportTaskItemPreviews(null, null, null, null, null, null, null);
            System.Diagnostics.Debug.WriteLine($"测试导出待审批excel");
            //var approval_path = exportService.ExportTaskItemsReadyApproval(null, null, null, null, null, null, 1);
        }
    }
}
