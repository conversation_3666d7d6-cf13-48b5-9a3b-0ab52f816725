﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class change_table_reportorder : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_TaskItems_Code_ProjectID",
                table: "TaskItems");

            migrationBuilder.DropIndex(
                name: "IX_QaWorkOrders_TaskItemId_TplId_TaskStepID_TriggerDay",
                table: "QaWorkOrders");

            migrationBuilder.DropColumn(
                name: "ImpDept",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "ImpEmail",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "ImpNickName",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "ImpPhone",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "ImpPostCode",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "ImpPostName",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "ImpProductSpe",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "ImpTechLevel",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "ImpTime",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "ImpUserName",
                table: "emt_reportorders");

            migrationBuilder.RenameColumn(
                name: "CustomerPhone",
                table: "emt_reportorders",
                newName: "ThirdApprover");

            migrationBuilder.RenameColumn(
                name: "CustomerName",
                table: "emt_reportorders",
                newName: "SecondApprover");

            migrationBuilder.RenameColumn(
                name: "CustomerMail",
                table: "emt_reportorders",
                newName: "ForthApprover");

            migrationBuilder.AlterColumn<string>(
                name: "NeName",
                table: "QaOrders",
                type: "varchar(512)",
                maxLength: 512,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(256)",
                oldMaxLength: 256,
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<bool>(
                name: "IsAllOperator",
                table: "per_roleinfo",
                type: "tinyint(1)",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "OperatorsListStr",
                table: "per_roleinfo",
                type: "longtext",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "FirstApprover",
                table: "emt_reportorders",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ReportFaultPersonDept",
                table: "emt_executetask",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ReportFaultPersonEmail",
                table: "emt_executetask",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ReportFaultPersonName",
                table: "emt_executetask",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ReportFaultPersonNickName",
                table: "emt_executetask",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ReportFaultPersonPhone",
                table: "emt_executetask",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "Property6",
                table: "ActionRecords",
                type: "varchar(512)",
                maxLength: 512,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(256)",
                oldMaxLength: 256,
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_TaskItems_Code_ProjectID",
                table: "TaskItems",
                columns: new[] { "Code", "ProjectID" });

            migrationBuilder.CreateIndex(
                name: "IX_QaWorkOrders_TaskItemId_TplId_TaskStepID_TriggerDay",
                table: "QaWorkOrders",
                columns: new[] { "TaskItemId", "TplId", "TaskStepID", "TriggerDay" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_TaskItems_Code_ProjectID",
                table: "TaskItems");

            migrationBuilder.DropIndex(
                name: "IX_QaWorkOrders_TaskItemId_TplId_TaskStepID_TriggerDay",
                table: "QaWorkOrders");

            migrationBuilder.DropColumn(
                name: "IsAllOperator",
                table: "per_roleinfo");

            migrationBuilder.DropColumn(
                name: "OperatorsListStr",
                table: "per_roleinfo");

            migrationBuilder.DropColumn(
                name: "FirstApprover",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "ReportFaultPersonDept",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "ReportFaultPersonEmail",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "ReportFaultPersonName",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "ReportFaultPersonNickName",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "ReportFaultPersonPhone",
                table: "emt_executetask");

            migrationBuilder.RenameColumn(
                name: "ThirdApprover",
                table: "emt_reportorders",
                newName: "CustomerPhone");

            migrationBuilder.RenameColumn(
                name: "SecondApprover",
                table: "emt_reportorders",
                newName: "CustomerName");

            migrationBuilder.RenameColumn(
                name: "ForthApprover",
                table: "emt_reportorders",
                newName: "CustomerMail");

            migrationBuilder.AlterColumn<string>(
                name: "NeName",
                table: "QaOrders",
                type: "varchar(256)",
                maxLength: 256,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(512)",
                oldMaxLength: 512,
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ImpDept",
                table: "emt_reportorders",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ImpEmail",
                table: "emt_reportorders",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ImpNickName",
                table: "emt_reportorders",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ImpPhone",
                table: "emt_reportorders",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ImpPostCode",
                table: "emt_reportorders",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ImpPostName",
                table: "emt_reportorders",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ImpProductSpe",
                table: "emt_reportorders",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ImpTechLevel",
                table: "emt_reportorders",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<DateTime>(
                name: "ImpTime",
                table: "emt_reportorders",
                type: "datetime(6)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ImpUserName",
                table: "emt_reportorders",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "Property6",
                table: "ActionRecords",
                type: "varchar(256)",
                maxLength: 256,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(512)",
                oldMaxLength: 512,
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_TaskItems_Code_ProjectID",
                table: "TaskItems",
                columns: new[] { "Code", "ProjectID" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_QaWorkOrders_TaskItemId_TplId_TaskStepID_TriggerDay",
                table: "QaWorkOrders",
                columns: new[] { "TaskItemId", "TplId", "TaskStepID", "TriggerDay" },
                unique: true);
        }
    }
}
