{"normal": [{"columnName": "ID", "bindProp": "Code", "width": 3200}, {"columnName": "司机姓名", "bindProp": "<PERSON><PERSON><PERSON>", "width": 3200}, {"columnName": "省份", "bindProp": "Addr1", "width": 3200}, {"columnName": "市区", "bindProp": "Addr2", "width": 3200}, {"columnName": "区域", "bindProp": "Region", "width": 3200}, {"columnName": "车辆类型", "bindProp": "VehicleType", "width": 3200}, {"columnName": "油箱容量(L)", "bindProp": "FuelTankCapacity"}], "month": [{"columnName": "系统计算的月度里程数（KM)", "bindProp": "SysCalMile"}, {"columnName": "系统计算的月度参考油耗金额(本地货币)", "bindProp": "SysCalCost"}, {"columnName": "当月本油卡花费总额(本地货币)", "bindProp": "FuelCardCost"}, {"columnName": "市场油价(本地货币/L)", "bindProp": "OilPrice"}, {"columnName": "根据月度油卡花费计算出的月度油耗(L)", "bindProp": "CardCalOil"}, {"columnName": "一线提供的百公里油耗参考值(L/100KM)", "bindProp": "FuelConsumption"}, {"columnName": "挪用次数", "bindProp": "ExceptionNum"}, {"columnName": "停车费", "bindProp": "ParkingFee"}, {"columnName": "高速费", "bindProp": "<PERSON>ll<PERSON><PERSON>"}], "day": [{"columnName": "挪用公里数(KM)", "bindProp": "DiffDistance", "min": 0, "max": 1}, {"columnName": "第一次check-in里程数(KM)", "bindProp": "FirstCheckIn"}, {"columnName": "最后一次check-out里程数(KM)", "bindProp": "LastCheckOut"}, {"columnName": "return最后一次里程数(KM)", "bindProp": "Return"}, {"columnName": "当天里程数（当天return-当天第一次check in)(KM)", "bindProp": "Mile"}, {"columnName": "油耗金额(本地货币)", "bindProp": "Cost"}]}