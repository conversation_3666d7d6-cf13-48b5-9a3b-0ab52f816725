﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class process : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_TaskStepValues_TaskItemID_TaskTplID_TaskStepID",
                table: "TaskStepValues",
                columns: new[] { "TaskItemID", "TaskTplID", "TaskStepID" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_TaskStepValues_TaskItemID_TaskTplID_TaskStepID",
                table: "TaskStepValues");
        }
    }
}
