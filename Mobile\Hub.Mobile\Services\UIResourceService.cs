﻿using Hub.Mobile.Const;
using Hub.Mobile.DAL;
using Hub.Mobile.DAL.Tables;
using Hub.Mobile.Interface;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

[assembly: Xamarin.Forms.Dependency(typeof(Hub.Mobile.Services.UIResourceService))]
namespace Hub.Mobile.Services
{
    public class UIResourceService :IUIResourceService
    {
        public string GetString(string name)
        {
            return Resources.UIResources.ResourceManager.GetString(name, Resources.UIResources.Culture);
        }
        public async Task SwitchLanguage(string cultureName)
        {
            UIResourceManager.GetInstance().LoadResource(cultureName);
            await SaveToDb(cultureName);
        }
        public async Task SaveToDb(string cultureName)
        {
            DataCache dbVal = await MobileSQLiteHelper.Current.DbContext.Table<DataCache>().Where(p => p.Key == MobileCommonConsts.LanguageCacheKey).FirstOrDefaultAsync();
            if (dbVal == null)
            {
                dbVal = new DataCache() { Key = MobileCommonConsts.LanguageCacheKey, Value = cultureName };
                await MobileSQLiteHelper.Current.DbContext.InsertAsync(dbVal);
            }
            else
            {
                dbVal.Value = cultureName;
                await MobileSQLiteHelper.Current.DbContext.UpdateAsync(dbVal);
            }
        }
        public string GetCurrentCultureName()
        {
            return Resources.UIResources.Culture.Name;
        }
    }
}
