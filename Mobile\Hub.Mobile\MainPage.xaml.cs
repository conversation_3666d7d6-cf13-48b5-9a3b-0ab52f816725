﻿using Hub.Mobile.Services;
using Hub.Mobile.ViewModels;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xamarin.Forms;

namespace Hub.Mobile
{
    public partial class MainPage : ContentPage
    {
        public MainPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
            BindingContext = new MainViewModel() { Navigation = this.Navigation };
            SynchronizeApps.AppChanged -= AppsChanged;
            SynchronizeApps.AppChanged += AppsChanged;
        }
        protected override void OnAppearing()
        {
            base.OnAppearing();
            (this.BindingContext as MainViewModel).LoadData();
        }
        public void AppsChanged(object sender, EventArgs eventHandler)
        {
            (this.BindingContext as MainViewModel).LoadData();
        }
    }
}
