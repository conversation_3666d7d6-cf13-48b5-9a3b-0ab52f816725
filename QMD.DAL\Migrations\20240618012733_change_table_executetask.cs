﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class change_table_executetask : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AttachList",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "AuditUserEmail",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "AuditUserName",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "AuditUserPhone",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "CustomerEmail",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "CustomerName",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "CustomerPhone",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "ReportFaultPersonDept",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "ReportFaultPersonEmail",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "ReportFaultPersonName",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "ReportFaultPersonNickName",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "ReportFaultPersonPhone",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "TaskNickName",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "TaskUserDept",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "TaskUserEmail",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "TaskUserLevel",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "TaskUserName",
                table: "emt_executetask");

            migrationBuilder.RenameColumn(
                name: "TaskUserPhone",
                table: "emt_executetask",
                newName: "ObjectUnit");

            migrationBuilder.AddColumn<string>(
                name: "AttachInfo",
                table: "emt_executetask",
                type: "longtext",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "AuthorizeAttachInfo",
                table: "emt_executetask",
                type: "longtext",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<int>(
                name: "ObjectCount",
                table: "emt_executetask",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AttachInfo",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "AuthorizeAttachInfo",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "ObjectCount",
                table: "emt_executetask");

            migrationBuilder.RenameColumn(
                name: "ObjectUnit",
                table: "emt_executetask",
                newName: "TaskUserPhone");

            migrationBuilder.AddColumn<string>(
                name: "AttachList",
                table: "emt_executetask",
                type: "varchar(500)",
                maxLength: 500,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "AuditUserEmail",
                table: "emt_executetask",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "AuditUserName",
                table: "emt_executetask",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "AuditUserPhone",
                table: "emt_executetask",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "CustomerEmail",
                table: "emt_executetask",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "CustomerName",
                table: "emt_executetask",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "CustomerPhone",
                table: "emt_executetask",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ReportFaultPersonDept",
                table: "emt_executetask",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ReportFaultPersonEmail",
                table: "emt_executetask",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ReportFaultPersonName",
                table: "emt_executetask",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ReportFaultPersonNickName",
                table: "emt_executetask",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ReportFaultPersonPhone",
                table: "emt_executetask",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "TaskNickName",
                table: "emt_executetask",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "TaskUserDept",
                table: "emt_executetask",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "TaskUserEmail",
                table: "emt_executetask",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "TaskUserLevel",
                table: "emt_executetask",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "TaskUserName",
                table: "emt_executetask",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }
    }
}
