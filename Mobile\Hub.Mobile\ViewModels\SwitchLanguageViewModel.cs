﻿
using Hub.Mobile.Interface;
using Hub.Mobile.Model;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Xamarin.Forms;

namespace Hub.Mobile.ViewModels
{
    public class SwitchLanguageViewModel : BasePageViewModel
    {
        IUIResourceService uIResourceService = DependencyService.Get<IUIResourceService>();
        object selection;
        private string initLanguage;
        static NLog.Logger log = NLog.LogManager.GetCurrentClassLogger();
        public object Selection
        {
            get { return selection; }
            set
            {
                SetProperty(ref selection, value);
            }
        }
        public Command CloseCommand { get; }
        public SwitchLanguageViewModel()
        {
            Selection = initLanguage = uIResourceService.GetCurrentCultureName();
            CloseCommand = new Command(OnCloseClicked);
        }

        private async void OnCloseClicked(object obj)
        {
            try
            {
                if (initLanguage != Selection?.ToString())
                {
                    await uIResourceService.SwitchLanguage(Selection?.ToString());
                }
                await Navigation.PopPopupAsync();
            }
            catch (Exception ex)
            {
                log.Error(ex, ex.Message);
            }
        }

    }
}
