﻿using Common.DAL.Methods;
using Hub.Model;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Common.Model;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hub.Service
{
    public class ClientLogService
    {
        private static string baseDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ClientLogs");
        private static string[] permittedExtensions = { ".zip" };
        static ClientLogService()
        {
            if (!Directory.Exists(baseDir))
            {
                Directory.CreateDirectory(baseDir);
            }
        }
        /// <summary>
        /// app日志上传
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task Upload(ClientLogDto request)
        {
            if (request?.File?.Length > 0)
            {
                var fileName = Path.GetFileName(request.File.FileName);
                var ext = Path.GetExtension(fileName).ToLowerInvariant();
                if (string.IsNullOrEmpty(ext) || !permittedExtensions.Contains(ext))
                {
                    throw new MessageException("file type is not supported");  
                }
                else
                {
                    string fileDir = Path.Combine(baseDir, Guid.NewGuid().ToString("N"));
                    if (!Directory.Exists(fileDir))
                    {
                        Directory.CreateDirectory(fileDir);
                    }
                    string filePath = Path.Combine(fileDir, fileName);
                    using (var stream = File.Create(filePath))
                    {
                        await request.File.CopyToAsync(stream);
                    }
                    string desFilePath = Path.Combine(fileDir, "description.txt");
                    using (var stream = File.CreateText(desFilePath))
                    {
                        stream.WriteLine(JsonConvert.SerializeObject(
                            new { Name = HttpContextHelper.Current?.User?.Identity?.Name ?? "sys", DeviceInfo = request }
                            ));
                    }
                }
            }
            else
            {
                throw new MessageException("file is empty");
            } 
        }
    }
}
