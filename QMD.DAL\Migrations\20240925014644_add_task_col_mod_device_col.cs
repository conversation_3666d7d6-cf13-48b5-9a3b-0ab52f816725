﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_task_col_mod_device_col : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "TarCompileTime",
                table: "emt_involvedevice",
                type: "longtext",
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "CompileTime",
                table: "emt_involvedevice",
                type: "longtext",
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<DateTime>(
                name: "EndFinishedTime",
                table: "emt_executetask",
                type: "datetime(6)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "MiddleFinishedTime",
                table: "emt_executetask",
                type: "datetime(6)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "StartFinishedTime",
                table: "emt_executetask",
                type: "datetime(6)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EndFinishedTime",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "MiddleFinishedTime",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "StartFinishedTime",
                table: "emt_executetask");

            migrationBuilder.AlterColumn<DateTime>(
                name: "TarCompileTime",
                table: "emt_involvedevice",
                type: "datetime(6)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "longtext",
                oldNullable: true)
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<DateTime>(
                name: "CompileTime",
                table: "emt_involvedevice",
                type: "datetime(6)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "longtext",
                oldNullable: true)
                .OldAnnotation("MySql:CharSet", "utf8mb4");
        }
    }
}
