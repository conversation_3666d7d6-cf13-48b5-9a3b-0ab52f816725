﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace Hub.Mobile.CommonControl
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class HybridPage : ContentPage
    {
        bool loadSuccess = false;
        public HybridPage(string title,string url)
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
            this.Title = title;
            this.hybridWebView.Source = url;
            this.hybridWebView.Navigated += HybirdWebView_Navigated;
        }
        private void HybirdWebView_Navigated(object sender, WebNavigatedEventArgs e)
        {
            if (e?.Result == WebNavigationResult.Success)
            {
                loadSuccess = true;
            }
        }
        public virtual async Task OnBackFire(Action<HybridPage> cancelCallBack)
        {
            try
            {
                if (loadSuccess)
                {
                    hybridWebView.Eval(@" try {
                hub_mobile_drive.pressBackCallBack();
            } catch (err) {
                mobileBack();
            }");
                }
                else
                {
                    if (hybridWebView.CanGoBack)
                    {
                        hybridWebView.GoBack();
                    }
                    else
                    {
                        await Navigation.PopAsync();
                    }
                }
            }
            catch (Exception ex)
            {

            }
            finally
            {
                cancelCallBack(this);
            }
        }
    }
}