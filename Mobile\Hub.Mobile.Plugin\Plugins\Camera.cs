﻿using Hub.Mobile.Const;
using Hub.Mobile.Interface;
using Hub.Mobile.Utility;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Plugin.Media;
using Plugin.Media.Abstractions;
using Plugin.Permissions;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Xamarin.Forms;

namespace Hub.Mobile.Plugin.Plugins
{
    public class Camera : IPlugin
    {
        public class Option
        {
            private int? compressionQuality;
            public int? CompressionQuality
            {
                get
                {
                    return compressionQuality == null || compressionQuality <= 0 ? 90 : compressionQuality.Value;
                }
                set { compressionQuality = value; }
            }
            private int? maxWidthHeight;
            public int? MaxWidthHeight
            {
                get
                {
                    return maxWidthHeight == null || maxWidthHeight <= 0 ? 800 : maxWidthHeight.Value;
                }
                set { maxWidthHeight = value; }
            }
            public bool SaveToAlbum { get; set; }
            public bool MultiSelect { get; set; }
            public bool NeedPosition { get; set; }
        }
        public class SaveAlbumOption
        {
            public string Url { get; set; }
            public string FileName { get; set; }
            public string Md5 { get; set; }
        }
        /// <summary>
        /// 拍照
        /// </summary>
        /// <param name="serverName"></param>
        /// <param name="action"></param>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task<ExecResult> TakePhoto(string args)
        {

            ExecResult result = new ExecResult();
            var ret = new JObject();
            try
            {
                Option option = new Option();
                if (!string.IsNullOrWhiteSpace(args))
                {
                    option = JsonConvert.DeserializeObject<Option>(args);
                }
                if (option.NeedPosition)
                {
                    //需要位置信息
                    var position = await MobileGPSHelper.GetPosition();
                    if (!position.Item1)
                    {
                        result.Message = position.Item2;
                        return result;
                    }
                    else
                    {
                        var positionJob = new JObject();
                        positionJob["Latitude"] = position.Item3.Latitude;
                        positionJob["Longitude"] = position.Item3.Longitude;
                        positionJob["Altitude"] = position.Item3.Altitude;
                        ret["Position"] = positionJob;
                    }
                }
                var media = CrossMedia.Current;
                if (await PermissionHelper.CheckPermission<StoragePermission>())
                {
                    if (!media.IsCameraAvailable || !media.IsTakePhotoSupported)
                    {
                        result.Message = !media.IsCameraAvailable ? "Camera unavailable" : "Take photo unsupported";
                        return result;
                    }
                    if (await PermissionHelper.CheckPermission<CameraPermission>())
                    {
                        var file = await media.TakePhotoAsync(new StoreCameraMediaOptions()
                        {
                            PhotoSize = PhotoSize.MaxWidthHeight,
                            CompressionQuality = option.CompressionQuality.Value,
                            MaxWidthHeight = option.MaxWidthHeight.Value,
                            Name = $"IMG_{DateTime.Now.ToString("yyyyMMdd")}_{DateTime.Now.ToString("HH:mm:ss")}.jpg",
                            SaveToAlbum = option.SaveToAlbum
                        });
                        if (file != null && File.Exists(file.Path))
                        {
                            ret["Photo"] = DependencyService.Get<IImageService>().ImageToBase64(file.Path);
                            File.Delete(file.Path);
                        }
                        else
                        {
                            ret["Photo"] = "";
                        }
                        result.Result = ret.ToString();
                        result.Success = true;
                        return result;
                    }
                    else
                    {
                        result.Message = "No Permission to access the camera device";
                        return result;
                    }
                }
                else
                {
                    result.Message = "No Storage Permission";
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = ex?.Message;
            }
            return result;
        }
        public async Task<ExecResult> PickPhoto(string args)
        {
            ExecResult result = new ExecResult();
            try
            {
                Option option = new Option();
                if (!string.IsNullOrWhiteSpace(args))
                {
                    option = JsonConvert.DeserializeObject<Option>(args);
                }
                if (await PermissionHelper.CheckPermission<StoragePermission>())
                {
                    var media = CrossMedia.Current;
                    if (!media.IsPickPhotoSupported)
                    {
                        result.Message = "Pick photo unsupported";
                        return result;
                    }
                    List<MediaFile> files;
                    if (option.MultiSelect)
                    {
                        files = await media.PickPhotosAsync(new PickMediaOptions()
                        {
                            PhotoSize = PhotoSize.MaxWidthHeight,
                            CompressionQuality = option.CompressionQuality.Value,
                            MaxWidthHeight = option.MaxWidthHeight.Value,
                        });
                    }
                    else
                    {
                        files = new List<MediaFile>();
                        files.Add(await media.PickPhotoAsync(new PickMediaOptions()
                        {
                            PhotoSize = PhotoSize.MaxWidthHeight,
                            CompressionQuality = option.CompressionQuality.Value,
                            MaxWidthHeight = option.MaxWidthHeight.Value,
                        }));
                    }
                    List<string> lstRet = new List<string>();
                    if (files?.Count > 0)
                    {
                        foreach (var file in files)
                        {
                            if (file != null && File.Exists(file.Path))
                            {
                                lstRet.Add(DependencyService.Get<IImageService>().ImageToBase64(file.Path));
                                File.Delete(file.Path);
                            }
                        }
                    }
                    result.Success = true;
                    result.Result = JsonConvert.SerializeObject(lstRet);
                }
                else
                {
                    result.Message = "No Storage Permission";
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = ex?.Message;
            }
            return result;
        }
        public async Task<ExecResult> SaveToAlbum(string args)
        {
            SaveAlbumOption option = JsonConvert.DeserializeObject<SaveAlbumOption>(args);
            ExecResult result = new ExecResult() { IsString = true };
            if (string.IsNullOrWhiteSpace(option.Md5))
            {
                result.Success = false;
                result.Message = "md5 is empty";
                return result;
            }
            if (string.IsNullOrWhiteSpace(option.Url))
            {
                result.Success = false;
                result.Message = "download url is empty";
                return result;
            }
            if (await PermissionHelper.CheckPermission<StoragePermission>())
            {
                string fileName = string.IsNullOrWhiteSpace(option.FileName) ? Path.GetFileName(new Uri(option.Url).AbsolutePath) : option.FileName;
                try
                {
                    var check = DependencyService.Get<IImageService>().ImageExistInAlbum(fileName,option.Md5);
                    if (check.Item1)
                    {
                        result.Result = check.Item2;
                        result.Success = true;
                        return result;
                    }
                }
                catch (Exception ex)
                {

                }
                string filePath = await Task.Run(() => { return DependencyService.Get<IDownload>().DownloadFile(option.Url, "", fileName); });
                result.Result = DependencyService.Get<IImageService>().SaveToAlbum(filePath);
                result.Success = true;
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }
            else
            {
                result.Success = false;
                result.Message = "No Storage Permission";
            }
            return result;
        }
    }
}
