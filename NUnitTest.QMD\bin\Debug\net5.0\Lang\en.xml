﻿<?xml version="1.0" encoding="utf-8"?>
<Resource>
	<!-- Report Order -->
	<Module name="RepOrder">
		<Control name="test">Test</Control>
		<Control name="GetNetProductLineMsg">An exception occurred while obtaining production line information:</Control>
		<Control name="GetReportOrderFormDataMsg">An exception occurred while obtaining the initial data for the change order form:</Control>
		<Control name="SearchUserByEmailOrNameMsg">An exception occurred while obtaining personnel information:</Control>
		<Control name="GetHisNoticeTasksMsg">Incorrect parameters, data retrieval failed.</Control>
		<Control name="ExportHisNoticeTasksMsg">Incorrect parameters, data retrieval failed.</Control>
		<Control name="LblNetName">Network Name</Control>
		<Control name="LblRegionDept">Region/Department</Control>
		<Control name="LblProvinceCity">Province/City</Control>
		<Control name="LblProductSpe">Network Specialty</Control>
		<Control name="LblOrderNo">Order Number</Control>
		<Control name="LblTaskNo">Implementation Order Number</Control>
		<Control name="LblOutputLines">Output Line</Control>
		<Control name="LblProductLines">Product Line</Control>
		<Control name="LblOrderTypes">Order Type</Control>
		<Control name="Enum_割接">Cutover</Control>
		<Control name="Enum_通知">Notification</Control>
		<Control name="Enum_风险预警">Risk Warning</Control>
		<Control name="Enum_网络整改">Network Rectification</Control>
		<Control name="Enum_网络检查">Network Inspection</Control>
		<Control name="Enum_改造">Renovation</Control>
		<Control name="Enum_升级">Upgrade</Control>
		<Control name="Enum_测试">Test</Control>
		<Control name="Enum_重保">Critical Protection</Control>
		<Control name="Enum_数据变更">Data Change</Control>
		<Control name="Enum_其他">Other</Control>
		<Control name="Enum_技术通知">Technical Notification</Control>
		<Control name="Enum_割接_国际">Cutover_International</Control>
		<Control name="Enum_网络整改_国际_国际">Network Rectification_International</Control>
		<Control name="Enum_网络检查_国际">Network Inspection_International</Control>
		<Control name="Enum_升级_国际">Upgrade_International</Control>
		<Control name="Enum_测试_国际">Testing_International</Control>
		<Control name="LblRiskLevels">Risk Level</Control>
		<Control name="Const_一级(紧急)">Level 1 (Urgent)</Control>
		<Control name="Const_一级">Level 1</Control>
		<Control name="Const_二级(紧急)">Level 2 (Urgent)</Control>
		<Control name="Const_二级">Level 2</Control>
		<Control name="Const_三级(紧急)">Level 3 (Urgent)</Control>-
		<Control name="Const_三级">Level 3</Control>
		<Control name="Const_四级(紧急)">Level 4 (Urgent)</Control>
		<Control name="Const_四级">Level 4</Control>
		<Control name="LblOrderStatus">Order Status</Control>
		<Control name="Enum_待提交">Pending Submission</Control>
		<Control name="Enum_一线接口人审核">First-line Interface Person Review</Control>
		<Control name="Enum_一线接口人审核不通过">First-line Interface Person Review Not Passed</Control>
		<Control name="Enum_二线接口人审核">Second-line Interface Person Review</Control>
		<Control name="Enum_二线接口人审核不通过">Second-line Interface Person Review Not Passed</Control>
		<Control name="Enum_三线接口人审核">Third-line Interface Person Review</Control>
		<Control name="Enum_三线接口人审核不通过">Third-line Interface Person Review Not Passed</Control>
		<Control name="Enum_LMT经理审核">LMT Manager Review</Control>
		<Control name="Enum_LMT经理审核不通过">LMT Manager Review Not Passed</Control>
		<Control name="Enum_一线主管授权">First-line Supervisor Authorization</Control>
		<Control name="Enum_一线主管授权不通过">First-line Supervisor Authorization Not Passed</Control>
		<Control name="Enum_销售授权">Sales Authorization</Control>
		<Control name="Enum_销售授权不通过">Sales Authorization Not Passed</Control>
		<Control name="Enum_客户授权">Customer Authorization</Control>
		<Control name="Enum_客户授权不通过">Customer Authorization Not Passed</Control>
		<Control name="Enum_实施中">In Progress</Control>
		<Control name="Enum_已完成">Completed</Control>
		<Control name="Enum_到期">Expired</Control>
		<Control name="Enum_系统超时自动一线审核不通过">System Timeout Automatic First-line Review Not Passed</Control>
		<Control name="Enum_系统超时自动二线线审核不通过">System Timeout Automatic Second-line Review Not Passed</Control>
		<Control name="Enum_系统超时自动三线审核不通过">System Timeout Automatic Third-line Review Not Passed</Control>
		<Control name="Enum_系统超时自动LMT审核不通过">System Timeout Automatic LMT Review Not Passed</Control>
		<Control name="Enum_系统超时自动主管授权不通过">System Timeout Automatic Supervisor Authorization Not Passed</Control>
		<Control name="Enum_系统超时自动销售授权不通过">System Timeout Automatic Sales Authorization Not Passed</Control>
		<Control name="Enum_系统超时自动客户授权不通过">System Timeout Automatic Customer Authorization Not Passed</Control>
		<Control name="LblOrderTitle">Order Title</Control>
		<Control name="LblPlanStarttime">Planned Start Time</Control>
		<Control name="LblPlanEndtime">Planned End Time</Control>
		<Control name="LblRealStartTime">Actual Start Time</Control>
		<Control name="LblRealEndtime">Actual End Time</Control>
		<Control name="LblSubmitTime">Submit Time</Control>
		<Control name="LblMaxSupport">The highest level of protection</Control>
		<Control name="LblTaskPlanStarttime">Task Start Time</Control>
		<Control name="LblTaskPlanEndtime">Task End Time</Control>
		<Control name="GetConditionEx">An exception occurred while obtaining the query conditions for personal order dispatch:</Control>
		<Control name="Enum_总部派发">Headquarters Dispatch</Control>
		<Control name="Enum_一线派发">First-line Dispatch</Control>
		<Control name="Enum_其他">Other</Control>
		<Control name="Enum_客户邮件待发送">Customer Email Pending Sending</Control>
		<Control name="Enum_客户邮件发送成功">Customer Email Sent Successfully</Control>
		<Control name="Enum_客户邮件发送失败">Customer Email Sending Failed</Control>
		<Control name="Enum_待提交">Pending Submission</Control>
		<Control name="Enum_已提交">Submitted</Control>
		<Control name="Enum_待实施">Pending Implementation</Control>
		<Control name="Enum_内部授权不通过">Internal Authorization Failed</Control>
		<Control name="Enum_客户授权通过">Customer Authorization Passed</Control>
		<Control name="Enum_客户授权不通过">Customer Authorization Failed</Control>
		<Control name="Enum_授权附件已传">Authorization Attachment Uploaded</Control>
		<Control name="Enum_失败">Failed</Control>
		<Control name="Enum_成功">Success</Control>
		<Control name="Enum_取消">Cancelled</Control>
		<Control name="Enum_到期未反馈">Expired without Feedback</Control>
		<Control name="GetPersonReportOrdersEx">An exception occurred while retrieving the personal change order list:</Control>
		<Control name="Enum_是">Yes</Control>
		<Control name="Enum_否">No</Control>
		<Control name="Enum_自主发起">Self-Initiated</Control>
		<Control name="Enum_任务派发">Task Dispatch</Control>
		<Control name="GetReportOrderEx">An exception occurred while retrieving the change order [{0}]:</Control>
		<Control name="GetReportOrdersFail">Failed to retrieve the change order. The corresponding order information was not found. The order number is:</Control>
		<Control name="GetReportOrdersEx">An exception occurred while retrieving the change order:</Control>
		<Control name="GetTaskOrdersFail">Failed to retrieve the implementation order. The corresponding order information was not found.</Control>
		<Control name="GetTaskOrdersEx">An exception occurred while retrieving the implementation order:</Control>
		<Control name="GetApproversFail">Failed to retrieve the reviewer information for the change order. The corresponding order information was not found. The order number is:</Control>
		<Control name="Enum_一线接口人">First-Line Interface Person</Control>
		<Control name="Enum_二线技术接口人">Second-Line Technical Interface Person</Control>
		<Control name="Enum_三线技术接口人">Third-Line Technical Interface Person</Control>
		<Control name="Enum_LMT经理">LMT Manager</Control>
		<Control name="Enum_授权主管">Authorization Supervisor</Control>
		<Control name="Enum_授权销售">Authorized Sales</Control>
		<Control name="Enum_授权客户">Authorized Customer</Control>
		<Control name="Enum_技服中心人员">Technical Service Center Staff</Control>
		<Control name="GetApproversEx">An exception occurred while retrieving the reviewer list:</Control>
		<Control name="GetCustomersEx">An exception occurred while retrieving customer information under the corresponding network in the Technical Service Center:</Control>
		<Control name="GetChangeUsersEx">An exception occurred while retrieving the change approval person list:</Control>
		<Control name="GetDepartmentNetsEx">An exception occurred while retrieving the network data of the Technical Service Center:</Control>
		<Control name="SetReportOrderFail">Only internal employees can create orders. Please have the network leader or internal personnel create the order.</Control>
		<Control name="SetReportOrderFailNoFound">Failed to update the change order. The corresponding order information was not found.</Control>
		<Control name="SetReportOrderFailStatus">The current order status is {0}, and it cannot be edited.</Control>
		<Control name="SetReportOrderFailCreater">The current user is not the order applicant and cannot edit it.</Control>
		<Control name="SetReportOrderFailObjects">There are duplicate implementation objects. Each object only needs one row. Please check [Details - Change Workload - Implementation Object]</Control>
		<Control name="SetReportOrderAddSuccess">Successfully added a new change order. The order number is:</Control>
		<Control name="SetReportOrderUpdateSuccess">Successfully updated the change order. The order number is:</Control>
		<Control name="SetReportOrderDbEx">Failed to set the change order. The order number is:</Control>
		<Control name="SetReportOrderEx">An exception occurred while setting the change order. The order number is:</Control>
		<Control name="CopyReportOrder">[Copy]</Control>
		<Control name="CopySuccess">Successfully copied the change order. The order number is:</Control>
		<Control name="CopyDbEx">Failed to copy the report order:</Control>
		<Control name="CopyEx">An exception occurred while copying the report order:</Control>
		<Control name="SubmitFailNull">Failed to submit the change order. The corresponding order information was not found.</Control>
		<Control name="SubmitFailCreater">You are not the order applicant and cannot submit the change order.</Control>
		<Control name="SubmitFailApprover">The first-line approval interface person cannot be empty. Please select the first-line approval interface person.</Control>
		<Control name="SubmitFailManager">The internal authorizer cannot be empty. Please select the internal authorizer.</Control>
		<Control name="SubmitSuccess">The report order [{0}] has been submitted successfully. The next step is for the first-line interface person ({1}) to approve it.</Control>
		<Control name="SubmitDbEx">An exception occurred while submitting the report order data to the database. The order number is:</Control>
		<Control name="SubmitEx">An exception occurred while submitting the report order. The order number is:</Control>
		<Control name="SubmitFailStatus">Failed to submit the report order. The current status of the order is:</Control>
		<Control name="UploadFailNofile">No customer authorization attachment has been uploaded. Please upload the attachment.</Control>
		<Control name="UploadFailCreater">The current user is not the order applicant and does not have permission to upload attachments for customer authorization. The order applicant is:</Control>
		<Control name="UploadFailTime">You need to wait {0} hours and {1} minutes before uploading the customer authorization attachment.</Control>
		<Control name="UploadFailStatus">The current order status does not allow uploading customer authorization attachments for customer authorization. The status is:</Control>
		<Control name="UploadFailStart">The start time of the current order has passed, and customer authorization cannot be performed. Customer authorization will be automatically reviewed and rejected by the system.</Control>
		<Control name="UploadSuccess">Successfully uploaded the attachment for customer authorization. The order status has entered "In Progress."</Control>
		<Control name="UploadDbSuccess">Successfully uploaded the customer authorization attachment. The order number is:</Control>
		<Control name="UploadDbEx">A database exception occurred while uploading the customer authorization attachment:</Control>
		<Control name="UploadNotice">The proactive maintenance report order is approved by the applicant through uploading the customer authorization attachment information. The order enters the "In Progress" status. Please create the implementation task promptly. The order number is:</Control>
		<Control name="UploadFailNo">The corresponding report order number does not exist. The order number is:</Control>
		<Control name="UploadFailEx">An exception occurred while uploading the customer authorization attachment:</Control>
		<Control name="ApprovalFailNull">Failed to approve the change order. The corresponding order information was not found.</Control>
		<Control name="ApprovalFailStatus">The current status of the order [{0}] is {1}, and the corresponding operation cannot be performed.</Control>
		<Control name="ApprovalFailOperator">The current status of the order [{0}] is {1}, and the operator is {2}.</Control>
		<Control name="ApprovalFailHandle">The order [{0}] has already been approved by the current user as: {1}. It cannot be approved again.</Control>
		<Control name="Enum_通过">Approved</Control>
		<Control name="Enum_不通过">Rejected</Control>
		<Control name="ApprovalSucess">Successfully approved the order [{0}], but since the order has expired, the system automatically rejected it.</Control>
		<Control name="ApprovalDbEx">An exception occurred while approving the report order:</Control>
		<Control name="ApprovalNotice">The proactive maintenance report order [{0}] has exceeded the planned start time and has been automatically reviewed by the system. The review result is automatically set to: Rejected.</Control>
		<Control name="ApprovalNoticeEx">An exception occurred while sending notifications for the change order review:</Control>
		<Control name="ApprovalEx">An exception occurred while approving the current order:</Control>
		<Control name="ChangeFailNull">Failed to switch the reviewer: The email of the new reviewer cannot be empty.</Control>
		<Control name="ChangeRecord">The status of the change order [{0}][{1}] has switched the original reviewer {2} to the new reviewer {3}</Control>
		<Control name="ChangeNotice">The current status of the change order [{0}] is: {1} ({2})</Control>
		<Control name="Enum_待审批">Pending Approval</Control>
		<Control name="Enum_待授权">Pending Authorization</Control>
		<Control name="ChangeSuccess">Successfully switched the reviewer for the change order [{0}].</Control>
		<Control name="ChangeDbEx">Error switching the approver for Change Order [{0}]:</Control>
		<Control name="ChangeNoticeEx">Exception occurred while sending notification for approver switch:</Control>
		<Control name="ChangeEx">Exception occurred while switching the approver for Change Order [{0}]:</Control>
		<Control name="ApprovalFailInvUser">The relevant personnel for the order cannot be empty. Please select the required personnel information during approval.</Control>
		<Control name="ApprovalFailInvSale">Sales personnel cannot be empty. Please select a salesperson.</Control>
		<Control name="ApprovalNoticeAuto">The proactive maintenance change order [{0}] has exceeded the planned start time, triggering automatic customer review. The review result is automatically set to: Customer Rejected.</Control>
		<Control name="ApprovalNoticeSubject">Maintenance Change Order Authorization</Control>
		<Control name="ApprovalMailEx">Exception occurred while sending an email to Customer [{1}] for authorization of Change Order [{0}]: {2}</Control>
		<Control name="ApprovalNotice">The proactive maintenance change order [{0}] has been authorized by sales, with the sales authorizer being: {1}</Control>
		<Control name="ApprovalContent">The current status of the proactive maintenance change order [{0}] is: {1} ({2})</Control>
		<Control name="ApprovalNoticeContent">The proactive maintenance change order [{0}] has been reviewed by user {1}, with the result being: {2}</Control>
		<Control name="ApprovalSuccess">Approval of the change order was successful. The order number is:</Control>
		<Control name="ApprovalDbEx">An error occurred while approving the change order. The order number is:</Control>
		<Control name="ApprovalNoticeEx">Exception occurred while sending notification for the change order review:</Control>
		<Control name="ApprovalEx">Exception occurred while approving the change order. The order number is:</Control>
		<Control name="DeleteNone">Please select a record to delete.</Control>
		<Control name="DeleteLimit">Please select at most one record to delete.</Control>
		<Control name="DeleteStatus">The current status of the order does not allow deletion. The status is:</Control>
		<Control name="DeleteTasks">The change order to be deleted has ongoing implementation tasks. Please complete the implementation tasks before deletion. The task number is:</Control>
		<Control name="DeleteSuccess">The order was successfully deleted:</Control>
		<Control name="DeleteDbEx">Failed to delete the change order. The order number is:</Control>
		<Control name="DeleteApplyUser">You are not the applicant for the current change order and cannot delete the order data.</Control>
		<Control name="DeleteEx">An exception occurred while deleting the change order:</Control>
		<Control name="ConfirmStatus">The current status of the order does not allow completion. Only orders in the "In Progress" status can be completed. The current status is:</Control>
		<Control name="ConfirmCharger">The user is not the network leader for this order and cannot complete it. The network leader for this order is:</Control>
		<Control name="ConfirmTasks">There are ongoing implementation tasks under the current change order. Please complete the tasks before finalizing the change order.</Control>
		<Control name="ConfirmMsg">Are you sure you want to complete this change order?</Control>
		<Control name="ConfirmEx">An exception occurred while completing the change order:</Control>
		<Control name="FinishSubject">Maintenance Change Order Completion Notification - Order Number:</Control>
		<Control name="FinishMailEx">Exception occurred while sending an email to Customer [{1}] for authorization of Change Order [{0}]: {2}</Control>
		<Control name="FinishNotice">User {0} ({1}) has completed the proactive maintenance change order [{2}] in the QMO system.</Control>
		<Control name="FinishSuccess">Successfully completed the change order.</Control>
		<Control name="FinishDbEx">Failed to complete the change order. The order number is:</Control>
		<Control name="FinishNoticeEx">An exception occurred while sending notification for the completed change order. The order number is:</Control>
		<Control name="FinishEx">An exception occurred while completing the change order. The order number is:</Control>
		<Control name="ResendFailNone">Failed to resend customer email: The corresponding order was not found.</Control>
		<Control name="ResendFailStatus">The current status of the change order does not allow sending customer emails. Only orders in the "Customer Authorization" status can resend emails. The current status is:</Control>
		<Control name="ResendFailCustomer">The current order does not have customer information set and cannot resend emails.</Control>
		<Control name="ResendSubject">Maintenance Change Order Authorization</Control>
		<Control name="ResendSuccess">Successfully resent the email to the customer for the change order.</Control>
		<Control name="ResendMailEx">Exception occurred while resending the email to Customer [{1}] for authorization of Change Order [{0}]: {2}</Control>
		<Control name="ResendEx">Failed to resend customer email:</Control>
		<Control name="ExpireSuccess">The order expiration process was completed successfully. Total count: {0}, Order number: {1}</Control>
		<Control name="ExpireDbEx">Error occurred during the order expiration process:</Control>
		<Control name="ExpireEx">Exception occurred during the order expiration process:</Control>
		<Control name="FlowEx">Exception occurred while retrieving the order process information:</Control>
		<Control name="LogEx">Exception occurred while retrieving the order process log:</Control>
		<Control name="ExtPropEx">Exception occurred while retrieving extended attributes:</Control>
		<Control name="Exp_TimeZoneId">The time zone information of the current network is empty. Please contact the administrator to fill it in.</Control>
		<Control name="Exp_NetName">Network Name</Control>
		<Control name="Exp_Region">Region</Control>
		<Control name="Exp_DepName">Office</Control>
		<Control name="Exp_Province">Province</Control>
		<Control name="Exp_City">City</Control>
		<Control name="Exp_Operator">Operator</Control>
		<Control name="Exp_ProductSpe">Specialty</Control>
		<Control name="Exp_ChargePersonName">Network Leader's Name</Control>
		<Control name="Exp_ChargePersonEmail">Network Leader's Email</Control>
		<Control name="Exp_OrderNo">Order Number</Control>
		<Control name="Exp_OrderTypeStr">Order Type</Control>
		<Control name="Exp_OutputLine">Output Line</Control>
		<Control name="Exp_ProductLine">Product Line</Control>
		<Control name="Exp_ProductLMT">Product Line LMT</Control>
		<Control name="Exp_RiskLevel">Risk Level</Control>
		<Control name="Exp_PlanStartTime">Planned Start Time</Control>
		<Control name="Exp_PlanEndTime">Planned End Time</Control>
		<Control name="Exp_LineRemark">Additional Product Information</Control>
		<Control name="Exp_Title">Order Subject</Control>
		<Control name="Exp_TaskContent">Task Description</Control>
		<Control name="Exp_AttachInfo">Attachment Information</Control>
		<Control name="Exp_IsReinstatedStr">Is Reinforced</Control>
		<Control name="Exp_UpgradeReason">Upgrade Reason</Control>
		<Control name="Exp_SoftwareForm">Software Form</Control>
		<Control name="Exp_OrderStatusStr">Order Status</Control>
		<Control name="Exp_CustomerMailStr">Customer Email Status</Control>
		<Control name="Exp_DisOrderNo">Maintenance Task Number</Control>
		<Control name="Exp_SubmitTime">Submission Time</Control>
		<Control name="Exp_RealEndTime">Actual Completion Time</Control>
		<Control name="Exp_NmpStr">Network Management Information</Control>
		<Control name="Exp_DeviceStr">Device Information</Control>
		<Control name="Exp_ProductStr">Change Workload</Control>
		<Control name="Exp_Customer">Customer Information</Control>
		<Control name="Exp_CustomerName">Customer Information - Customer Name</Control>
		<Control name="Exp_CustomerEmail">Customer Information - Customer Email</Control>
		<Control name="Exp_CustomerPhone">Customer Information - Customer Phone</Control>

		<Control name="Exp_MaxSurpport">Highest Level of Support</Control>
		<Control name="MaxLine_13">First Line</Control>
		<Control name="MaxLine_14">Second Line</Control>
		<Control name="MaxLine_15">Third Line</Control>
		<Control name="MaxLine_16">Fourth Line</Control>
		<Control name="Exp_CurUsers">Current Executor (Filing)</Control>
		<Control name="Exp_TaskUsers">Change Implementer</Control>
		<Control name="Exp_CheckUsers">Change Checker</Control>
		<Control name="Exp_GrtUsers">Support Person (Filing)</Control>
		<Control name="Exp_ApplyUser">Applicant (Filing)</Control>
		<Control name="Exp_FstApprover">First Line Approver</Control>
		<Control name="Exp_FstApproverDept">Department of First Line Approver</Control>
		<Control name="Exp_SecApprover">Second Line Approver</Control>
		<Control name="Exp_SecApproverDept">Department of Second Line Approver</Control>
		<Control name="Exp_ThdApprover">Third Line Approver</Control>
		<Control name="Exp_ThdApproverDept">Department of Third Line Approver</Control>
		<Control name="Exp_FouApprover">LMT Approver</Control>
		<Control name="Exp_FouApproverDept">Department of LMT Approver</Control>
		<Control name="Exp_CurUsers">Current Operator</Control>
		<Control name="Exp_CurUsersDepts">Department of Current Operator</Control>
		<Control name="Exp_TaskUsers">Implementer</Control>
		<Control name="Exp_TaskUsersDepts">Department of Implementer</Control>
		<Control name="Exp_CheckUsers">Checker</Control>
		<Control name="Exp_CheckUsersDepts">Department of Checker</Control>
		<Control name="Exp_ApplyUser">Applicant</Control>
		<Control name="Exp_ApplyUserDepts">Department of Applicant</Control>
		<Control name="Exp_FstApprover">First Line Approver</Control>
		<Control name="Exp_FstApproverDept">Department of First Line Approver</Control>
		<Control name="Exp_SecApprover">Second Line Approver</Control>
		<Control name="Exp_SecApproverDept">Department of Second Line Approver</Control>
		<Control name="Exp_ThdApprover">Third Line Approver</Control>
		<Control name="Exp_ThdApproverDept">Department of Third Line Approver</Control>
		<Control name="Exp_FouApprover">Fourth Line Approver</Control>
		<Control name="Exp_FouApproverDept">Department of Fourth Line Approver</Control>
		<Control name="Exp_Managers">Managerial Authorizer</Control>
		<Control name="Exp_ManagersDepts">Department of Managerial Authorizer</Control>
		<Control name="Exp_Salers">Sales Authorizer</Control>
		<Control name="Exp_SalersDepts">Department of Sales Authorizer</Control>
		<Control name="Exp_FstGrtUsers">First Line Support Person</Control>
		<Control name="Exp_FstGrtUsersDepts">Department of First Line Support Person</Control>
		<Control name="Exp_SecGrtUsers">Second Line Support Person</Control>
		<Control name="Exp_SecGrtUsersDepts">Department of Second Line Support Person</Control>
		<Control name="Exp_ThdGrtUsers">Third Line Support Person</Control>
		<Control name="Exp_ThdGrtUsersDepts">Department of Third Line Support Person</Control>
		<Control name="Exp_FouGrtUsers">Fourth Line Support Person</Control>
		<Control name="Exp_FouGrtUsersDepts">Department of Fourth Line Support Person</Control>
		
		<Control name="Exp_TaskNo">Implementation Task Number</Control>
		<Control name="Exp_TaskTitle">Implementation Task Title</Control>
		<Control name="Exp_OprTypeStr">Implementation Task Type</Control>
		<Control name="Exp_TaskTaskUsers">Implementation Executor</Control>
		<Control name="Exp_TaskCheckUsers">Implementation Checker</Control>
		<Control name="Exp_TaskGrtUsers">Implementation Guarantor</Control>
		<Control name="Exp_TaskPlanStartTime">Implementation Planned Start Time</Control>
		<Control name="Exp_TaskPlanMiddleTime">Implementation Planned Operation End Time</Control>
		<Control name="Exp_TaskPlanEndTime">Implementation Planned End Time</Control>
		<Control name="Exp_TaskRealStartTime">Implementation Actual Start Time</Control>
		<Control name="Exp_TaskRealEndTime">Implementation Actual End Time</Control>
		<Control name="Exp_SolutionContent">Implementation Plan</Control>
		<Control name="Exp_TaskStatusStr">Implementation Task Status</Control>
		<Control name="Exp_ObjectName">Implementation Object Name</Control>
		<Control name="Exp_PlanObjectCount">Implementation Planned Object Count</Control>
		<Control name="Exp_ActObjectCount">Implementation Actual Object Count</Control>
		<Control name="Exp_FileName">Export Change Order Data</Control>
		<Control name="Exp_SheetName">Change Order Data</Control>
		<Control name="ExpEx">Exception occurred while exporting change order list:</Control>
		<Control name="GetCountEx">Exception occurred while retrieving total counts for each dimension:</Control>
		<Control name="UserOrdersFailAdmin">The currently logged-in account is {0}, which is not an administrator and does not have permission to view data for user {1}.</Control>
		<Control name="UserOrdersFailType">The specified order type data cannot be retrieved.</Control>
		<Control name="UserOrdersFailEx">Exception occurred while retrieving order data related to the user:</Control>
		<Control name="ConversionFailNone">Please select at least one order to transfer.</Control>
		<Control name="ConversionFailUser">Both the original and new operators of the order cannot be empty.</Control>
		<Control name="ConversionFailSame">The original and new operators of the order are the same person, or the current user is not an administrator and has selected someone other than themselves as the original operator. No transfer is needed.</Control>
		<Control name="ConversionNotice">Due to a position change for user {0}, the proactive maintenance filing order has been transferred to new user {1}. Order number: {2}</Control>
		<Control name="ConversionSuccess">One-click order transfer successful. Number of orders executed this time:</Control>
		<Control name="ConversionDbEx">Order transfer failed:</Control>
		<Control name="ConversionMailEx">Exception occurred while sending notifications for personnel changes:</Control>
		<Control name="ConversionEx">Exception occurred during one-click order transfer:</Control>
		<Control name="ImpAngGrtOrdersEx">Exception occurred while retrieving order information related to the user:</Control>
		<Control name="TransferFailNone">No order has been selected for transfer. Please select at least one order to transfer.</Control>
		<Control name="TransferFailUser">The recipient of the order is the current user. Transfer failed.</Control>
		<Control name="TransferNotice">User {0} has initiated a transfer of the proactive maintenance filing order. The confirmed recipient is {1}. Order number: {2}</Control>
		<Control name="TransferSuccess">Order transfer successful. Waiting for the recipient to confirm receipt.</Control>
		<Control name="TransferDbEx">Error occurred while saving transfer data to the database during one-click order transfer:</Control>
		<Control name="TransferEx">Exception occurred during one-click order transfer:</Control>
		<Control name="TransferListEx">Exception occurred while retrieving transferred order data for the user:</Control>
		<Control name="ConfirmTransferNotice">User {0} ({1}) has confirmed the receipt of {2}. Order number: {3}</Control>
		<Control name="EnumReportOrder">Filing Order</Control>
		<Control name="EnumTaskOrder">Implementation Task</Control>
		<Control name="ConfirmTransferSuccess">Order receipt confirmed successfully.</Control>
		<Control name="ConfirmTransferDbEx">Error occurred while saving receipt confirmation data to the database:</Control>
		<Control name="ConfirmTransferEx">Exception occurred during order receipt confirmation:</Control>
		<Control name="VaildFailNet">No network has been selected for filing. Please select a network.</Control>
		<Control name="VaildFailTime">The local time is empty and cannot be used to apply for a filing order.</Control>
		<Control name="VaildFailPlanTime">The planned start and end times cannot be empty. Please select the times.</Control>
		<Control name="VaildFailForm">Failed to create a new order: Information for the order applicant, implementer, checker, and customer cannot be empty.</Control>
		<Control name="VaildFailUser">The implementer and checker for Level 1 and Level 2 orders cannot be the same user. Please reselect the implementer/checker.</Control>
		<Control name="VaildFailTitle">The order title is empty. Please enter a title for the order.</Control>
		<Control name="VaildFailPermission">The current user does not have permission to initiate a filing order for this network.</Control>
		<Control name="VaildFailStime">The planned start time cannot be earlier than the current time. Please reselect the planned start time.</Control>
		<Control name="VaildFailEtime">The planned start time cannot be later than or equal to the planned end time. Please reselect the planned times.</Control>
		<Control name="VaildFailLimit">The interval between the planned start and end times cannot exceed 30 days. Please reselect the times.</Control>
		<Control name="VaildFailDue">The current order level is {0}. An application must be submitted at least {1} hours in advance.</Control>
		<Control name="VaildFailProduct">Product and device information has not been filled in. Please enter the product information.</Control>
		<Control name="VaildFailAttach">The implementation plan attachment for filing has not been uploaded. Please upload the implementation plan attachment.</Control>
		<Control name="VaildSuccess">Order validation successful.</Control>
		<Control name="VaildEx">Exception occurred while validating the filing order:</Control>
		<Control name="SubValidMsg">The following required fields have not been filled in. Please complete all required fields before submission.</Control>
		<Control name="SubValidLevel">For Level 1 and Level 2 filing orders, the implementer and checker must be completely different. Please reselect the implementer and checker.</Control>
		<Control name="SubValidNullUserLevel">There are users with empty levels among the implementers/checkers. Please complete the user level information.</Control>
		<Control name="SubValidTime">The planned start time cannot be earlier than the current time. Please reselect the planned start time.</Control>
		<Control name="SubValidTimeLimit">The planned start time cannot be later than or equal to the planned end time. Please reselect the planned times.</Control>
		<Control name="SubValidDeadLine">The current order level is {0}. An application must be submitted at least {1} hours in advance.</Control>
		<Control name="SubValidSuccess">Order validation successful.</Control>
		<Control name="SubValidEx">Exception occurred while validating the filing order:</Control>
		<Control name="NoticeMailEx">Exception occurred while sending the filing order email. Order number:</Control>
		<Control name="NoticeDingEx">Exception occurred while sending the filing order via Fire Control Communication. Order number:</Control>
		<Control name="NoticeToMailEx">Failed to send email: {0}; Order object information: {1}</Control>
		<Control name="NoticeToDingEx">Exception occurred while sending the Fire Control Communication task for filing order [{0}]: {1}</Control>
		<Control name="ApprovalToDingEx">Exception occurred while completing the Fire Control Communication task:</Control>
		<Control name="Att_NetInfo">Network Information</Control>
		<Control name="Att_Province">Province</Control>
		<Control name="Att_City">City</Control>
		<Control name="Att_Operator">Operator</Control>
		<Control name="Att_ProductSpe">Network Specialty</Control>
		<Control name="Att_NetName">Network Name</Control>
		<Control name="Att_ChargePersonName">Network Leader's Name</Control>
		<Control name="Att_ChargePersonPhone">Network Leader's Phone</Control>
		<Control name="Att_ChargePersonEmail">Network Leader's Email</Control>
		<Control name="Att_OrderInfo">Order Basic Information</Control>
		<Control name="Att_OrderNo">Order Number</Control>
		<Control name="Att_OrderType">Order Type</Control>
		<Control name="Att_OutputLine">Output Line</Control>
		<Control name="Att_ProductLine">Product Line</Control>
		<Control name="Att_ProductLMT">Product Line LMT</Control>
		<Control name="Att_LineRemark">Additional Product Information</Control>
		<Control name="Att_RiskLevel">Risk Level</Control>
		<Control name="Att_PlanStartTime">Planned Start Time</Control>
		<Control name="Att_PlanEndTime">Planned End Time</Control>
		<Control name="Att_SubmitTime">Submission Time</Control>
		<Control name="Att_RepTitle">Order Subject</Control>
		<Control name="Att_TaskContent">Task Description</Control>
		<Control name="Att_NetNmpInfo">Network Management Information</Control>
		<Control name="Att_NetNmpDet">Current Network Management Series: {0}, Current Network Management Version: {1}, Current Network Management Patch: {2}, Target Network Management Series: {3}, Target Network Management Version: {4}, Target Network Management Patch: {5}</Control>
		<Control name="Att_ProductInfo">Product Information</Control>
		<Control name="Att_ProductDetail">Device Model: {0}, Board Name: {1}, Current Hardware Version: {2}, Current Software Version: {3}, Current Software Compilation Date: {4}, Target Hardware Version: {5}, Target Software Version: {6}, Target Compilation Date: {7}</Control>
		<Control name="Att_ChangeInfo">Change Information</Control>
		<Control name="Att_FillInfo">Filing Information</Control>
		<Control name="Att_FillDetail">Object Name: {0}, Object Quantity: {1}</Control>
		<Control name="Att_ProcessInfo">Progress Pre-filing</Control>
		<Control name="Att_UserInfo">Personnel Information</Control>
		<Control name="Att_ApplyUser">Applicant</Control>
		<Control name="Att_TaskUser">Implementer</Control>
		<Control name="Att_GrtUser">Guarantor</Control>
		<Control name="Att_DepGrtUser">First-Line Guarantor</Control>
		<Control name="Att_TacGrtUser">Second-Line Guarantor</Control>
		<Control name="Att_LmtGrtUser">Third-Line Guarantor</Control>
		<Control name="Att_TestGrtUser">Fourth-Line Guarantor</Control>
		<Control name="Att_Customer">Customer</Control>
		<Control name="Att_CheckUser">Checker</Control>
		<Control name="Att_CopyUser">CC Recipient</Control>
		<Control name="Att_DispatchInfo">Task Dispatch Information</Control>
		<Control name="Att_DisOrderNo">Dispatch Order Number</Control>
		<Control name="Att_DisTitle">Dispatch Order Title</Control>
		<Control name="Att_DisStartTime">Dispatch Order Start Time</Control>
		<Control name="Att_DisEndTime">Dispatch Order End Time</Control>
		<Control name="Att_DisRemark">Dispatch Order Remarks</Control>
		<Control name="Att_TaskInfo">Basic Information</Control>
		<Control name="Att_TaskNo">Implementation Order Number</Control>
		<Control name="Att_TaskSolution">Implementation Plan</Control>
		<Control name="Att_OprType">Operation Type</Control>
		<Control name="Att_TaskStartTime">Planned Start Time</Control>
		<Control name="Att_TaskEndTime">Planned End Time</Control>
		<Control name="Att_TaskSubTime">Last Submission Time</Control>
		<Control name="Att_CusotomTime">Customer Approval Deadline</Control>
		<Control name="Att_Coordinate">Coordination Required</Control>
		<Control name="Att_CustomerCoorp">Customer Coordination</Control>
		<Control name="Att_CooprContent">
			<p>1. Business Verification Coordination: Provide customer coordination test personnel information. For surrounding coordination, provide surrounding coordination personnel information.</p>
			<p>2. Data Center Access Authorization: Grant access authorization to the data center where the service site is located. Arrange for an escort if necessary.</p>
			<p>3. Network Access Authorization: Complete the network access review and authorization required for this service. If remote access is involved, provide the corresponding remote login information, such as login account, password, and IP address.</p>
			<p>4. Network Operation Authorization: Complete the review and authorization of the service purpose, delivery method, service plan, and implementation recording for this service.</p>
			<p>5. Equipment Password Provision: Provide the relevant accounts or passwords for the service site to complete the service implementation.</p>
			<p>6. Equipment Password Change: After the service is completed, promptly change the user passwords involved to ensure information security without vulnerabilities.</p>
		</Control>
		<Control name="ApprovalNewFailUser">The relevant personnel for the order cannot be empty. Please select the required personnel information during approval.</Control>
		<Control name="ApprovalNewFailSale">The relevant personnel for the order cannot be empty. Please select the required personnel information during approval.</Control>
		<Control name="ApprovalNewFailTime">Automatic customer review has been triggered due to exceeding the planned start time, and the review result is "Customer Rejected."</Control>
		<Control name="ApprovalNewFailNotice">The proactive maintenance filing order [{0}] has exceeded the planned start time, triggering automatic customer review. The review result is automatically set to "Customer Rejected."</Control>
		<Control name="ApprovalNewNotice">The proactive maintenance filing order [{0}] is pending customer authorization.</Control>
		<!-- Added on 20250306 -->
		<Control name="UserType_0">Applicant</Control>
		<Control name="UserType_1">Synchronization Informant</Control>
		<Control name="UserType_2">Implementer</Control>
		<Control name="UserType_3">Checker</Control>
		<Control name="UserType_4">Current Operator</Control>
		<Control name="UserType_5">Network Confirmer</Control>
		<Control name="UserType_6">Extension Approver</Control>
		<Control name="UserType_7">Order Receiver</Control>
		<Control name="UserType_8">Order Closer</Control>
		<Control name="UserType_9">First-line Supporter</Control>
		<Control name="UserType_10">Second-line Supporter</Control>
		<Control name="UserType_11">Third-line Supporter</Control>
		<Control name="UserType_12">Fourth-line Supporter</Control>
		<Control name="UserType_13">First-line Auditor</Control>
		<Control name="UserType_14">Second-line Auditor</Control>
		<Control name="UserType_15">Third-line Auditor</Control>
		<Control name="UserType_16">LMT Auditor</Control>
		<Control name="UserType_17">First-line Supervisor Authorizer</Control>
		<Control name="UserType_18">Sales Authorizer</Control>
		<Control name="UserType_19">Customer</Control>
		<Control name="UserType_20">Regional Supervisor Authorizer</Control>
		<Control name="OrderKind_1">Dispatch Order</Control>
		<Control name="OrderKind_2">Dispatch Network</Control>
		<Control name="OrderKind_3">Change Order</Control>
		<Control name="OrderKind_4">Implementation Task</Control>
		<Control name="OrderKind_5">Scheduled Task</Control>
		<Control name="EnumTransferStatus_0">Submitted</Control>
		<Control name="EnumTransferStatus_1">Confirmed</Control>
		<!--2025.3.20新增-->
		<Control name="MoreInfoStr">More devices...\r\nPlease go to the QMO system to view more device information</Control>
		<Control name="Att_Region">Region</Control>
		<Control name="Att_IsReinstated">Is reinstated</Control>
		<!--2025.4.16新增-->
		<Control name="TransferStatus_0">Submitted</Control>
		<Control name="TransferStatus_1">Confirmed</Control>
		<Control name="OrderStatus_1">Pending Submission</Control>
		<Control name="OrderStatus_2">First-line Interface Person Review</Control>
		<Control name="OrderStatus_3">First-line Interface Person Review Not Passed</Control>
		<Control name="OrderStatus_4">Second-line Interface Person Review</Control>
		<Control name="OrderStatus_5">Second-line Interface Person Review Not Passed</Control>
		<Control name="OrderStatus_6">Third-line Interface Person Review</Control>
		<Control name="OrderStatus_7">Third-line Interface Person Review Not Passed</Control>
		<Control name="OrderStatus_8">LMT Manager Review</Control>
		<Control name="OrderStatus_9">LMT Manager Review Not Passed</Control>
		<Control name="OrderStatus_10">First-line Supervisor Authorization</Control>
		<Control name="OrderStatus_11">First-line Supervisor Authorization Not Passed</Control>
		<Control name="OrderStatus_12">Sales Authorization</Control>
		<Control name="OrderStatus_13">Sales Authorization Not Passed</Control>
		<Control name="OrderStatus_14">Customer Authorization</Control>
		<Control name="OrderStatus_15">Customer Authorization Not Passed</Control>
		<Control name="OrderStatus_16">In Progress</Control>
		<Control name="OrderStatus_17">Completed</Control>
		<Control name="OrderStatus_18">Expired</Control>
		<Control name="TaskStatus_0">Pending Submission</Control>
		<Control name="TaskStatus_1">Submitted</Control>
		<Control name="TaskStatus_2">Pending Implementation</Control>
		<Control name="TaskStatus_3">Internal Authorization Failed</Control>
		<Control name="TaskStatus_4">Customer Authorization Passed</Control>
		<Control name="TaskStatus_5">Customer Authorization Failed</Control>
		<Control name="TaskStatus_6">Authorization Attachment Uploaded</Control>
		<Control name="TaskStatus_7">Failed</Control>
		<Control name="TaskStatus_8">Success</Control>
		<Control name="TaskStatus_9">Cancelled</Control>
	</Module>
	<Module name="DisOrder">
		<Control name="FormDataEx">An exception occurred while obtaining the initial data for the dispatch order form:</Control>
		<Control name="ImpDeviceEx">An exception occurred while importing device data:</Control>
		<Control name="AddFailUser">Non-internal employees do not have permission to create dispatch orders.</Control>
		<Control name="AddFailPermission">The current user does not have permission to initiate dispatch tasks. Interface personnel, supervisors, or managers are required to add. Please contact the administrator if needed.</Control>
		<Control name="AddFailAttach">The current dispatch task plan has not been uploaded. Please upload the plan attachment.</Control>
		<Control name="AddSuccess">Successfully saved the dispatch order. The order number is:</Control>
		<Control name="AddDbEx">An exception occurred while saving the new dispatch order data to the database:</Control>
		<Control name="AddEx">An exception occurred while creating the dispatch order:</Control>
		<Control name="UpdateFailNull">Failed to update the dispatch order. The corresponding order information was not found.</Control>
		<Control name="UpdateFailUser">Non-internal employees do not have permission to update dispatch orders.</Control>
		<Control name="UpdateFailStatus">The current order status does not allow editing. The current status is:</Control>
		<Control name="UpdateFailApply">The current user is not the order applicant and cannot edit. The order applicant is:</Control>
		<Control name="OrderStatus_0">Unsubmitted</Control>
		<Control name="OrderStatus_1">Pending Second-Line Review</Control>
		<Control name="OrderStatus_2">Second-Line Rejected</Control>
		<Control name="OrderStatus_3">First-Line Interface Person Confirmed</Control>
		<Control name="OrderStatus_4">Network Leader Confirmed</Control>
		<Control name="OrderStatus_5">In Progress</Control>
		<Control name="OrderStatus_6">Completed</Control>
		<Control name="OrderStatus_7">Revoked</Control>
		<Control name="OrderStatus_8">Closed</Control>
		<Control name="UpdateFailAttach">The current dispatch task plan has not been uploaded. Please upload the plan attachment.</Control>
		<Control name="UpdateSuccess">Successfully saved the dispatch order. The order number is:</Control>
		<Control name="UpdateDbEx">An exception occurred while saving the updated dispatch order data to the database. The order number is:</Control>
		<Control name="UpdateEx">An exception occurred while updating the dispatch order. The order number is:</Control>
		<Control name="LblFuzzy">Fuzzy Search</Control>
		<Control name="LblFuzzyHold">Order Number/Order Subject/Product Model</Control>
		<Control name="LblTitle">Order Subject</Control>
		<Control name="LblStatus">Order Status</Control>
		<Control name="LblNetName">Network Name</Control>
		<Control name="LblRegionDept">Region/Department</Control>
		<Control name="LblProvinceCity">Province/City</Control>
		<Control name="LblProductSpe">Network Specialty</Control>
		<Control name="LblRealStartTime">Actual Start Time</Control>
		<Control name="LblRealEndTime">Actual End Time</Control>
		<Control name="LblOrderNo">Order Number</Control>
		<Control name="LblOutputLine">Output Line</Control>
		<Control name="LblProductLine">Product Line</Control>
		<Control name="LblOrderType">Order Type</Control>
		<Control name="LblRiskLevel">Risk Level</Control>
		<Control name="LblPlanStartTime">Planned Start Time</Control>
		<Control name="LblPlanEndTime">Planned End Time</Control>
		<Control name="LblSubmitTime">Submit Time</Control>
		<Control name="LblMaxSupport">Max Surpport Level</Control>
		<Control name="ConditionEx">An exception occurred while obtaining query conditions for personal dispatch orders:</Control>
		<Control name="Const_高级">High-Level</Control>
		<Control name="Const_中级">Medium-Level</Control>
		<Control name="Const_低级">Low-Level</Control>
		<Control name="OrderType_1">Cutover</Control>
		<Control name="OrderType_2">Notification</Control>
		<Control name="OrderType_3">Risk Alert</Control>
		<Control name="OrderType_4">Network Rectification</Control>
		<Control name="OrderType_5">Network Inspection</Control>
		<Control name="OrderType_6">Renovation</Control>
		<Control name="OrderType_7">Upgrade</Control>
		<Control name="OrderType_8">Test</Control>
		<Control name="OrderType_9">Critical Protection</Control>
		<Control name="OrderType_10">Data Change</Control>
		<Control name="OrderType_11">Other</Control>
		<Control name="OrderType_12">Technical Notification</Control>
		<Control name="OrderKind_1">Headquarters Dispatch</Control>
		<Control name="OrderKind_2">First-Line Dispatch</Control>
		<Control name="OrderKind_3">Other</Control>
		<Control name="NetSource_1">Headquarters Intelligent Matching</Control>
		<Control name="NetSource_2">Headquarters Selection</Control>
		<Control name="NetSource_3">First-Line Intelligent Matching</Control>
		<Control name="NetSource_4">First-Line Selection</Control>
		<Control name="NetStatus_0">Unsubmitted</Control>
		<Control name="NetStatus_1">Second-Line Review</Control>
		<Control name="NetStatus_2">Second-Line Rejected</Control>
		<Control name="NetStatus_3">First-Line Interface Person Confirmed</Control>
		<Control name="NetStatus_4">Network Leader Confirmed</Control>
		<Control name="NetStatus_5">In Progress</Control>
		<Control name="NetStatus_6">Completed</Control>
		<Control name="NetStatus_7">Revoked</Control>
		<Control name="NetStatus_8">Closed</Control>
		<Control name="IsChange_0">Pending</Control>
		<Control name="IsChange_1">Change</Control>
		<Control name="IsChange_2">No Change</Control>
		<Control name="DelayStatus_0">No Delay</Control>
		<Control name="DelayStatus_1">Delay Request</Control>
		<Control name="DelayStatus_2">Delay Rejected</Control>
		<Control name="DelayStatus_3">Delay Approved</Control>
		<Control name="NetChooseType_0">Manual Selection</Control>
		<Control name="NetChooseType_1">Intelligent Selection</Control>
		<Control name="NetChooseType_2">Manual Selection</Control>
		<Control name="Const_Charger">Network Leaders</Control>
		<Control name="Const_InterPerson">Interface Personnel</Control>
		<Control name="GetPersonDisOrdersEx">An exception occurred while obtaining the list of personal dispatch orders:</Control>
		<Control name="GetDisOrderNetPageListEx">An exception occurred while obtaining the network list for personal dispatch orders:</Control>
		<Control name="ApproverFailTac">The second-line supervisor is not set, and the second-line reviewer cannot be selected.</Control>
		<Control name="ApproverEx">An exception occurred while obtaining the reviewer list:</Control>
		<Control name="UserPermissionFailNull">Failed to obtain the corresponding dispatch order information.</Control>
		<Control name="UserPermissionFailUser">The current user is not the order applicant and does not have permission to submit the dispatch order.</Control>
		<Control name="UserPermissionFailStatus">The current order status does not allow submission. The current status is:</Control>
		<Control name="UserPermissionMsg">The current dispatch task will be reviewed by the second-line supervisor after submission. Do you confirm the submission?</Control>
		<Control name="UserPermissionDirectMsg">The current dispatch task will be directly sent to the first-line interface person after submission. Do you confirm the submission?</Control>
		<Control name="UserPermissionEx">An exception occurred while obtaining user permission information:</Control>
		<Control name="GetOrderFailNull">Failed to obtain the dispatch order. The corresponding order information was not found.</Control>
		<Control name="GetOrderEx">An exception occurred while obtaining the dispatch order details:</Control>
		<Control name="GetOrdersEx">An exception occurred while obtaining the dispatch order details:</Control>
		<Control name="OnExecutingEx">An exception occurred while obtaining the maintenance tasks currently being executed by the user:</Control>
		<Control name="SubmitFailNull">Failed to submit the dispatch task. The corresponding order information was not found.</Control>
		<Control name="SubmitFailUser">You are not the order applicant and cannot submit the dispatch task. The current order applicant is:</Control>
		<Control name="SubmitFailTac">Submission failed: No second-line approver (TAC supervisor) selected.</Control>
		<Control name="SubmitApprovalNotice">User {0} ({1}) has created a maintenance dispatch task that requires your review. The dispatch order number is: {2}</Control>
		<Control name="SubmitFailNets">No network information has been selected. Please select a network before submitting.</Control>
		<Control name="SubmitCopyNotice">Notification of proactive maintenance dispatch order in the QMO system: User {0} has newly published a proactive maintenance dispatch task with order number: {1}</Control>
		<Control name="SubmitDepNotice">User {0} ({1}) has created a maintenance dispatch task that requires you to confirm the network. Dispatch order number: {2}</Control>
		<Control name="SubmitSuccess">The dispatch order has been submitted successfully.</Control>
		<Control name="SubmitDbEx">An exception occurred while submitting data to the database:</Control>
		<Control name="SubmitEx">An exception occurred while submitting the dispatch task:</Control>
		<Control name="SubmitFailStatus">The dispatch order [{0}] is currently in the status of {1} and cannot be submitted.</Control>
		<Control name="ValidAFailOrder">Failed to determine if the A role is configured correctly. The corresponding dispatch order was not obtained.</Control>
		<Control name="ValidAFailDept">
			The A role for the {2} network line in the {1} specialty of {0} has not been configured.<br />
		</Control>
		<Control name="ValidAEx">An exception occurred while verifying the configuration of the A role:</Control>
		<Control name="CurUserFailNull">Failed to obtain the current user information. The current user has not been synchronized to the QMO system. Please contact the administrator for synchronization.</Control>
		<Control name="CurUserEx">Failed to obtain the current user information:</Control>
		<Control name="ValidOrderFailNet">The current dispatch order does not contain network data related to you. You do not need to process this dispatch order.</Control>
		<Control name="ValidOrderFailStatus">The current sub-order is in the status of {0}, and the corresponding operation cannot be performed.</Control>
		<Control name="ValidOrderFailRole">You are not the interface person and do not have permission to approve the dispatch order or select the network for the dispatch order.</Control>
		<Control name="ValidOrderEx">An exception occurred while verifying the maintenance dispatch order:</Control>
		<Control name="ApprovalFailNull">The corresponding dispatch order information was not found.</Control>
		<Control name="ApprovalFailStatus">The current order status is {0}, and the approval operation cannot be performed.</Control>
		<Control name="ApprovalEx">An exception occurred while approving the current dispatch task:</Control>
		<Control name="ApprovalNetFailNull">The corresponding dispatch order information was not found.</Control>
		<Control name="ApprovalNetFailUser">The approver of this order is {0}. The current user does not have permission to perform the approval operation.</Control>
		<Control name="ApprovalNetFailNet">No network information has been selected for implementing this dispatch task. Please select a network before submitting.</Control>
		<Control name="ApprovalNetFailChoose">No approver has been selected for switching. Please make a selection.</Control>
		<Control name="ApprovalNetFailChange">The approver has not changed. Please select a different approver.</Control>
		<Control name="ApprovalNetNewNotice">User {0} has switched the approver to the new user {1}. The new user {1} has a new dispatch task order that requires approval with the order number: {2}.</Control>
		<Control name="ApprovalNetNewDbSuc">Successfully switched the approver.</Control>
		<Control name="ApprovalNetNewDbEx">Approval failed due to a database error while saving the data:</Control>
		<Control name="ApprovalNetResNotice">The approval result for maintenance dispatch order {0}{1} is {2}, with the approval comment: {3}</Control>
		<Control name="ApprovalStatus_0">Rejected</Control>
		<Control name="ApprovalStatus_1">Approved</Control>
		<Control name="ApprovalStatus_2">Rejected</Control>
		<Control name="ApprovalNetRecNotice">User {0} ({1}) has created a maintenance dispatch task that requires you to confirm the network. Dispatch order number: {2}</Control>
		<Control name="ApprovalNetResSuc">Successfully approved the dispatch order.</Control>
		<Control name="ApprovalNetResDbEx">Failed to approve the dispatch order:</Control>
		<Control name="ApprovalNetResEx">An exception occurred while approving the current dispatch task:</Control>
		<Control name="ConfFailRole">The current user is not the interface person and does not have permission to confirm the network data.</Control>
		<Control name="ConfFailRole1">The current user is not the recipient of the order and cannot confirm the network data.</Control>
		<Control name="ConfFailNet">The network confirmed by the network leader cannot be removed. {0}</Control>
		<Control name="ConfNotice">You have a maintenance dispatch task that needs to be implemented. Please check the dispatch order number: {0}</Control>
		<Control name="ConfSuccess">Network confirmation successful.</Control>
		<Control name="ConfDbEx">Network confirmation failed for the Technical Service Center [{0}]: {1}</Control>
		<Control name="ConfEx">An exception occurred while confirming the current dispatch task:</Control>
		<Control name="ExtPropEx">An exception occurred while obtaining extended attributes:</Control>
		<Control name="InvOrderEx">An exception occurred while obtaining the dispatch order involving related devices:</Control>
		<Control name="RecordEx">An exception occurred while obtaining the operation log of the dispatch order. Order number:</Control>
		<Control name="FlowEx">An exception occurred while obtaining the order process information:</Control>
		<Control name="RevokeFailUser">The current user is not the order applicant {0} and cannot revoke the dispatch order.</Control>
		<Control name="RevokeFailStatus">The current status of the dispatch order is {0}, and revocation is not required.</Control>
		<Control name="RevokeNotice">User {0} has revoked the dispatch task. The dispatch task has been terminated, and the dispatch order number is: {1}</Control>
		<Control name="RevokeSuccess">Successfully revoked the order.</Control>
		<Control name="RevokeDbEx">Failed to revoke the order. Order number:</Control>
		<Control name="RevokeEx">An exception occurred while revoking the order. Order number:</Control>
		<Control name="Att_OrderNo">Order Number</Control>
		<Control name="Att_OrderStatus">Order Status</Control>
		<Control name="Att_OrderType">Order Type</Control>
		<Control name="Att_NetType">Net Type</Control>
		<Control name="Att_Title">Order Issue Subject</Control>
		<Control name="Att_OutputLine">Output Line</Control>
		<Control name="Att_ProductLine">Product Line</Control>
		<Control name="Att_LineRemark">Product Line Additional Information</Control>
		<Control name="Att_RiskLevel">Risk Level</Control>
		<Control name="Att_SubmitTime">Submission Time</Control>
		<Control name="Att_PlanStartTime">Planned Start Time</Control>
		<Control name="Att_PlanEndTime">Planned Completion Time</Control>
		<Control name="Att_Approver">Current Approver</Control>
		<Control name="Att_ApplyName">Applicant Name</Control>
		<Control name="Att_ApplyEmail">Applicant Email</Control>
		<Control name="Att_DisOrderNo">Dispatch Order Number</Control>
		<Control name="Att_NetName">Network Name</Control>
		<Control name="Att_ProductSpe">Net Major</Control>
		<Control name="Att_Status">Sub-order Status</Control>
		<Control name="Att_Percent">Completion Percentage</Control>
		<Control name="Att_ReqStartTime">Request Start Time</Control>
		<Control name="Att_ReqEndTime">Request End Time</Control>
		<Control name="Att_IsChange">Necessary To Implement</Control>
		<Control name="Att_Liaison">Liaison</Control>
		<Control name="Att_Charger">Net Charger</Control>
		<Control name="Att_PlanStartTime">Planned Start Time</Control>
		<Control name="Att_PlanEndTime">Planned End Time</Control>
		<Control name="Att_Operator">Operator</Control>
		<Control name="Att_Region">Region</Control>
		<Control name="Att_DepName">Technical Service Center</Control>
		<Control name="Att_Province">Province</Control>
		<Control name="Att_City">City</Control>
		<Control name="Exp_FileName">Export Dispatch Order Data</Control>
		<Control name="ExpEx">Exception occurred while exporting personal dispatch order list:</Control>
		<Control name="DeleteFailUser">The current user is not the order applicant and cannot delete the dispatch order.</Control>
		<Control name="DeleteFailStatus">The current order status is {0}, and the dispatch order cannot be deleted.</Control>
		<Control name="DeleteSuccess">Dispatch order deleted successfully.</Control>
		<Control name="DeleteDbEx">Failed to delete dispatch order:</Control>
		<Control name="DeleteEx">Exception occurred while deleting dispatch order, order number is:</Control>
		<Control name="DelayFailRole">The current user is not a second-line supervisor or interface role and has no permission to obtain the delay application list.</Control>
		<Control name="DelayEx">Exception occurred while obtaining the delay application data list:</Control>
		<Control name="DelayAprFailNone">Please select the delay application that needs to be approved.</Control>
		<Control name="DelayAprNotice">Second-line supervisor approval result for the delay sub-order: {0}, approved network: {1} ({2}), approval content: {3}</Control>
		<Control name="DelayAprSuccess">Delay application approved successfully, order number:</Control>
		<Control name="DelayAprDbEx">Failed to approve delay application, error message:</Control>
		<Control name="DelayAprEx">Exception occurred while approving delay data, exception message:</Control>
		<Control name="GetNetProcessEx">Exception occurred while obtaining the dispatch network progress data list, dispatch order number:</Control>
		<Control name="FillProcessFailStatus">The current dispatch task status is {0}, and reporting cannot be performed.</Control>
		<Control name="FillProcessFailNet">The current dispatch network sub-order status is {0}, and reporting cannot be performed.</Control>
		<Control name="FillProcessFailPer">The current user has no reporting permission. Please contact user {0} or {1} for reporting.</Control>
		<Control name="FillProcessFailProd">Workload not reported, please report workload.</Control>
		<Control name="FillProcessFailSum">The total progress of the current network reporting exceeds 100, and reporting fails.</Control>
		<Control name="FillProcessSuccess">Dispatch sub-order progress reporting successful.</Control>
		<Control name="FillProcessDbEx">Progress reporting failed:</Control>
		<Control name="FillProcessNull">No new progress data.</Control>
		<Control name="FillProcessEx">Dispatch network order progress reporting failed, order number:</Control>
		<Control name="GetNetPlanInfoEx">Exception occurred while reporting the plan, exception message:</Control>
		<Control name="SetPlanFailUser">The current user is not the interface person or responsible person for this network and has no permission to report the plan for this network.</Control>
		<Control name="SetPlanFailStatus">The current dispatch task status is {0}, and the plan cannot be edited.</Control>
		<Control name="SetPlanFailNetStatus">The current dispatch network sub-order status is {0}, and the plan cannot be edited.</Control>
		<Control name="SetPlanSucMsg">Plan set successfully.</Control>
		<Control name="SetPlanSucMsg1">Plan set successfully, but the planned completion time exceeds the required time for the dispatch task, and an automatic delay application has been generated.</Control>
		<Control name="SetPlanDbEx">Failed to report the plan:</Control>
		<Control name="SetPlanEx">Exception occurred while reporting the plan, exception message:</Control>
		<Control name="NoNeedFailUser">The current user is not the confirmation person or interface person for this dispatch network sub-order and cannot report the relevant information.</Control>
		<Control name="NoNeedFailStatus">The current dispatch order status is {0}, and the relevant information cannot be reported.</Control>
		<Control name="NoNeedFailNetStatus">The current dispatch sub-order status is {0}, and the relevant information cannot be reported.</Control>
		<Control name="NoNeedSuccess">Dispatch network sub-order reporting of non-implementation information successful.</Control>
		<Control name="NoNeedDbEx">Failed to report non-implementation information, error message:</Control>
		<Control name="NoNeedEx">Exception occurred while confirming the reporting of non-implementation information for the network, exception message:</Control>
		<Control name="StartRepFailStatus">The current dispatch task status is {entity.OrderStatus.GetDescription()}, and reporting cannot be initiated.</Control>
		<Control name="StartRepFailNetStatus">The current dispatch task network sub-order status is {netRelEntity.Status.GetDescription()}, and reporting cannot be initiated.</Control>
		<Control name="StartRepFailUser">The current user is not the interface person or responsible person for this network and cannot initiate reporting.</Control>
		<Control name="StartRepEx">Exception occurred while initiating reporting for the current task plan:</Control>
		<Control name="GetNetSearchLineEx">Exception occurred while obtaining the production line query conditions:</Control>
		<Control name="GetWhyIINetOrganizationEx">Exception occurred while obtaining the network production line-related data for the maintenance domain:</Control>
		<Control name="GetNetSerachNetEx">Exception occurred while intelligently querying network data:</Control>
		<Control name="GetNetSearchDeviceCacheEx">Exception occurred while obtaining device-related data:</Control>
		<Control name="GetNetSearchNmpCacheEx">Exception occurred while obtaining network management-related data:</Control>
		<Control name="GetWhyIINetOrganizationCacheEx">Exception occurred while obtaining network production line data:</Control>
		<Control name="ValidFailTime">The planned start time cannot be later than the planned end time. Please select a different time.</Control>
		<Control name="ValidFailTitle">The dispatch order title cannot be empty. Please enter the dispatch order title.</Control>
		<Control name="ValidFailFile">The dispatch task implementation plan attachment cannot be empty. Please upload the dispatch task implementation plan attachment.</Control>
		<Control name="ValidSuccess">Validation of dispatch order data successful.</Control>
		<Control name="ValidEx">Exception occurred while validating the dispatch order:</Control>
		<Control name="SendNoticeToMailEx">Failed to send email: {0}; Order object information: {1}</Control>
		<Control name="SendNoticeToFenghuotongEx">Exception occurred while sending the Fenghuotong to-do for the report order [{0}]: {1}</Control>
		<Control name="Att_OrderInfo">Order Basic Information</Control>
		<Control name="Att_OrderNo">Order Number</Control>
		<Control name="Att_OrderType">Order Type</Control>
		<Control name="Att_OutputLine">Output Line</Control>
		<Control name="Att_ProductLine">Product Line</Control>
		<Control name="Att_ProductLmt">Product Line LMT</Control>
		<Control name="Att_LineRemark">Product Line Additional Information</Control>
		<Control name="Att_RiskLevel">Risk Level</Control>
		<Control name="Att_PlanStartTime">Planned Start Time</Control>
		<Control name="Att_PlanEndTime">Planned End Time</Control>
		<Control name="Att_Title">Order Subject</Control>
		<Control name="Att_SolutionContent">Task Description</Control>
		<Control name="Att_NmpInfo">Network Management Information</Control>
		<Control name="Att_NmpContent">Current Network Management Series: {0}, Current Network Management Version: {1}, Current Network Management Patch: {2}, Target Network Management Series: {3}, Target Network Management Version: {4}, Target Network Management Patch: {5}</Control>
		<Control name="Att_DevInfo">Product Information</Control>
		<Control name="Att_DevContent">Device Model: {0}, Board Name: {1}, Current Hardware Version: {2}, Current Software Version: {3}, Current Software Compilation Date: {4}, Target Hardware Version: {5}, Target Software Version: {6}, Target Compilation Date: {7}</Control>
		<Control name="Att_ChangeInfo">Change Information</Control>
		<Control name="Att_ApplyInfo">Applicant Information</Control>
		<Control name="Att_ApplyUser">Applicant</Control>
		<Control name="CalStatusFailDone">All networks have completed the dispatch tasks, and the dispatch order status has been automatically updated to "Completed."</Control>
		<Control name="CalStatusFailStatus">All networks have formulated implementation plans, and the entire dispatch task has entered the implementation phase.</Control>
		<Control name="CalStatusDbEx">Failed to report the plan:</Control>
		<Control name="CalStatusEx">Exception occurred while calculating the completion status of the dispatch order:</Control>
		<Control name="GetPermissionNetsEx">Exception occurred while obtaining network data within permission:</Control>
	</Module>

	<!--权限-->
	<Module name="Permission">
		<Control name="RoleAlreadyExistsMsg">The same role name already exists</Control>
		<Control name="AddOrUpdateRoleEx">An exception occurred while AddOrUpdateRole：</Control>
		<Control name="DeleteRolesEx">An exception occurred while DeleteRoles：</Control>
		<Control name="TransferRoleTree">The role information list cannot be empty</Control>
		<Control name="AddOrUpdateFunctionEx">An exception occurred while AddOrUpdateFunction：</Control>
		<Control name="DeleteFunctionsEx">An exception occurred while DeleteFunctions：</Control>
		<Control name="QueryUsersByRoleWithoutPaginationEx">An exception occurred while QueryUsersByRoleWithoutPagination：</Control>
		<Control name="SavePermissionByRoleMsg">The format of the parent network permission is incorrect</Control>
		<Control name="SavePermissionByRoleTansEx">An exception occurred while SavePermissionByRole ：</Control>
		<Control name="SavePermissionByRoleEx">An exception occurred while SavePermissionByRole ：</Control>
		<Control name="GetDicByRoleIdListEx">An exception occurred while GetDicByRoleIdList ：</Control>

		<Control name="RoleApproveType_经理人">Manager</Control>
		<Control name="RoleApproveType_接口人">Contact Person</Control>
		<Control name="RoleApproveType_网络负责人">Network Manager</Control>
		<Control name="RoleApproveType_工程师">Engineer</Control>
		<Control name="RoleApproveType_主管">Supervisor</Control>
		<Control name="RoleDataType_一线">First-tier</Control>
		<Control name="RoleDataType_二线">Second-tier</Control>
		<Control name="RoleDataType_三线">Third-tier</Control>
		<Control name="RoleDataType_LMT">LMT</Control>
		<Control name="UserNetType_默认">Default</Control>
		<Control name="UserNetType_负责派发网络">Distribution Network</Control>
	</Module>

	<!--实施工单-->
	<Module name="ExcuteTask">
		<Control name="OrderNoEmpty">The OrderNo Is Empty</Control>
		<Control name="ReportOrderEmpty">The Report Order Is Empty</Control>
		<Control name="CodeSkEmpty">The CodeSk Is Empty</Control>
		<Control name="UserNoPermission">The current user has no network permission</Control>
		<Control name="UserNoOrderPermission">The current user does not have network permission for this order</Control>
		<Control name="HasNoUserInfo">No current online user information found</Control>
		<Control name="QueryExecuteTasksByOrderEx">An exception occurred while QueryExecuteTasksByOrderEx：</Control>
		<Control name="CopyTask_Params_Msg">The parameter to CopyTask cannot be empty</Control>
		<Control name="CopyTask_ReportOrder_Msg">The current Order does not exist or has been deleted</Control>
		<Control name="CopyTask_Applicant_Msg">The order applicant is empty, please verify</Control>
		<Control name="CopyTask_NoPermission">The applicant and the current login user are inconsistent, and the order cannot be copied</Control>
		<Control name="CopyTask_OrderNo_Msg">The task order number is invalid</Control>
		<Control name="CopyTask_Submit_Trans_Ex">An exception occurred while writing data to database(CopyTask):</Control>
		<Control name="CopyTask_Submit_Ex">An exception occurred while submit(CopyTask):</Control>
		<Control name="JudgeDeviceCount_Params_Msg">The parameter to CopyTask cannot be empty</Control>
		<Control name="JudgeDeviceCount_Msg1">Product model:{0},single disk name:{1},objectName:{2} order of the device does not exist</Control>
		<Control name="JudgeDeviceCount_Msg2">Product model:{0},single disk name:{1},objectName:{2} The total number of {3} exceeds the number of {4}</Control>
		<Control name="JudgeDeviceCountEx">An exception occurred while JudgeDeviceCount：</Control>
		<Control name="EditAndAddExecuteTasks_Params_Msg">The parameters for submitting the implementation task are empty!</Control>
		<Control name="EditAndAddExecuteTasks_Task_Msg">The submitted implementation task is empty!</Control>
		<Control name="EditAndAddExecuteTasks_ReportOrder_Msg">The current change order does not exist or has been deleted!</Control>
		<Control name="EditAndAddExecuteTasks_orderApplyUser_Msg">The application for change of work order is empty, please check!</Control>
		<Control name="EditAndAddExecuteTasks_Permission_Msg1">Non-owned employees have no permission to create and implement work orders</Control>
		<Control name="EditAndAddExecuteTasks_Permission_Msg2">The applicant who changes the work order is inconsistent with the current user, and the applicant has no right to add or modify the implementation work order</Control>
		<Control name="EditAndAddExecuteTasks_Permission_Msg3">If the applicant for the implementation of the work order is inconsistent with the applicant for the change of the work order, the applicant has no right to add or modify the implementation work order</Control>
		<Control name="EditAndAddExecuteTasks_Permission_Msg4">The status of the change work order is not [under implementation] or [due], and the implementation work order cannot be added or modified</Control>
		<Control name="EditAndAddExecuteTasks_Permission_Msg5">Only the work orders in the "to be submitted" and "Internal authorization failed" states can be modified</Control>
		<Control name="EditAndAddExecuteTasks_Permission_Msg6">Implementation task: The interval between the scheduled start time and the scheduled end time of [{0}] is more than 24 hours!</Control>
		<Control name="EditAndAddExecuteTasks_Permission_Msg7">Implementation task :[Operation start time] of [{0}] is not within the planned time range of the change work order!</Control>
		<Control name="EditAndAddExecuteTasks_Permission_Msg8">Implementation task :[Business confirmation start time] of [{0}] is not within the planned time range of the change work order!</Control>
		<Control name="EditAndAddExecuteTasks_Permission_Msg9">Implementation task: the [scheduled completion time] of [{0}] is not within the planned time range of the change work order!</Control>
		<Control name="EditAndAddExecuteTasks_Permission_Msg10">One or more implementation work orders that are being modified are deleted. Close them and try again</Control>
		<Control name="EditAndAddExecuteTasks_Permission_Msg11">The work order number of the task is invalid</Control>
		<Control name="EditAndAddExecuteTasks_Permission_Msg12">An exception occurred while writing data to database(EditAndAddExecuteTasks):</Control>
		<Control name="EditAndAddExecuteTasks_Permission_Msg13">An exception occurred while submit(EditAndAddExecuteTasks):</Control>
		<Control name="EditAndAddExecuteTasks_Permission_Msg14">For task implementation, the following must be satisfied: Planned completion time > Business confirmation planned start time > Operation planned start time</Control>
		<Control name="GetTplByOperTypeMsg">The template of implementation type {0} is not queried</Control>
		<Control name="GetTplByOperTypeEx">Obtain the implementation task template. If the process is abnormal, check the background logs</Control>
		<Control name="QueryTaskDetail_Msg1">Check that the parameters of the implementation task are empty!</Control>
		<Control name="QueryTaskDetail_Msg2">The current change order does not exist or has been deleted!</Control>
		<Control name="QueryTaskDetail_Msg3">The current task does not exist or has been deleted</Control>
		<Control name="QueryTaskDetail_Ex">GetTaskDetail method error, please check background log</Control>
		<Control name="QueryAuthorizationAttachments_Params_Msg">view the authorization attached parameter is empty!!</Control>
		<Control name="QueryAuthorizationAttachments_OrderNoExist_Msg">the current change order does not exist or be deleted! </Control>
		<Control name="SubmitTaskAthorizeAttachments_Msg1"> modify the authorization attached parameter is empty! </Control>
		<Control name="SubmitTaskAthorizeAttachments_Msg2">the current change order does not exist or be deleted  </Control>
		<Control name="SubmitTaskAthorizeAttachments_Msg3"> applicant is empty, to change the work order, please check! </Control>
		<Control name="SubmitTaskAthorizeAttachments_Msg4"> applicant is empty to implement the work order, please check! </Control>
		<Control name="SubmitTaskAthorizeAttachments_Msg5"> the current operation and the order of the applicant or the applicant for the implementation of the repair order, do not allow the operation! </Control>
		<Control name="SubmitTaskAthorizeAttachments_Msg6"> implementation work order does not exist or be deleted! </Control>
		<Control name="SubmitTaskAthorizeAttachments_Msg7"> the current implementation of the repair order status is not "submitted", does not allow new authorization accessories! </Control>
		<Control name="SubmitTaskAthorizeAttachments_Msg8"> work order status is "success", are not allowed to modify the authorization accessories! </Control>
		<Control name="GetManagerApprover_Msg">If no interface data matching the conditions is found, contact the first-line supervisor for configuration.</Control>
		<Control name="GetManagerApprover_Ex">An exception occurred while GetManagerApprover：</Control>
		<Control name="SubmitExecuteTask_Params_Msg">The parameters for submitting the task are empty!</Control>
		<Control name="SubmitExecuteTask_OrderNoExist_Msg">The change number for submitting the implementation task does not exist!</Control>
		<Control name="SubmitExecuteTask_Select_Msg">Please select at least one implementation order!</Control>
		<Control name="SubmitExecuteTask_Order_Msg">One or more current changers do not exist or have been deleted!</Control>
		<Control name="SubmitExecuteTask_Status_Msg">The current implementation is not allowed to submit because one or more states are not [to be submitted] or [internal authorization is not passed]</Control>
		<Control name="SubmitExecuteTask_OvertimOrder_Msg">The current time has exceeded the planned completion time, the specific work order is as follows :{0}, not allowed to operate!</Control>
		<Control name="SubmitExecuteTask_Msg1">The single or multiple applicants of the current change work are inconsistent with the current logon, and there is no permission to complete</Control>
		<Control name="SubmitExecuteTask_Msg2">One or more applications for the current change job are empty. Please check!</Control>
		<Control name="SubmitExecuteTask_Msg3">One or more applicants of the current implementation are inconsistent with the current logon, and there is no permission to complete</Control>
		<Control name="SubmitExecuteTask_Msg4">The current implementation of one or more applications is empty, please check!</Control>
		<Control name="SubmitExecuteTask_OvertimOrder_TranEx">An exception occurs when the task is submitted to the database:</Control>
		<Control name="SubmitExecuteTask_OvertimOrderEx">An exception occurred while SubmitExecuteTask:</Control>
		<Control name="FinishExecuteTask_Msg1">The parameter for completing the implementation task is empty!</Control>
		<Control name="FinishExecuteTask_Msg2">The current change order does not exist or has been deleted! </Control>
		<Control name="FinishExecuteTask_Msg3">Implementation task applicant is empty</Control>
		<Control name="FinishExecuteTask_Msg4">Implementation task implementation manual empty</Control>
		<Control name="FinishExecuteTask_Msg5">Implementation task verification is empty</Control>
		<Control name="FinishExecuteTask_Msg6">The current user is not the implementer or verifier of the implementation work order, and has no operation authority! </Control>
		<Control name="FinishExecuteTask_Msg7">Implementation work order does not exist or has been deleted!</Control>
		<Control name="FinishExecuteTask_Msg8">The status of the current implementation work order is not [to be implemented], and operation</Control>
		<Control name="FinishExecuteTask_Msg9">The current implementation work order status is [{0}], it is not allowed to cancel </Control>
		<!--<Control name="FinishExecuteTask_Msg10">There are uncompleted required processes, unable to complete the operation </Control>-->
		<Control name="FinishExecuteTask_Msg10_1">No template information was obtained for the template ID {0}</Control>
		<Control name="FinishExecuteTask_Msg10_2">The relationship between task number: {0} and template ID: {1} does not exist. Please contact the administrator for handling.</Control>
		<Control name="FinishExecuteTask_Msg10_3">An exception occurred when verifying whether the required process steps were completed. Please check the background log.</Control>
		<Control name="FinishExecuteTask_Msg11">Please fill in the failure reason in the remarks!</Control>
		<Control name="FinishExecuteTask_Msg12">Please fill in the remaining questions in the remarks!</Control>
		<Control name="FinishExecuteTask_Msg13">Please fill in the cancellation reason in the remarks!</Control>
		<Control name="FinishExecuteTask_Msg14">When completing the work order, the actual quantity must be greater than or equal to 0, please fill in again!</Control>
		<Control name="FinishExecuteTask_Msg15">When completing the work order, the working time must be greater than or equal to 0, please fill in again!</Control>
		<Control name="FinishExecuteTask_Msg16">Unprocessed FinishStatus value; {0}</Control>
		<Control name="FinishExecuteTask_Msg17">Complete the implementation task. An exception occurs when submitting data to the database: </Control>
		<Control name="FinishExecuteTask_Msg18">Failure to complete the implementation task, please check the background log</Control>
		<Control name="TaskApprovel_Msg1">Non-first line authority has no access to audit! </Control>
		<Control name="TaskApprovel_Msg2">The first line supervisor authorization cannot be found for the implementation order</Control>
		<Control name="TaskApprovel_Msg3">The status of the implementation work order is not submitted. Please submit it for review!</Control>
		<Control name="TaskApprovel_Msg4"> Switch approver exception: {0}</Control>
		<Control name="TaskApprovel_Msg5">The email fails to be sent after work order switching approver is implemented. The work order number {0}</Control>
		<Control name="TaskApprovel_Msg6">The current approval time exceeds the scheduled start time of task implementation. Callback is the only option available. Select another option.</Control>
		<Control name="TaskApprovel_Msg7">Exception to the approval implementation task: {0}</Control>
		<Control name="TaskApprovel_Msg8">Error switching/approval implementation task, please check background log</Control>
		<Control name="DeleteExecuteTask_Msg1"> Delete execution task parameter is null! </Control>
		<Control name="DeleteExecuteTask_Msg2"> The change number of the deleted execution task does not exist! </Control>
		<Control name="DeleteExecuteTask_Msg3"> Please select at least one execution order! </Control>
		<Control name="DeleteExecuteTask_Msg4"> One or more of the current changers do not exist or are deleted! </Control>
		<Control name="DeleteExecuteTask_Msg5"> One or more current implementers do not exist or are deleted! </Control>
		<Control name="DeleteExecuteTask_Msg6"> One or more states of the current executor are [success], and cannot be deleted </Control>
		<Control name="DeleteExecuteTask_Msg7"> Current change job One or more applicants are inconsistent with the current logon, no permission to complete </Control>
		<Control name="DeleteExecuteTask_Msg8"> One or more applicants for the current change job are empty, please check </Control>
		<Control name="DeleteExecuteTask_Msg9"> The current executor has one or more applicants that are inconsistent with the current logon and has no permission to complete </Control>
		<Control name="DeleteExecuteTask_Msg10"> One or more applications for the current implementer are empty, please check </Control>
		<Control name="DeleteExecuteTask_Msg11"> An exception occurred in the database of deleted execution tasks: {0}</Control>
		<Control name="DeleteExecuteTask_Msg12"> Error deleting execution task, please check background log </Control>
		<Control name="QueryTplsByTaskNo_Msg"> TaskItem for executing the work order does not exist! </Control>
		<Control name="QueryTplsByTaskNo_Ex"> Error query implementation task template, please view background log </Control>
		<Control name="QueryTplsByTaskNo_Ext_Msg1">无法找到对应站点</Control>
		<Control name="QueryTplsByTaskNo_Ext_Msg2">参数Level不能为空</Control>
		<Control name="QueryTplsByTaskNo_Ext_Msg3">当前用户无该工单实施权限，请使用该工单实施人登录查看实施</Control>
		<Control name="QueryTplsByTaskNo_Ext_Msg4">当前用户无该工单审核权限，请使用该工单方案提供人登录查看审核</Control>
		<Control name="QueryTplsByTaskNo_Ext_Msg5">当前用户无该网络实施权限，请使用该网络负责人登录查看实施</Control>
		<Control name="QueryTplsByTaskNo_Ext_Msg6">当前用户无该网络审核权限，请使用该网络维护主管登录查看审核</Control>
		<Control name="GetTaskTplValues_Msg1">实施工单的TaskItem不存在!</Control>
		<Control name="GetTaskTplValues_Msg2">实施工单不存在!</Control>
		<Control name="GetTaskTplValues_Ex">查询实施任务模板数据出错，请查看后台日志</Control>
		<Control name="GetTaskTplValues_Ext_Msg1">[GetTplValues]获取模板下的工序值异常：</Control>
		<Control name="SaveTaskTplValues_Msg1">保存实施任务的参数为空</Control>
		<Control name="SaveTaskTplValues_Msg2">实施工单的TaskItem不存在</Control>
		<Control name="SaveTaskTplValues_Msg3">当前实施任务的模板id不存在</Control>
		<Control name="SaveTaskTplValues_Msg4">保存实施任务模板数据出错，请查看后台日志</Control>
		<Control name="SaveTaskTplValues_Ext_Msg1">当前准备更改的数据中，模板里的工序有变化，请关闭后重新修改</Control>
		<Control name="SaveTaskTplValues_Ext_Msg2">第{0}行的{1}的数据为空;</Control>
		<Control name="SaveTaskTplValues_Ext_Msg3">工序{0}的数据不完整：{1} {2}</Control>
		<Control name="SaveTaskTplValues_Ext_Msg4">判断数据有效性失败：</Control>
		<Control name="SaveTaskTplValues_Ext_Msg5">当前要修改的工序{0}:值不存在</Control>
		<Control name="SaveTaskTplValues_Ext_Msg6">SaveStepValuesForTask提交数据到数据库中发生异常</Control>
		<Control name="TaskPermission_Msg1">实施任务的参数为空!</Control>
		<Control name="TaskPermission_Msg2">变更工单或工单的申请人数据有误！</Control>
		<Control name="TaskPermission_Msg3">实施任务为空，请检查数据！</Control>
		<Control name="TaskPermission_Msg4">实施任务实施人为空</Control>
		<Control name="TaskPermission_Msg5">实施任务核查人为空</Control>
		<Control name="TaskPermission_Msg6">当前用户没有这个变更工单的网络权限或不是实施工单的相关人,无权实施！</Control>
		<Control name="TaskPermission_Msg7">当前用户不是实施工单的实施人或核查人,没有打点权限！</Control>
		<Control name="TaskPermission_Msg8">实施任务的状态不是【待实施】或【成功】!</Control>
		<Control name="TaskPermission_Msg9">实施任务权限出错,请查看后台日志!</Control>

		<Control name="Const_RepUnits_网络">Network</Control>
		<Control name="Const_RepUnits_站点">Site</Control>
		<Control name="Const_RepUnits_网元">Network Element</Control>
		<Control name="Const_RepUnits_网管">Network Management</Control>
		<Control name="Const_RepUnits_单盘">Single Board</Control>
		<Control name="Const_RepUnits_节点">Node</Control>
		<Control name="Const_RepUnits_端口">Port</Control>
		<Control name="Const_RepUnits_拓扑">Topology</Control>
		<Control name="Const_RepUnits_其他">Other</Control>
		<Control name="TimeZoneTag_北京时间">Beijing Time</Control>
		<Control name="TimeZoneTag_网络时区">Network Timezone</Control>
	</Module>

	<Module name="Settings">
		<Control name="DescBeijing">Beijing Time</Control>
		<Control name="DescLocal">Local Time</Control>
	</Module>
</Resource>