﻿using Hub.Mobile.Interface;
using NLog;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Text;
[assembly: Xamarin.Forms.Dependency(typeof(Hub.Mobile.Services.ZipService))]
namespace Hub.Mobile.Services
{
    public class ZipService : IZipService
    {
        private ILogger logger = LogManager.GetCurrentClassLogger();
        public void ZipFiles(string sourceDirectoryName, string destinationArchiveFileName, string[] relativeFilenames)
        {
            if (Directory.Exists(sourceDirectoryName))
            {
                var dirName = Path.GetDirectoryName(destinationArchiveFileName);
                if (!Directory.Exists(dirName))
                {
                    Directory.CreateDirectory(dirName);
                }

                using (FileStream output = new FileStream(destinationArchiveFileName, FileMode.Create))
                {
                    using (ZipArchive zipArchive = new ZipArchive(output, ZipArchiveMode.Create))
                    {
                        foreach (var filename in relativeFilenames)
                        {
                            var filepath = Path.Combine(sourceDirectoryName, filename);
                            if (File.Exists(filepath))
                            {
                                zipArchive.CreateEntryFromFile(filepath, filename, CompressionLevel.Optimal);
                            }
                        }
                    }
                }
            }
            else
            {
                logger.Error("zip file error, source dir not exist: " + sourceDirectoryName);
                throw new Exception("zip file error, source dir not exist");
            }
        }

        public void ZipDir(string sourceDirectoryName, string destinationArchiveFileName)
        {
            if (Directory.Exists(sourceDirectoryName))
            {
                var dirName = Path.GetDirectoryName(destinationArchiveFileName);
                if (!Directory.Exists(dirName))
                {
                    Directory.CreateDirectory(dirName);
                }

                using (FileStream output = new FileStream(destinationArchiveFileName, FileMode.Create))
                {
                    using (ZipArchive zipArchive = new ZipArchive(output, ZipArchiveMode.Create))
                    {
                        var dirOrFiles = Directory.EnumerateFileSystemEntries(sourceDirectoryName, "*.*", SearchOption.AllDirectories);
                        foreach (var df in dirOrFiles)
                        {
                            if (File.Exists(df))//is file
                            {
                                string relativePath = string.Empty;
                                if (sourceDirectoryName.EndsWith("\\"))
                                {
                                    relativePath = df.Substring(sourceDirectoryName.Length);
                                }
                                else
                                {
                                    relativePath = df.Substring(sourceDirectoryName.Length + 1);
                                }
                                zipArchive.CreateEntryFromFile(df, relativePath, CompressionLevel.Optimal);
                            }
                        }
                    }
                }
            }
            else
            {
                logger.Error("zip file error, source dir not exist: " + sourceDirectoryName);
                throw new Exception("zip file error, source dir not exist");
            }
        }
    }
}
