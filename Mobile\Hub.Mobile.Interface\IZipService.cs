﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Hub.Mobile.Interface
{
    public interface IZipService
    {
        /// <summary>
        /// 压缩目录中指定文件到目标zip包中
        /// </summary>
        /// <param name="sourceDirectoryName">源目录路径</param>
        /// <param name="destinationArchiveFileName">目标zip文件路径</param>
        /// <param name="relativeFilenames">需要压缩的文件相对于sourceDirectoryName的路径</param>
        /// <returns>压缩包的路径</returns>
        void ZipFiles(string sourceDirectoryName, string destinationArchiveFileName, string[] relativeFilenames);
        /// <summary>
        /// 压缩目录中全部文件到目标zip包中
        /// </summary>
        /// <param name="sourceDirectoryName">源目录路径</param>
        /// <param name="destinationArchiveFileName">目标zip文件路径</param> 
        /// <returns>压缩包的路径</returns>
        void ZipDir(string sourceDirectoryName, string destinationArchiveFileName);
    }
}
