﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using Common.Model;
using System.Threading.Tasks;
using NPOI.OpenXmlFormats.Dml.ChartDrawing;
using Microsoft.AspNetCore.Http.Internal;
using Microsoft.AspNetCore.Http;

namespace Common.Utility
{
    /// <summary>
    /// MD5
    /// </summary>
    public static class FileHelper
    {
        public static string GetFileMD5Hash(string filePath)
        {
            try
            {
                using (FileStream fs = new FileStream(filePath, FileMode.Open))
                {
                    MD5 md5 = new MD5CryptoServiceProvider();
                    byte[] retVal = md5.ComputeHash(fs);
                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < retVal.Length; i++)
                    {
                        sb.Append(retVal[i].ToString("X2"));
                    }
                    return sb.ToString();
                }
            }
            catch (Exception ex)
            {
                throw new MessageException($"GetFileMD5Hash Failed, error massage:{ex.Message}");
            }
        }

        public static long GetFileSize(string filePath)
        {
            try
            {
                FileInfo fi = new FileInfo(filePath);
                return fi.Length;
            }
            catch
            {

            }
            return 0;
        }

        /// <summary>
        /// Bytes -> 文件
        /// </summary>
        /// <param name="bytes">byte数组</param>
        /// <param name="path">保存地址</param>
        public static void BytesToFile(byte[] bytes, string path)
        {
            try
            {
                // 文件存在则删除
                if (System.IO.File.Exists(path)) { System.IO.File.Delete(path); }

                // 写入文件，方式一
                using (FileStream fs = new FileStream(path, FileMode.CreateNew))
                {
                    using (BinaryWriter bw = new BinaryWriter(fs))
                    {
                        bw.Write(bytes, 0, bytes.Length);
                    }
                }
                // 写入文件，方式二
                //System.IO.File.WriteAllBytes(path, bytes);
            }
            catch
            {
                throw;
            }
        }

        public static string GetFileHash(string path)
        {
            string hasResult = string.Empty;
            using (var stream = new FileStream(path, FileMode.Open))
            {
                try
                {
                    var hash = SHA1.Create();
                    byte[] hashByte = hash.ComputeHash(stream);
                    hasResult = BitConverter.ToString(hashByte).Replace("-", "");
                }
                catch
                { }
                finally
                {
                    stream.Close();
                    stream.Dispose();
                }
            }
            return hasResult;
        }

        public static string ToMd5Hash(this string path)
        {
            return GetFileHash(path);
        }

        /// <summary>
        /// 获取指定目录下的文件列表
        /// </summary>
        /// <param name="dirPath"></param>
        /// <param name="inr"></param>
        /// <returns></returns>
        public static List<string> GetFiles(string dirPath, bool recursive=false)
        {
            List<string> resList = new List<string>();
            if (!Directory.Exists(dirPath))
            {
                return resList;
            }
            DirectoryInfo di = new DirectoryInfo(dirPath);
            resList = resList.Concat(di.GetFiles().ToList().Select(x => x.FullName).ToList()).ToList();
            if (recursive)
            {
                di.GetDirectories().ToList().ForEach(x => {
                    resList = resList.Concat(GetFiles(x.FullName, recursive)).ToList();
                });
            }
            return resList;
        }

        /// <summary>
        /// 将文件路径转成FormFileCollection
        /// </summary>
        /// <param name="filePaths"></param>
        /// <returns></returns>
        public static IFormFileCollection ConvertToFormFileCollection(List<string> filePaths, List<string> names = null)
        {
            FormFileCollection formFiles = new FormFileCollection();
            //int i = 0;
            //foreach (string filePath in filePaths)
            //{
            //    string name = Path.GetFileName(filePath);
            //    if (names != null && names.Count > i && !string.IsNullOrEmpty(names[i]))
            //    {
            //        name = names[i];
            //    }
            //    var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
            //    var formFile = new FormFile(fileStream, 0, fileStream.Length, name, Path.GetFileName(filePath))
            //    {
            //        Headers = new HeaderDictionary(),
            //        ContentType = "application/octet-stream"
            //    };
            //    i++;
            //    formFiles.Add(formFile);
            //}
            return formFiles;
        }
    }
}
