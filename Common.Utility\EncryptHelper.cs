﻿using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Security;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Common.Utility
{
    public static class EncryptHelper
    {
        #region MD5 加密

        /// <summary>
        /// MD5加密
        /// </summary>
        public static string Md532(this string source)
        {
            if (string.IsNullOrEmpty(source)) return null;
            var encoding = Encoding.UTF8;
            MD5 md5 = MD5.Create();
            return HashAlgorithmBase(md5, source, encoding);
        }

        public static string Md5Hex(string data)
        {
            MD5 md5 = MD5.Create();
            byte[] dataHash = md5.ComputeHash(Encoding.Default.GetBytes(data));
            StringBuilder sb = new StringBuilder();
            foreach (byte b in dataHash)
            {
                sb.Append(b.ToString("x2").ToLower());
            }
            return sb.ToString();
        }

        /// <summary>
        /// 32位MD5加密
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        public static string MD5Encrypt32(this string source)
        {
            MD5CryptoServiceProvider md5Hasher = new MD5CryptoServiceProvider();
            byte[] hashedDataBytes;
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
            hashedDataBytes = md5Hasher.ComputeHash(Encoding.GetEncoding("gb2312").GetBytes(source));
            StringBuilder tmp = new StringBuilder();
            foreach (byte i in hashedDataBytes)
            {
                tmp.Append(i.ToString("x2"));
            }
            return tmp.ToString();//默认情况
        }

        /// <summary>
        /// 加盐MD5加密
        /// </summary>
        public static string Md532Salt(this string source, string salt)
        {
            return string.IsNullOrEmpty(source) ? source.Md532() : (source + "『" + salt + "』").Md532();
        }

        #endregion

        #region SHA 加密

        /// <summary>
        /// SHA1 加密
        /// </summary>
        public static string Sha1(this string source)
        {
            if (string.IsNullOrEmpty(source)) return null;
            var encoding = Encoding.UTF8;
            SHA1 sha1 = new SHA1CryptoServiceProvider();
            return HashAlgorithmBase(sha1, source, encoding);
        }

        /// <summary>
        /// SHA256 加密
        /// </summary>
        public static string Sha256(this string source)
        {
            if (string.IsNullOrEmpty(source)) return null;
            var encoding = Encoding.UTF8;
            SHA256 sha256 = new SHA256Managed();
            return HashAlgorithmBase(sha256, source, encoding);
        }

        /// <summary>
        /// SHA512 加密
        /// </summary>
        public static string Sha512(this string source)
        {
            if (string.IsNullOrEmpty(source)) return null;
            var encoding = Encoding.UTF8;
            SHA512 sha512 = new SHA512Managed();
            return HashAlgorithmBase(sha512, source, encoding);
        }

        /// <summary>
        /// Sha1加密，用于钉钉鉴权
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        public static string Sha1ForDingTalk(this string source)
        {
            byte[] bytes = Encoding.UTF8.GetBytes(source);
            byte[] dataList = SHA1.Create().ComputeHash(bytes);
            StringBuilder stringBuilder = new StringBuilder();
            foreach (byte data in dataList)
            {
                stringBuilder.Append(data.ToString("X2"));
            }
            return stringBuilder.ToString();
        }

        #endregion

        #region HMAC 加密

        /// <summary>
        /// HmacSha1 加密
        /// </summary>
        public static string HmacSha1(this string source, string keyVal)
        {
            if (string.IsNullOrEmpty(source)) return null;
            var encoding = Encoding.UTF8;
            byte[] keyStr = encoding.GetBytes(keyVal);
            HMACSHA1 hmacSha1 = new HMACSHA1(keyStr);
            return HashAlgorithmBase(hmacSha1, source, encoding);
        }

        public static byte[] GetHmacSha1Bytes(this string source, string keyVal)
        {
            if (string.IsNullOrEmpty(source)) return null;
            var encoding = Encoding.UTF8;
            byte[] keyStr = encoding.GetBytes(keyVal);
            HMACSHA1 hmacSha1 = new HMACSHA1(keyStr);
            byte[] btStr = encoding.GetBytes(source);
            byte[] hashBytes = hmacSha1.ComputeHash(btStr);
            return hashBytes;
        }

        /// <summary>
        /// HmacSha256 加密
        /// </summary>
        public static string HmacSha256(this string source, string keyVal)
        {
            if (string.IsNullOrEmpty(source)) return null;
            var encoding = Encoding.UTF8;
            byte[] keyStr = encoding.GetBytes(keyVal);
            HMACSHA256 hmacSha256 = new HMACSHA256(keyStr);
            return HashAlgorithmBase(hmacSha256, source, encoding);
        }

        /// <summary>
        /// HmacSha384 加密
        /// </summary>
        public static string HmacSha384(this string source, string keyVal)
        {
            if (string.IsNullOrEmpty(source)) return null;
            var encoding = Encoding.UTF8;
            byte[] keyStr = encoding.GetBytes(keyVal);
            HMACSHA384 hmacSha384 = new HMACSHA384(keyStr);
            return HashAlgorithmBase(hmacSha384, source, encoding);
        }

        /// <summary>
        /// HmacSha512 加密
        /// </summary>
        public static string HmacSha512(this string source, string keyVal)
        {
            if (string.IsNullOrEmpty(source)) return null;
            var encoding = Encoding.UTF8;
            byte[] keyStr = encoding.GetBytes(keyVal);
            HMACSHA512 hmacSha512 = new HMACSHA512(keyStr);
            return HashAlgorithmBase(hmacSha512, source, encoding);
        }

        /// <summary>
        /// HmacMd5 加密
        /// </summary>
        public static string HmacMd5(this string source, string keyVal)
        {
            if (string.IsNullOrEmpty(source)) return null;
            var encoding = Encoding.UTF8;
            byte[] keyStr = encoding.GetBytes(keyVal);
            HMACMD5 hmacMd5 = new HMACMD5(keyStr);
            return HashAlgorithmBase(hmacMd5, source, encoding);
        }

        ///// <summary>
        ///// HmacRipeMd160 加密
        ///// </summary>
        //public static string HmacRipeMd160(this string source, string keyVal)
        //{
        //    if (string.IsNullOrEmpty(source)) return null;
        //    var encoding = Encoding.UTF8;
        //    byte[] keyStr = encoding.GetBytes(keyVal);
        //    HMACRIPEMD160 hmacRipeMd160 = new HMACRIPEMD160(keyStr);
        //    return HashAlgorithmBase(hmacRipeMd160, source, encoding);
        //}

        #endregion

        #region AES 加密解密

        /// <summary>  
        /// AES加密  
        /// </summary>  
        /// <param name="source">待加密字段</param>  
        /// <param name="keyVal">密钥值</param>  
        /// <param name="ivVal">加密辅助向量</param> 
        /// <returns></returns>  
        public static string AesStr(this string source, string keyVal, string ivVal)
        {
            var encoding = Encoding.UTF8;
            byte[] btKey = keyVal.FormatByte(encoding);
            byte[] btIv = ivVal.FormatByte(encoding);
            byte[] byteArray = encoding.GetBytes(source);
            string encrypt;
            Rijndael aes = Rijndael.Create();
            using (MemoryStream mStream = new MemoryStream())
            {
                using (CryptoStream cStream = new CryptoStream(mStream, aes.CreateEncryptor(btKey, btIv), CryptoStreamMode.Write))
                {
                    cStream.Write(byteArray, 0, byteArray.Length);
                    cStream.FlushFinalBlock();
                    encrypt = Convert.ToBase64String(mStream.ToArray());
                }
            }
            aes.Clear();
            return encrypt;
        }

        /// <summary>  
        /// AES解密  
        /// </summary>  
        /// <param name="source">待加密字段</param>  
        /// <param name="keyVal">密钥值</param>  
        /// <param name="ivVal">加密辅助向量</param>  
        /// <returns></returns>  
        public static string UnAesStr(this string source, string keyVal, string ivVal)
        {
            var encoding = Encoding.UTF8;
            byte[] btKey = keyVal.FormatByte(encoding);
            byte[] btIv = ivVal.FormatByte(encoding);
            byte[] byteArray = Convert.FromBase64String(source);
            string decrypt;
            Rijndael aes = Rijndael.Create();
            using (MemoryStream mStream = new MemoryStream())
            {
                using (CryptoStream cStream = new CryptoStream(mStream, aes.CreateDecryptor(btKey, btIv), CryptoStreamMode.Write))
                {
                    cStream.Write(byteArray, 0, byteArray.Length);
                    cStream.FlushFinalBlock();
                    decrypt = encoding.GetString(mStream.ToArray());
                }
            }
            aes.Clear();
            return decrypt;
        }

        /// <summary>  
        /// AES Byte类型 加密  
        /// </summary>  
        /// <param name="data">待加密明文</param>  
        /// <param name="keyVal">密钥值</param>  
        /// <param name="ivVal">加密辅助向量</param>  
        /// <returns></returns>  
        public static byte[] AesByte(this byte[] data, string keyVal, string ivVal)
        {
            byte[] bKey = new byte[32];
            Array.Copy(Encoding.UTF8.GetBytes(keyVal.PadRight(bKey.Length)), bKey, bKey.Length);
            byte[] bVector = new byte[16];
            Array.Copy(Encoding.UTF8.GetBytes(ivVal.PadRight(bVector.Length)), bVector, bVector.Length);
            byte[] cryptograph;
            Rijndael aes = Rijndael.Create();
            try
            {
                using (MemoryStream mStream = new MemoryStream())
                {
                    using (CryptoStream cStream = new CryptoStream(mStream, aes.CreateEncryptor(bKey, bVector), CryptoStreamMode.Write))
                    {
                        cStream.Write(data, 0, data.Length);
                        cStream.FlushFinalBlock();
                        cryptograph = mStream.ToArray();
                    }
                }
            }
            catch
            {
                cryptograph = null;
            }
            return cryptograph;
        }

        /// <summary>  
        /// AES Byte类型 解密  
        /// </summary>  
        /// <param name="data">待解密明文</param>  
        /// <param name="keyVal">密钥值</param>  
        /// <param name="ivVal">加密辅助向量</param> 
        /// <returns></returns>  
        public static byte[] UnAesByte(this byte[] data, string keyVal, string ivVal)
        {
            byte[] bKey = new byte[32];
            Array.Copy(Encoding.UTF8.GetBytes(keyVal.PadRight(bKey.Length)), bKey, bKey.Length);
            byte[] bVector = new byte[16];
            Array.Copy(Encoding.UTF8.GetBytes(ivVal.PadRight(bVector.Length)), bVector, bVector.Length);
            byte[] original;
            Rijndael aes = Rijndael.Create();
            try
            {
                using (MemoryStream mStream = new MemoryStream(data))
                {
                    using (CryptoStream cStream = new CryptoStream(mStream, aes.CreateDecryptor(bKey, bVector), CryptoStreamMode.Read))
                    {
                        using (MemoryStream originalMemory = new MemoryStream())
                        {
                            byte[] buffer = new byte[1024];
                            int readBytes;
                            while ((readBytes = cStream.Read(buffer, 0, buffer.Length)) > 0)
                            {
                                originalMemory.Write(buffer, 0, readBytes);
                            }

                            original = originalMemory.ToArray();
                        }
                    }
                }
            }
            catch
            {
                original = null;
            }
            return original;
        }

        /// <summary>
        /// C#获取文件MD5值方法
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static string GetMD5HashFromFile(string fileName)
        {
            try
            {
                using (FileStream file = new FileStream(fileName, FileMode.Open))
                {
                    System.Security.Cryptography.MD5 md5 = new System.Security.Cryptography.MD5CryptoServiceProvider();
                    ////转换成utf8字节流
                    //byte[] data = new byte[file.Length];
                    //file.Write(data, 0, (int)file.Length);
                    //string str = System.Text.Encoding.UTF8.GetString(data);
                    //byte[] retVal = md5.ComputeHash(System.Text.Encoding.UTF8.GetBytes(str));
                    byte[] retVal = md5.ComputeHash(file);
                    file.Close();

                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < retVal.Length; i++)
                    {
                        sb.Append(retVal[i].ToString("x2"));
                    }
                    return sb.ToString();
                }
            }
            catch (Exception ex)
            {
                throw new Exception("GetMD5HashFromFile() fail,error:" + ex.Message);
            }
        }

        #endregion

        #region RSA 加密解密

        //密钥对，请配合密钥生成工具使用『 http://download.csdn.net/detail/downiis6/9464639 』
        public const string PublicRsaKey = @"<RSAKeyValue><Modulus>wuHjdKRxDOn8c0JaZEe6B4tIPec8i5gXXfRqilYBLP2z2iw70r15+ULKeVOONmh8rbVrncRi4+FJCKjn5pu9Dgg6WM2dJ7FxtUV7F26STlHybjnmrQe3EDzLGQrk4m1cPFN7YJZS/F2527ZdneIuVsU2JdOtDCp+4iBKsLzlp6k=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>";
        public const string PrivateRsaKey = @"<RSAKeyValue><Modulus>wuHjdKRxDOn8c0JaZEe6B4tIPec8i5gXXfRqilYBLP2z2iw70r15+ULKeVOONmh8rbVrncRi4+FJCKjn5pu9Dgg6WM2dJ7FxtUV7F26STlHybjnmrQe3EDzLGQrk4m1cPFN7YJZS/F2527ZdneIuVsU2JdOtDCp+4iBKsLzlp6k=</Modulus><Exponent>AQAB</Exponent><P>2WzpueyVKrpae0HPEqvKGvPR5nAaeRsxWCgYEuXGRX9kh7CczcGQdpynLo45Q58Vbs4KYA01c6cTOjDH2Vj2Qw==</P><Q>5XUbyooMpunE3Xph51L1U61vvjeZrUQV6P/DBCEiYceZ/GlP8qSY0FdekPm8nRnwaOoXk1FT26dgizeder2Jow==</Q><DP>NTzM0gCS2b59jzai7HBx8T2IRMNoSAUXhP10P5RRgwzZYbwl+N+PbuuWp/idHuiiB9qdgb1aPDJJq/LrzFVTHw==</DP><DQ>wxGbDcXs2Z2VGoCnaMQzAe9JNxoHkLB4O/vrK8CLfTr2YwqYx/w8WapdH7zgJ2Lc6iRQ5JHYS71wqwod3DH0Jw==</DQ><InverseQ>vbo7+XZdFgVQI6FD/1oXXWwX9jwxdPjPlmTG5YyM9rCal7LAFdfkZtpPRH00z+Tk8hqP2YWI7M9cI2jbf/jwOA==</InverseQ><D>GACqEFCSoKRkagPG+KlBlzKcnrc6JKKtD27jp7nTt5QLJfjNvh/ag+H/PctAmEtCk+3k3SUg3ZqpRqObEF2n0/ln+LYT7xLeEjRQXOJd1uWri/dZLrPT0+weXSqJ8VIS2q6N/QOLrCzBXXWN4ifv0vG1VB/j5ke94tGseTI7ACs=</D></RSAKeyValue>";
        /// <summary>
        /// RSA 加密
        /// </summary>
        public static string Rsa(this string source)
        {
            RSACryptoServiceProvider rsa = new RSACryptoServiceProvider();
            rsa.FromXmlString(PublicRsaKey);
            var cipherbytes = rsa.Encrypt(Encoding.UTF8.GetBytes(source), true);
            return Convert.ToBase64String(cipherbytes);
        }

        /// <summary>
        /// RSA解密
        /// </summary>
        public static string UnRsa(this string source)
        {
            RSACryptoServiceProvider rsa = new RSACryptoServiceProvider();
            rsa.FromXmlString(PrivateRsaKey);
            var cipherbytes = rsa.Decrypt(Convert.FromBase64String(source), true);
            return Encoding.UTF8.GetString(cipherbytes);
        }
        public static void CreateRSAKey()
        {
            //设置[公钥私钥]文件路径
            string privateKeyPath = @"d:\\PrivateKey.xml";
            string publicKeyPath = @"d:\\PublicKey.xml";
            //创建RSA对象
            RSACryptoServiceProvider rsa = new RSACryptoServiceProvider();
            //生成RSA[公钥私钥]
            string privateKey = rsa.ToXmlString(true);
            string publicKey = rsa.ToXmlString(false);
            //将密钥写入指定路径
            File.WriteAllText(privateKeyPath, privateKey);//文件内包含公钥和私钥
            File.WriteAllText(publicKeyPath, publicKey);//文件内只包含公钥
        }

        /// <summary>
        /// RSA 加密
        /// </summary>
        public static string Rsa(this string source, string publicKey, int byteLength = 116)
        {
            byte[] encryptedData;
            RSACryptoServiceProvider rsa = new RSACryptoServiceProvider();
            rsa.FromXmlString(publicKey);
            var plainData = Encoding.UTF8.GetBytes(source);
            using (var plaiStream = new MemoryStream(plainData))
            {
                using (var crypStream = new MemoryStream())
                {
                    var offSet = 0;
                    var inputLen = plainData.Length;
                    for (var i = 0; inputLen - offSet > 0; offSet = i * byteLength)
                    {
                        if (inputLen - offSet > byteLength)
                        {
                            var buffer = new Byte[byteLength];
                            plaiStream.Read(buffer, 0, byteLength);
                            var cryptograph = rsa.Encrypt(buffer, false);
                            crypStream.Write(cryptograph, 0, cryptograph.Length);
                        }
                        else
                        {
                            var buffer = new Byte[inputLen - offSet];
                            plaiStream.Read(buffer, 0, inputLen - offSet);
                            var cryptograph = rsa.Encrypt(buffer, false);
                            crypStream.Write(cryptograph, 0, cryptograph.Length);
                        }
                        ++i;
                    }
                    crypStream.Position = 0;
                    encryptedData = crypStream.ToArray();
                }
                return Convert.ToBase64String(encryptedData).Replace("-", string.Empty);
            }
        }

        /// <summary>
        /// RSA解密
        /// </summary>
        public static string UnRsa(this string source, string privateKey, int byteLength = 128)
        {
            RSACryptoServiceProvider rsa = new RSACryptoServiceProvider();
            rsa.FromXmlString(privateKey);
            byte[] decryptedData;
            byte[] encryptedBytes = Convert.FromBase64String(source);
            using (var plaiStream = new MemoryStream(encryptedBytes))
            {
                using (var decrypStream = new MemoryStream())
                {
                    var offSet = 0;
                    var inputLen = encryptedBytes.Length;
                    for (var i = 0; inputLen - offSet > 0; offSet = i * byteLength)
                    {
                        if (inputLen - offSet > byteLength)
                        {
                            var buffer = new Byte[byteLength];
                            plaiStream.Read(buffer, 0, byteLength);
                            var decrypData = rsa.Decrypt(buffer, false);
                            decrypStream.Write(decrypData, 0, decrypData.Length);
                        }
                        else
                        {
                            var buffer = new Byte[inputLen - offSet];
                            plaiStream.Read(buffer, 0, inputLen - offSet);
                            var decrypData = rsa.Decrypt(buffer, false);
                            decrypStream.Write(decrypData, 0, decrypData.Length);
                        }
                        ++i;
                    }
                    decrypStream.Position = 0;
                    decryptedData = decrypStream.ToArray();
                }
            }
            return Encoding.UTF8.GetString(decryptedData);
        }

        #region 公钥加密，私钥解密

        /// <summary>
        /// RSA xml公钥加密
        /// </summary>
        /// <param name="plaintext">明文</param>
        /// <param name="publicKey">公钥</param>
        /// <returns>密文字符串</returns>
        public static string EncryptByRSA(string plaintext, string publicKey, string input_charset = "utf-8")
        {
            byte[] dataToEncrypt = Encoding.GetEncoding(input_charset).GetBytes(plaintext);
            using (RSACryptoServiceProvider RSA = new RSACryptoServiceProvider())
            {
                RSA.FromXmlString(publicKey);
                int MaxBlockSize = RSA.KeySize / 8 - 11;    //加密块最大长度限制
                if (dataToEncrypt.Length <= MaxBlockSize)
                    return Convert.ToBase64String(RSA.Encrypt(dataToEncrypt, false));

                using (MemoryStream PlaiStream = new MemoryStream(dataToEncrypt))
                using (MemoryStream CrypStream = new MemoryStream())
                {
                    Byte[] Buffer = new Byte[MaxBlockSize];
                    int BlockSize = PlaiStream.Read(Buffer, 0, MaxBlockSize);

                    while (BlockSize > 0)
                    {
                        Byte[] ToEncrypt = new Byte[BlockSize];
                        Array.Copy(Buffer, 0, ToEncrypt, 0, BlockSize);

                        Byte[] Cryptograph = RSA.Encrypt(ToEncrypt, false);
                        CrypStream.Write(Cryptograph, 0, Cryptograph.Length);
                        BlockSize = PlaiStream.Read(Buffer, 0, MaxBlockSize);
                    }
                    return Convert.ToBase64String(CrypStream.ToArray(), Base64FormattingOptions.None);
                }
            }
            //byte[] dataToEncrypt = Encoding.GetEncoding(input_charset).GetBytes(plaintext);
            //using (RSACryptoServiceProvider RSA = new RSACryptoServiceProvider())
            //{
            //    RSA.FromXmlString(publicKey);
            //    byte[] encryptedData = RSA.Encrypt(dataToEncrypt, false);
            //    return Convert.ToBase64String(encryptedData);
            //}
        }

        /// <summary>
        /// RSA xml私钥解密
        /// </summary>
        /// <param name="ciphertext">密文</param>
        /// <param name="privateKey">私钥</param>
        /// <returns>明文字符串</returns>
        public static string DecryptByRSA(string ciphertext, string privateKey, string input_charset = "utf-8")
        {
            using (RSACryptoServiceProvider RSA = new RSACryptoServiceProvider())
            {
                RSA.FromXmlString(privateKey);
                byte[] encryptedData = Convert.FromBase64String(ciphertext);
                int MaxBlockSize = RSA.KeySize / 8;    //解密块最大长度限制

                if (encryptedData.Length <= MaxBlockSize)
                {
                    byte[] source = RSA.Decrypt(encryptedData, false);
                    char[] asciiChars = new char[Encoding.GetEncoding(input_charset).GetCharCount(source, 0, source.Length)];
                    Encoding.GetEncoding(input_charset).GetChars(source, 0, source.Length, asciiChars, 0);
                    return new string(asciiChars);
                }
                using (MemoryStream CrypStream = new MemoryStream(encryptedData))
                using (MemoryStream PlaiStream = new MemoryStream())
                {
                    Byte[] Buffer = new Byte[MaxBlockSize];
                    int BlockSize = CrypStream.Read(Buffer, 0, MaxBlockSize);
                    while (BlockSize > 0)
                    {
                        Byte[] ToDecrypt = new Byte[BlockSize];
                        Array.Copy(Buffer, 0, ToDecrypt, 0, BlockSize);

                        Byte[] Plaintext = RSA.Decrypt(ToDecrypt, false);
                        PlaiStream.Write(Plaintext, 0, Plaintext.Length);
                        BlockSize = CrypStream.Read(Buffer, 0, MaxBlockSize);
                    }

                    char[] asciiChars = new char[Encoding.GetEncoding(input_charset).GetCharCount(PlaiStream.ToArray(), 0, PlaiStream.ToArray().Length)];
                    Encoding.GetEncoding(input_charset).GetChars(PlaiStream.ToArray(), 0, PlaiStream.ToArray().Length, asciiChars, 0);
                    return new string(asciiChars);
                }
            }
            //UnicodeEncoding byteConverter = new UnicodeEncoding();
            //using (RSACryptoServiceProvider RSA = new RSACryptoServiceProvider())
            //{
            //    RSA.FromXmlString(privateKey);
            //    byte[] encryptedData = Convert.FromBase64String(ciphertext);
            //    byte[] source = RSA.Decrypt(encryptedData, false);
            //    char[] asciiChars = new char[Encoding.GetEncoding(input_charset).GetCharCount(source, 0, source.Length)];
            //    Encoding.GetEncoding(input_charset).GetChars(source, 0, source.Length, asciiChars, 0);
            //    return new string(asciiChars);
            //}
        }

        #endregion

        #region 私钥加密，公钥解密

        /// <summary>
        /// 用XML私钥给数据进行RSA加密
        /// </summary>
        /// <param name="xmlPrivateKey"></param>
        /// <param name="strEncryptString"></param>
        /// <returns></returns>
        public static string EncryptByPrivateKey(string strEncryptString, string xmlPrivateKey, string input_charset = "utf-8")
        {
            //加载私钥
            RSACryptoServiceProvider privateRsa = new RSACryptoServiceProvider();
            privateRsa.FromXmlString(xmlPrivateKey);

            //转换密钥
            AsymmetricCipherKeyPair keyPair = DotNetUtilities.GetKeyPair(privateRsa);
            IBufferedCipher c = CipherUtilities.GetCipher("RSA/ECB/PKCS1Padding"); //使用RSA/ECB/PKCS1Padding格式

            c.Init(true, keyPair.Private);//第一个参数为true表示加密，为false表示解密；第二个参数表示密钥
            byte[] dataToEncrypt = Encoding.GetEncoding(input_charset).GetBytes(strEncryptString);//获取字节

            byte[] cache;
            int time = 0;//次数
            int inputLen = dataToEncrypt.Length;
            int bufferSize = (privateRsa.KeySize / 8) - 11;
            int offSet = 0;

            MemoryStream outStream = new MemoryStream();
            while (inputLen - offSet > 0)
            {
                if (inputLen - offSet > bufferSize)
                {
                    cache = c.DoFinal(dataToEncrypt, offSet, bufferSize);
                }
                else
                {
                    cache = c.DoFinal(dataToEncrypt, offSet, inputLen - offSet);
                }
                //写入
                outStream.Write(cache, 0, cache.Length);

                time++;
                offSet = time * bufferSize;
            }

            byte[] resData = outStream.ToArray();

            string strBase64 = Convert.ToBase64String(resData);
            outStream.Close();
            return strBase64;
        }

        /// <summary>
        /// 用XML公钥给数据进行RSA解密 
        /// </summary>
        /// <param name="xmlPublicKey"> 公钥(XML格式字符串) </param>
        /// <param name="strDecryptString"> 要解密数据 </param>
        /// <returns> 解密后的数据 </returns>
        public static string DecryptByPublicKey(string strDecryptString, string xmlPublicKey, string input_charset = "utf-8")
        {
            //加载公钥
            RSACryptoServiceProvider publicRsa = new RSACryptoServiceProvider();
            publicRsa.FromXmlString(xmlPublicKey);
            RSAParameters rp = publicRsa.ExportParameters(false);
            //转换密钥
            AsymmetricKeyParameter pbk = DotNetUtilities.GetRsaPublicKey(rp);
            IBufferedCipher c = CipherUtilities.GetCipher("RSA/ECB/PKCS1Padding");
            //第一个参数为true表示加密，为false表示解密；第二个参数表示密钥
            c.Init(false, pbk);
            byte[] DataToDecrypt = Convert.FromBase64String(strDecryptString);
            byte[] cache;
            int time = 0;//次数
            int inputLen = DataToDecrypt.Length;
            int offSet = 0;
            int MaxBlockSize = publicRsa.KeySize / 8;    //解密块最大长度限制
            MemoryStream outStream = new MemoryStream();
            while (inputLen - offSet > 0)
            {
                if (inputLen - offSet > MaxBlockSize)
                {
                    cache = c.DoFinal(DataToDecrypt, offSet, MaxBlockSize);
                }
                else
                {
                    cache = c.DoFinal(DataToDecrypt, offSet, inputLen - offSet);
                }
                //写入
                outStream.Write(cache, 0, cache.Length);

                time++;
                offSet = time * MaxBlockSize;
            }
            byte[] resData = outStream.ToArray();

            string strDec = Encoding.GetEncoding(input_charset).GetString(resData);
            return strDec;
        }

        #endregion

        #endregion

        #region DES 加密解密

        /// <summary>
        /// DES 加密
        /// </summary>
        public static string Des(this string source, string keyVal, string ivVal)
        {
            try
            {
                byte[] data = Encoding.UTF8.GetBytes(source);
                var des = new DESCryptoServiceProvider { Key = Encoding.ASCII.GetBytes(keyVal.Length > 8 ? keyVal.Substring(0, 8) : keyVal), IV = Encoding.ASCII.GetBytes(ivVal.Length > 8 ? ivVal.Substring(0, 8) : ivVal) };
                var desencrypt = des.CreateEncryptor();
                byte[] result = desencrypt.TransformFinalBlock(data, 0, data.Length);
                return BitConverter.ToString(result);
            }
            catch { return "转换出错！"; }
        }

        /// <summary>
        /// DES 解密
        /// </summary>
        public static string UnDes(this string source, string keyVal, string ivVal)
        {
            try
            {
                string[] sInput = source.Split("-".ToCharArray());
                byte[] data = new byte[sInput.Length];
                for (int i = 0; i < sInput.Length; i++)
                {
                    data[i] = byte.Parse(sInput[i], NumberStyles.HexNumber);
                }
                var des = new DESCryptoServiceProvider { Key = Encoding.ASCII.GetBytes(keyVal.Length > 8 ? keyVal.Substring(0, 8) : keyVal), IV = Encoding.ASCII.GetBytes(ivVal.Length > 8 ? ivVal.Substring(0, 8) : ivVal) };
                var desencrypt = des.CreateDecryptor();
                byte[] result = desencrypt.TransformFinalBlock(data, 0, data.Length);
                return Encoding.UTF8.GetString(result);
            }
            catch { return "解密出错！"; }
        }

        #endregion

        #region TripleDES 加密解密

        /// <summary>
        /// DES3 加密
        /// </summary>
        public static string Des3(this string source, string keyVal)
        {
            try
            {
                TripleDESCryptoServiceProvider des = new TripleDESCryptoServiceProvider
                {
                    Key = keyVal.FormatByte(Encoding.UTF8),
                    Mode = CipherMode.ECB,
                    Padding = PaddingMode.PKCS7
                };
                using (MemoryStream ms = new MemoryStream())
                {
                    byte[] btArray = Encoding.UTF8.GetBytes(source);
                    try
                    {
                        using (CryptoStream cs = new CryptoStream(ms, des.CreateEncryptor(), CryptoStreamMode.Write))
                        {
                            cs.Write(btArray, 0, btArray.Length);
                            cs.FlushFinalBlock();
                        }
                        return ms.ToArray().Bytes2Str();
                    }
                    catch { return source; }
                }
            }
            catch
            {
                return "TripleDES加密出现错误";
            }
        }

        /// <summary>
        /// DES3 解密
        /// </summary>
        public static string UnDes3(this string source, string keyVal)
        {
            try
            {
                byte[] byArray = source.Str2Bytes();
                TripleDESCryptoServiceProvider des = new TripleDESCryptoServiceProvider
                {
                    Key = keyVal.FormatByte(Encoding.UTF8),
                    Mode = CipherMode.ECB,
                    Padding = PaddingMode.PKCS7
                };
                using (MemoryStream ms = new MemoryStream())
                {
                    using (CryptoStream cs = new CryptoStream(ms, des.CreateDecryptor(), CryptoStreamMode.Write))
                    {
                        cs.Write(byArray, 0, byArray.Length);
                        cs.FlushFinalBlock();
                        cs.Close();
                        ms.Close();
                        return Encoding.UTF8.GetString(ms.ToArray());
                    }
                }
            }
            catch
            {
                return "TripleDES解密出现错误";
            }
        }

        #endregion

        #region BASE64 加密解密

        /// <summary>
        /// BASE64 加密
        /// </summary>
        /// <param name="source">待加密字段</param>
        /// <returns></returns>
        public static string Base64(this string source)
        {
            var btArray = Encoding.UTF8.GetBytes(source);
            return Convert.ToBase64String(btArray, 0, btArray.Length);
        }

        /// <summary>
        /// BASE64 解密
        /// </summary>
        /// <param name="source">待解密字段</param>
        /// <returns></returns>
        public static string UnBase64(this string source)
        {
            var btArray = Convert.FromBase64String(source);
            return Encoding.UTF8.GetString(btArray);
        }

        #endregion

        #region 位移加解密
        public static string DriftEncode(this string str)
        {
            if (string.IsNullOrEmpty(str))
            {
                return str;
            }
            string htext = "";

            for (int i = 0; i < str.Length; i++)
            {
                htext = htext + (char)(str[i] + 10 - 1 * 2);
            }
            return htext;
        }

        public static string DriftDecode(this string str)
        {
            if (string.IsNullOrEmpty(str))
            {
                return str;
            }
            string dtext = "";

            for (int i = 0; i < str.Length; i++)
            {
                dtext = dtext + (char)(str[i] - 10 + 1 * 2);
            }
            return dtext;
        }
        #endregion

        #region 文件移位加密
        /// <summary>
        /// 文件移位解密
        /// </summary>
        /// <param name="srcFile"></param>
        /// <param name="tarFile"></param>
        /// <param name="offset">补偿位置</param>
        /// <param name="count">写入数量</param>
        public static string DecDisPlaceFile(string srcFile, string tarFile, int offset, int count)
        {
            string result = string.Empty;
            try
            {
                byte[] data = new byte[100];
                int len = 0;
                using (FileStream fs = new FileStream(srcFile, FileMode.Open, FileAccess.Read),
                    tfs = new FileStream(tarFile, FileMode.OpenOrCreate))
                {
                    while ((len = fs.Read(data, 0, data.Length)) != 0)
                    {
                        if (len == 100)
                        {
                            tfs.Write(data, offset, count);
                        }
                        else
                        {
                            tfs.Write(data, 0, len);
                        }
                    }
                    fs.Close();
                    tfs.Close();
                }
                result = tarFile;
            }
            catch 
            {

            }
            return result;
        }

        /// <summary>
        /// 文件移位加密
        /// </summary>
        /// <param name="srcFile"></param>
        /// <param name="tarFile"></param>
        /// <param name="offset">补偿字节位置</param>
        /// <param name="count">单次写入字节数量</param>
        public static string EncDisPlaceFile(string srcFile, string tarFile, int offset, int count)
        {
            string result = string.Empty;
            try
            {
                byte[] data = new byte[100];
                int len = 0;
                using (FileStream fs = new FileStream(srcFile, FileMode.Open, FileAccess.Read),
                    tfs = new FileStream(tarFile, FileMode.OpenOrCreate))
                {
                    while ((len = fs.Read(data, offset, count)) != 0)
                    {
                        if (len == 60)
                        {
                            tfs.Write(data, 0, 100);
                        }
                        else
                        {
                            tfs.Write(data, offset, len);
                        }
                    }
                    fs.Close();
                    tfs.Close();
                }
                result = tarFile;
            }
            catch
            {

            }
            return result;
        }
        #endregion

        #region 内部方法

        /// <summary>
        /// 转成数组
        /// </summary>
        private static byte[] Str2Bytes(this string source)
        {
            source = source.Replace(" ", "");
            byte[] buffer = new byte[source.Length / 2];
            for (int i = 0; i < source.Length; i += 2) buffer[i / 2] = Convert.ToByte(source.Substring(i, 2), 16);
            return buffer;
        }

        /// <summary>
        /// 转换成字符串
        /// </summary>
        private static string Bytes2Str(this IEnumerable<byte> source, string formatStr = "{0:X2}")
        {
            StringBuilder pwd = new StringBuilder();
            foreach (byte btStr in source) { pwd.AppendFormat(formatStr, btStr); }
            return pwd.ToString();
        }

        private static byte[] FormatByte(this string strVal, Encoding encoding)
        {
            return encoding.GetBytes(strVal.Base64().Substring(0, 16).ToUpper());
        }

        /// <summary>
        /// HashAlgorithm 加密统一方法
        /// </summary>
        private static string HashAlgorithmBase(HashAlgorithm hashAlgorithmObj, string source, Encoding encoding)
        {
            byte[] btStr = encoding.GetBytes(source);
            byte[] hashStr = hashAlgorithmObj.ComputeHash(btStr);
            return hashStr.Bytes2Str();
        }

        #endregion
    }
}
