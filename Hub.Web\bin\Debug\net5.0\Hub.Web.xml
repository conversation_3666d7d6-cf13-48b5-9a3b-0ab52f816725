<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Hub.Web</name>
    </assembly>
    <members>
        <member name="M:Hub.Web.Controllers.AppController.GetAllApps">
            <summary>
            提供给手机端获取所有小程序数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hub.Web.Controllers.AppController.Insert(Hub.Model.AppDto)">
            <summary>
            提供给网页端插入一条新的小程序信息,Online模式使用这方法
            </summary> 
            <returns>返回的实体对象包含新增对象的ID</returns>
        </member>
        <member name="M:Hub.Web.Controllers.AppController.Update(Hub.Model.AppDto)">
            <summary>
            提供给网页端更新一条小程序信息
            </summary> 
            <returns>返回的实体对象包含新增对象的ID</returns>
        </member>
        <member name="M:Hub.Web.Controllers.AppController.GetPageData(Common.DAL.Methods.PageCriteria)">
            <summary>
            提供给网页端返回特定页面的小程序数据
            </summary> 
        </member>
        <member name="M:Hub.Web.Controllers.AppController.Delete(System.String)">
            <summary>
            根据ID删除某个小程序，如果是Hybrid小程序，还会删除服务器上保存的更新文件
            </summary> 
        </member>
        <member name="M:Hub.Web.Controllers.AppController.UploadHybridApp(Hub.Model.AppFileDto)">
            <summary>
            上传一个新的混合式App，如果服务器上已经存在相同AppCode的App，则会更新一个版本号
            </summary>
            <param name="req">带有文件流的APP实体对象，版本号自动更新，并且自动根据文件名生成cdn的下载地址</param>
            <returns>返回的实体对象包含新增对象的ID以及CDN下载地址</returns>
        </member>
        <member name="M:Hub.Web.Controllers.AppController.GetAppDownloadInfo(System.String)">
            <summary>
            获取混合式APP的新版本信息
            </summary>  
        </member>
        <member name="M:Hub.Web.Controllers.AppController.NewApp">
            <summary>
            测试用
            </summary>
        </member>
    </members>
</doc>
