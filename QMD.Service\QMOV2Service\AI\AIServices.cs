using Common.DAL.Methods;
using Common.Service;
using DocumentFormat.OpenXml.Office2021.DocumentTasks;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using LinqKit;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using NPOI.Util;
using QMD.DAL;
using QMD.DAL.Expends;
using QMD.DAL.Migrations;
using QMD.DAL.Table;
using QMD.DAL.Table.emt;
using QMD.Model;
using QMD.Model.emt;
using QMD.Model.QA;
using QMD.Model.Why.Why2;
using QMD.Repository;
using QMD.Service.QMOV2Service;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace QMD.Service.QMOV2Service.AI
{
    /// <summary>
    /// AI智能填报QMO工单服务
    /// </summary>
    public class AIServices
    {
        private readonly ILogger<AIServices> _logger;
        private readonly QmdDbContext _dbContext;
        private readonly OpenAIService _openAIService;
        private readonly DispatchOrderService _disOrderService;
        private readonly NetUserPositionRepository _positionRepository;

        // 标准工单类型列表
        private readonly Dictionary<int, string> _orderTypes;

        // 模板内容缓存
        private static string _firstStageTemplate;
        private static string _secondStageTemplate;
        private static string _upgradeTemplate;
        private static string _commonTemplate;

        public AIServices(
            ILogger<AIServices> logger,
            QmdDbContext dbContext,
            OpenAIService openAIService,
            DispatchOrderService disOrderService,
            NetUserPositionRepository positionRepository)
        {
            _logger = logger;
            _dbContext = dbContext;
            _openAIService = openAIService;
            _disOrderService = disOrderService;
            _positionRepository = positionRepository;

            // 从枚举获取标准工单类型列表
            _orderTypes = GetOrderTypesFromEnum();

        }

        #region 提示词缓存
        private static readonly object _lock = new object();

        /// <summary>
        /// 第一阶段模板：提取基础信息（省份、网络名称、工单类型）
        /// </summary>
        public string FirstStageTemplate
        {
            get
            {
                lock (_lock)
                {
                    if (string.IsNullOrWhiteSpace(_firstStageTemplate))
                    {
                        string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AIPrompts", "first_stage_template.md");
                        if (File.Exists(templatePath))
                        {
                            _firstStageTemplate = File.ReadAllText(templatePath);
                        }
                    }
                }
                return _firstStageTemplate;
            }
        }

        /// <summary>
        /// 第二阶段模板：匹配标准网络名称
        /// </summary>
        public string SecondStageTemplate
        {
            get
            {
                lock (_lock)
                {
                    if (string.IsNullOrWhiteSpace(_secondStageTemplate))
                    {
                        string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AIPrompts", "second_stage_template.md");
                        if (File.Exists(templatePath))
                        {
                            _secondStageTemplate = File.ReadAllText(templatePath);
                        }
                    }
                }
                return _secondStageTemplate;
            }
        }

        /// <summary>
        /// 升级类型工单模板
        /// </summary>
        public string UpgradeTemplate
        {
            get
            {
                lock (_lock)
                {
                    if (string.IsNullOrWhiteSpace(_upgradeTemplate))
                    {
                        string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AIPrompts", "upgrade_template.md");
                        if (File.Exists(templatePath))
                        {
                            _upgradeTemplate = File.ReadAllText(templatePath);
                        }
                    }
                }
                return _upgradeTemplate;
            }
        }

        /// <summary>
        /// 通用工单模板
        /// </summary>
        public string CommonTemplate
        {
            get
            {
                lock (_lock)
                {
                    {
                        string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AIPrompts", "common_template.md");
                        if (File.Exists(templatePath))
                        {
                            _commonTemplate = File.ReadAllText(templatePath);
                        }
                    }
                }
                return _commonTemplate;
            }
        }
        #endregion

        /// <summary>
        /// 从EnumDisOrderType枚举获取标准工单类型列表
        /// </summary>
        /// <returns>工单类型字典</returns>
        private Dictionary<int, string> GetOrderTypesFromEnum()
        {
            var orderTypes = new Dictionary<int, string>();
            
            // 获取EnumDisOrderType枚举类型
            Type enumType = typeof(EnumDisOrderType);
            
            // 遍历枚举值
            foreach (EnumDisOrderType value in Enum.GetValues(enumType))
            {
                // 获取枚举值的字段信息
                FieldInfo field = enumType.GetField(value.ToString());
                
                // 获取Description特性
                DescriptionAttribute attribute = field.GetCustomAttribute<DescriptionAttribute>();
                
                // 获取EnumOrderKind特性
                EnumOrderKindAttribute kindAttribute = field.GetCustomAttribute<EnumOrderKindAttribute>();
                
                // 只添加OrderKind为1或3的工单类型（全部适用或报备适用）
                if (kindAttribute != null && (kindAttribute.OrderKind == 1 || kindAttribute.OrderKind == 3))
                {
                    // 使用Description特性作为名称
                    string description = attribute?.Description ?? value.ToString();
                    
                    // 添加到字典
                    orderTypes.Add((int)value, description);
                }
            }
            
            return orderTypes;
        }

        /// <summary>
        /// AI智能填报QMO工单（单个文件）
        /// </summary>
        /// <param name="file">要解析的docx文件</param>
        /// <returns>解析结果</returns>
        public async Task<BaseRes<ReportordersFormDto>> AIFillFormAsync(IFormFile file)
        {
            try
            {
                _logger.LogInformation($"开始AI智能填报QMO工单，文件名: {file.FileName}");

                // 验证是否为docx文件
                string fileExtension = Path.GetExtension(file.FileName).ToLower();
                if (fileExtension != ".docx")
                {
                    return new BaseRes<ReportordersFormDto>(false, "仅支持docx格式的Word文档文件");
                }

                // 保存上传的文件到临时目录，使用GUID生成唯一的临时文件名
                string tempDir = Path.Combine(Path.GetTempPath(), "QMDAITemp");
                if (!Directory.Exists(tempDir))
                {
                    Directory.CreateDirectory(tempDir);
                }
                
                string tempFilePath = Path.Combine(tempDir, $"{Guid.NewGuid()}.docx");
                using (var stream = new FileStream(tempFilePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                try
                {
                    // 第一阶段：从docx文件提取文本，并调用AI提取基础信息
                    var docTextResult = ExtractTextFromDocx(tempFilePath);
                    if (!docTextResult.Success)
                    {
                        return new BaseRes<ReportordersFormDto>(false, docTextResult.ErrorMessage);
                    }
                    var docText = docTextResult.Text;

                    var basicInfoResult = await ExtractBasicInfoAsync(docText);
                    if (!basicInfoResult.Success)
                    {
                        return new BaseRes<ReportordersFormDto>(false, basicInfoResult.ErrorMessage);
                    }
                    var basicInfo = basicInfoResult.BasicInfo;

                    // 验证基础信息
                    if (!ValidateBasicInfo(basicInfo, out var errorMsg))
                    {
                        return new BaseRes<ReportordersFormDto>(false, errorMsg);
                    }

                    // 第二阶段：将网络名称与数据库匹配，获取标准网络名称
                    var matchResult = await MatchNetworkNameAsync(basicInfo.NetworkName, basicInfo.Province,basicInfo.City, basicInfo.ProductSpe);
                    if (!matchResult.Success)
                    {
                        return new BaseRes<ReportordersFormDto>(false, matchResult.ErrorMessage);
                    }
                    var networkInfo = matchResult.NetworkInfo;

                    // 第三阶段：根据工单类型选择提示词模板，调用AI解析详细字段
                    var detailResult = await ExtractDetailInfoAsync(docText, basicInfo, networkInfo);
                    if (!detailResult.Success)
                    {
                        return new BaseRes<ReportordersFormDto>(false, detailResult.ErrorMessage);
                    }

                    _logger.LogInformation($"AI智能填报QMO工单完成，工单类型: {basicInfo.OrderTypeName}");

                    return new BaseRes<ReportordersFormDto>(detailResult.DetailInfo);
                }
                finally
                {
                    // 删除临时文件
                    if (File.Exists(tempFilePath))
                    {
                        File.Delete(tempFilePath);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AI智能填报QMO工单失败");
                return new BaseRes<ReportordersFormDto>(false, $"处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从docx文件提取文本内容
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>提取结果，包含是否成功、提取的文本内容和错误消息</returns>
        private (bool Success, string Text, string ErrorMessage) ExtractTextFromDocx(string filePath)
        {
            try
            {
                _logger.LogInformation($"开始从docx文件提取文本: {filePath}");

                if (!File.Exists(filePath))
                {
                    return (false, null, $"文件不存在: {filePath}");
                }

                StringBuilder text = new StringBuilder();

                // 使用 DocumentFormat.OpenXml 解析 docx 文件
                using (WordprocessingDocument doc = WordprocessingDocument.Open(filePath, false))
                {
                    if (doc.MainDocumentPart?.Document?.Body == null)
                    {
                        return (false, null, "无效的docx文件：无法找到文档主体");
                    }

                    Body body = doc.MainDocumentPart.Document.Body;

                    // 提取所有段落的文本
                    foreach (var paragraph in body.Elements<Paragraph>())
                    {
                        string paragraphText = paragraph.InnerText;
                        if (!string.IsNullOrWhiteSpace(paragraphText))
                        {
                            text.AppendLine(paragraphText);
                        }
                    }

                    // 提取表格中的文本
                    foreach (var table in body.Elements<Table>())
                    {
                        foreach (var row in table.Elements<TableRow>())
                        {
                            foreach (var cell in row.Elements<TableCell>())
                            {
                                string cellText = cell.InnerText;
                                if (!string.IsNullOrWhiteSpace(cellText))
                                {
                                    text.Append(cellText + "\t");
                                }
                            }
                            text.AppendLine(); // 表格行结束换行
                        }
                    }
                }

                var extractedText = text.ToString().Trim();
                _logger.LogInformation($"从docx文件提取文本完成，文本长度: {extractedText.Length}");

                if (string.IsNullOrWhiteSpace(extractedText))
                {
                    _logger.LogWarning($"从docx文件提取的文本为空: {filePath}");
                }

                return (true, extractedText, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"从docx文件提取文本失败: {filePath}");
                return (false, null, $"从docx文件提取文本失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 第一阶段：提取基础信息
        /// </summary>
        /// <param name="docText">文档文本</param>
        /// <returns>提取结果，包含是否成功、基础信息和错误消息</returns>
        private async Task<(bool Success, BasicInfoDto BasicInfo, string ErrorMessage)> ExtractBasicInfoAsync(string docText)
        {
            try
            {
                _logger.LogInformation("开始提取基础信息（省份、网络名称、工单类型、专业）");

                // 获取标准省份和专业列表
                var provinces = _dbContext.Set<Netprovider>()
                    .Where(p => !string.IsNullOrWhiteSpace(p.Province) && p.IsActived).Select(x => x.Province).Distinct().ToList();
                var cities = _dbContext.Set<Netprovider>()
                    .Where(p => !string.IsNullOrWhiteSpace(p.City) && p.IsActived).Select(x=>x.City).Distinct().ToList();

                var productSpes = _dbContext.Set<Netprovider>()
                    .Select(p => p.ProductSpe).Distinct().ToList(); // 确保专业字段唯一

                // 获取第一阶段提示词模板
                var templateContent = FirstStageTemplate;

                // 替换模板中的占位符
                var prompt = templateContent
                    .Replace("{provinces}", JsonConvert.SerializeObject(provinces))
                    .Replace("{cites}", JsonConvert.SerializeObject(cities))
                    .Replace("{orderTypes}", JsonConvert.SerializeObject(_orderTypes.Select(kv => new { id = kv.Key, name = kv.Value }).ToList()))
                    .Replace("{productSpes}", JsonConvert.SerializeObject(productSpes)) // 添加专业字段替换
                    .Replace("{docContent}", docText);

                // 调用AI提取基础信息
                var response = await _openAIService.GetFirstStageCompletionAsync(prompt);

                // 解析JSON响应
                var parseResult = ParseBasicInfoResponse(response);
                if (!parseResult.Success)
                {
                    return (false, null, parseResult.ErrorMessage);
                }

                _logger.LogInformation($"提取基础信息完成，省份: {parseResult.BasicInfo.Province}，网络名称: {parseResult.BasicInfo.NetworkName}，工单类型: {parseResult.BasicInfo.OrderTypeName}");

                return (true, parseResult.BasicInfo, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "提取基础信息失败");
                return (false, null, $"提取基础信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 解析第一阶段AI响应
        /// </summary>
        /// <param name="response">AI响应</param>
        /// <returns>解析结果，包含是否成功、基础信息和错误消息</returns>
        private (bool Success, BasicInfoDto BasicInfo, string ErrorMessage) ParseBasicInfoResponse(string response)
        {
            try
            {
                // 使用正则表达式提取JSON部分
                var match = Regex.Match(response, @"\{[\s\S]*\}");
                if (!match.Success)
                {
                    return (false, null, "无法从AI响应中提取JSON");
                }

                var jsonStr = match.Value;
                var basicInfo = JsonConvert.DeserializeObject<BasicInfoDto>(jsonStr);

                if (basicInfo == null)
                {
                    return (false, null, "解析AI响应JSON失败");
                }

                return (true, basicInfo, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"解析AI响应失败: {response}");
                return (false, null, $"解析AI响应失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证基础信息
        /// </summary>
        /// <param name="basicInfo">基础信息</param>
        /// <param name="errorMsg">错误信息</param>
        /// <returns>校验是否通过</returns>
        private bool ValidateBasicInfo(BasicInfoDto basicInfo, out string errorMsg)
        {
            errorMsg = null;
            // 验证省份
            var provinces = _dbContext.Set<Netprovider>()
                .Select(p => p.Province)
                .Distinct()
                .ToList();
            if (!provinces.Contains(basicInfo.Province))
            {
                errorMsg = $"提取的省份 '{basicInfo.Province}' 不在标准省份列表中";
                return false;
            }
            //验证专业
            var productSpes = _dbContext.Set<Netprovider>()
                .Select(p => p.ProductSpe)
                .Distinct()
                .ToList();
            if (!productSpes.Contains(basicInfo.ProductSpe))
            {
                errorMsg = $"提取的专业 '{basicInfo.ProductSpe}' 不在标准专业列表中";
                return false;
            }

            // 验证工单类型
            if (!_orderTypes.ContainsKey(basicInfo.OrderType))
            {
                errorMsg = $"提取的工单类型ID '{basicInfo.OrderType}' 不在标准工单类型列表中";
                return false;
            }

            // 验证工单类型名称
            if (_orderTypes[basicInfo.OrderType] != basicInfo.OrderTypeName)
            {
                errorMsg = $"提取的工单类型名称 '{basicInfo.OrderTypeName}' 与ID '{basicInfo.OrderType}' 不匹配";
                return false;
            }

            // 验证网络名称
            if (string.IsNullOrWhiteSpace(basicInfo.NetworkName))
            {
                errorMsg = "提取的网络名称不能为空";
                return false;
            }

            return true;
        }

        /// <summary>
        /// 第二阶段：匹配标准网络名称
        /// </summary>
        /// <param name="networkName">提取的网络名称</param>
        /// <param name="province">提取的省份</param>
        /// <returns>匹配结果，包含是否成功、网络信息和错误消息</returns>
        private async Task<(bool Success, NetworkInfoDto NetworkInfo, string ErrorMessage)> MatchNetworkNameAsync(string networkName, string province,string city, string productSpe)
        {
            try
            {
                _logger.LogInformation($"开始匹配标准网络名称: {networkName}");

                // 从数据库获取所有网络名称
                var netProviders = _dbContext.Set<Netprovider>().ToList();

                // 先用省份过滤，得到本省份所有网络
                var filteredProviders = netProviders
                    .Where(p => p.Province == province && !string.IsNullOrEmpty(p.NetName))
                    .ToList();
                if (!string.IsNullOrWhiteSpace(city))
                {
                    filteredProviders = filteredProviders.Where(x => x.City == city).ToList();
                }

                if (filteredProviders.Count == 0)
                {
                    return (false, null, $"未找到省份 '{province}:{city}' 下的网络数据");
                }

                // 提取所有可用的专业和运营商，去重
                var allProductSpe = filteredProviders
                    .Where(p => !string.IsNullOrEmpty(p.ProductSpe))
                    .Select(p => p.ProductSpe)
                    .Distinct()
                    .ToList();
                var allOperator = filteredProviders
                    .Where(p => !string.IsNullOrEmpty(p.Operator))
                    .Select(p => p.Operator)
                    .Distinct()
                    .ToList();

                // 构建标准网络列表，包含网络名、运营商和专业
                var standardNetworks = filteredProviders
                    .Select(p => new
                    {
                        p.NetName,
                        p.Province,
                        p.NetProperties,
                        p.ProductSpe,
                        p.Operator
                    })
                    .ToList();

                // 获取第二阶段提示词模板
                var templateContent = SecondStageTemplate;

                // 构建AI提示词，包含省份、网络名称、所有可选专业、所有可选运营商
                var prompt = templateContent
                    .Replace("{productSpe}", productSpe)
                    .Replace("{networkName}", networkName)
                    .Replace("{province}", province)
                    .Replace("{city}", city)
                    .Replace("{standardNetworks}", JsonConvert.SerializeObject(standardNetworks))
                    .Replace("{allProductSpe}", JsonConvert.SerializeObject(allProductSpe))
                    .Replace("{allOperator}", JsonConvert.SerializeObject(allOperator));

                // 调用AI匹配标准网络名称
                var response = await _openAIService.GetSecondStageCompletionAsync(prompt);

                // 解析AI响应
                var match = Regex.Match(response, @"\{[\s\S]*\}");
                if (!match.Success)
                {
                    return (false, null, "无法从AI响应中提取JSON");
                }

                var jsonStr = match.Value;
                var matchResult = JsonConvert.DeserializeObject<NetworkMatchResult>(jsonStr);

                if (matchResult == null || string.IsNullOrEmpty(matchResult.StandardNetworkName))
                {
                    return (false, null, "无法匹配标准网络名称");
                }

                // 查找匹配的网络提供商
                var matchedProvider = filteredProviders.FirstOrDefault(p =>
                    p.NetName == matchResult.StandardNetworkName);

                if (matchedProvider == null)
                {
                    // 如果没有找到匹配的网络名称，使用专业属性进行辅助判断
                    matchedProvider = filteredProviders.FirstOrDefault(p =>
                        string.IsNullOrEmpty(p.ProductSpe) && p.ProductSpe == productSpe);
                }

                if (matchedProvider == null)
                {
                    return (false, null, $"无法找到匹配的标准网络名称: {matchResult.StandardNetworkName}");
                }

                var networkInfo = new NetworkInfoDto
                {
                    StandardNetworkName = matchedProvider.NetName,
                    CodeSk = matchedProvider.CodeSk,
                    MatchResult = matchResult.MatchConfidence
                };

                _logger.LogInformation($"AI匹配标准网络名称完成: {networkInfo.StandardNetworkName}，匹配置信度: {matchResult.MatchConfidence}%");

                return (true, networkInfo, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"匹配标准网络名称失败: {networkName}");
                return (false, null, $"匹配标准网络名称失败: {networkName}");
            }
        }

        /// <summary>
        /// 第三阶段：提取详细字段信息
        /// </summary>
        /// <param name="docText">文档文本</param>
        /// <param name="basicInfo">基础信息</param>
        /// <param name="networkInfo">网络信息</param>
        /// <returns>提取结果，包含是否成功、详细表单信息和错误消息</returns>
        private async Task<(bool Success, ReportordersFormDto DetailInfo, string ErrorMessage)> ExtractDetailInfoAsync(string docText, BasicInfoDto basicInfo, NetworkInfoDto networkInfo)
        {
            try
            {
                _logger.LogInformation($"开始提取详细信息，工单类型: {basicInfo.OrderTypeName}");

                // 根据工单类型选择提示词模板
                string templateContent;
                if (basicInfo.OrderType == 7) // 7是升级类型,EnumDisOrderType
                {
                    templateContent = UpgradeTemplate;
                }
                else
                {
                    templateContent = CommonTemplate;
                }

                // 替换模板中的占位符
                var prompt = templateContent
                    .Replace("{docContent}", docText)
                    .Replace("{riskLevels}", string.Join(",", QMD.Env.ConfigEnvValues.QmoV2Cfg.RepRiskLevels)); // 使用ConfigEnvValues中的风险级别替换

                // 调用AI提取详细信息
                var response = await _openAIService.GetDetailCompletionAsync(prompt);

                // 解析JSON响应
                var parseResult = ParseDetailInfoResponse(response, basicInfo, networkInfo);
                if (!parseResult.Success)
                {
                    return (false, null, parseResult.ErrorMessage);
                }

                _logger.LogInformation($"提取详细信息完成，工单标题: {parseResult.DetailInfo.Title}");

                return (true, parseResult.DetailInfo, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "提取详细信息失败");
                return (false, null, $"提取详细信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取详细信息提取模板内容
        /// </summary>
        /// <param name="orderTypeId">工单类型ID</param>
        /// <returns>模板内容</returns>
        private string GetDetailTemplate(int orderTypeId)
        {
            // 只区分升级类型和其他类型
            return orderTypeId == 7 ? UpgradeTemplate : CommonTemplate;
        }

        /// <summary>
        /// 解析第三阶段AI响应
        /// </summary>
        /// <param name="response">AI响应</param>
        /// <param name="basicInfo">基础信息</param>
        /// <param name="networkInfo">网络信息</param>
        /// <returns>解析结果，包含是否成功、详细表单信息和错误消息</returns>
        private (bool Success, ReportordersFormDto DetailInfo, string ErrorMessage) ParseDetailInfoResponse(string response, BasicInfoDto basicInfo, NetworkInfoDto networkInfo)
        {
            try
            {
                // 使用正则表达式提取JSON部分
                var match = Regex.Match(response, @"\{[\s\S]*\}");
                if (!match.Success)
                {
                    return (false, null, "无法从AI响应中提取JSON");
                }

                var jsonStr = match.Value;
                var jsonObject = JsonConvert.DeserializeObject<JObject>(jsonStr);

                if (jsonObject == null)
                {
                    return (false, null, "解析AI响应JSON失败");
                }

                // 创建表单DTO对象
                var detailInfo = new ReportordersFormDto
                {
                    OrderType = (EnumDisOrderType)basicInfo.OrderType, // 根据 basicInfo 的 OrderType 确定
                    //OutputLine = DeserializeJson<string>(jsonObject, "outputLine"), // 从 JSON 中提取
                    //ProductLine = DeserializeJson<string>(jsonObject, "productLine"), // 从 JSON 中提取
                    //ProductLMT = DeserializeJson<List<string>>(jsonObject, "productLMT"), // 从 JSON 中提取
                    LineRemark = DeserializeJson<string>(jsonObject, "lineRemark"), // 从 JSON 中提取
                    RiskLevel = DeserializeJson<string>(jsonObject, "riskLevel"), // 从 JSON 中提取
                    PlanStartTime = DeserializeJson<DateTime?>(jsonObject, "planStartTime"), // 从 JSON 中提取并转换为 DateTime
                    PlanEndTime = DeserializeJson<DateTime?>(jsonObject, "planEndTime"), // 从 JSON 中提取并转换为 DateTime
                    Title = DeserializeJson<string>(jsonObject, "title"), // 从 JSON 中提取
                    //CustomName = DeserializeJson<CustomNameDto>(jsonObject, "customName") ?? new CustomNameDto(), // 从 JSON 中提取
                    CustomEmail = DeserializeJson<string>(jsonObject, "customEmail"), // 从 JSON 中提取
                    CustomPhone = DeserializeJson<string>(jsonObject, "customPhone"), // 从 JSON 中提取
                    CodeSk = networkInfo.CodeSk, // 从 JSON 中提取
                    TaskContent = DeserializeJson<string>(jsonObject, "taskContent"), // 从 JSON 中提取
                    IsReinstated = DeserializeJson<bool>(jsonObject, "isReinstated"), // 从 JSON 中提取
                    SoftwareForm = JsonConvert.SerializeObject(DeserializeJson<List<string>>(jsonObject, "softwareForm")), // 转换为 JSON 字符串
                    UpgradeReason = JsonConvert.SerializeObject(DeserializeJson<List<string>>(jsonObject, "upgradeReason")), // 转换为 JSON 字符串
                    IsActived = true, // 始终有效
                    TaskUsers = DeserializeJson<List<NetUserPosition>>(jsonObject, "taskUsers") ?? new List<NetUserPosition>(), // 从 JSON 中提取
                    CheckUsers = DeserializeJson<List<NetUserPosition>>(jsonObject, "checkUsers") ?? new List<NetUserPosition>(), // 从 JSON 中提取
                    InvolveDevices = DeserializeJson<List<InvolveDeviceDto>>(jsonObject, "involveDevices") ?? new List<InvolveDeviceDto>(), // 从 JSON 中提取
                    InvolveNmpinfos = DeserializeJson<List<InvolveNmpinfoDto>>(jsonObject, "involveNmpinfos") ?? new List<InvolveNmpinfoDto>(), // 从 JSON 中提取
                    InvolveProducts = DeserializeJson<List<InvolveProductDto>>(jsonObject, "involveProducts") ?? new List<InvolveProductDto>(), // 从 JSON 中提取
                    
                    //LocalTime = DeserializeJson<string>(jsonObject, "localTime"), // 从 JSON 中提取
                    CustomName = DeserializeJson<CustomNameDto>(jsonObject, "customName") ?? new CustomNameDto(), // 从 JSON 中提取
                    // CopyUsers = DeserializeJson<List<InvolveUserDto>>(jsonObject, "copyUsers") ?? new List<InvolveUserDto>(), // 从 JSON 中提取
                    UpgradeMode = (EnumUpgradeMode)DeserializeJson<int>(jsonObject, "upgradeMode"), // 从 JSON 中提取
                    CustomUsers = DeserializeJson<List<InvolveUserDto>>(jsonObject, "customUsers") ?? new List<InvolveUserDto>(), // 从 JSON 中提取
                };

                //OutputLine,ProductLine,ProductLMT这三个需要代码判断
                var netOrgs = _disOrderService.GetWhyIINetOrganizationCache().GetAwaiter().GetResult()?.Data ?? new List<ProductLineInfoDto>();
                var netLinqOrgs = netOrgs.GroupBy(x => x.codeSk).Select(x => new {
                    CodeSk = x.Key,
                    OutputLine = x.Max(t => t.primaryOrganization),
                    ProductLine = x.Max(t => t.productLine),
                    ProductLmt = x.Select(t => t.fourthOrganization).Distinct().ToList()
                }).ToList();
                netLinqOrgs = netLinqOrgs.Where(x => networkInfo.CodeSk == x.CodeSk).ToList();

                var netLinqOrg = netLinqOrgs.FirstOrDefault();
                if(netLinqOrg != null)
                {
                    detailInfo.OutputLine = netLinqOrg.OutputLine;
                    detailInfo.ProductLine = netLinqOrg.ProductLine;
                    detailInfo.ProductLMT = netLinqOrg.ProductLmt;
                }
                //所有的人员信息需要进一步校验（TaskUsers，CheckUsers）
                detailInfo.TaskUsers = GetMappedUsers(detailInfo.TaskUsers);
                detailInfo.CheckUsers = GetMappedUsers(detailInfo.CheckUsers);

                detailInfo.ApplyUser = _positionRepository.Query(x=>x.IsActived && x.UserEmail == HttpContextHelper.CurrentUserEmail).FirstOrDefault();

                detailInfo.LocalTime = DateTime.Now;

                detailInfo.CustomUsers.ForEach(x =>
                {
                    x.UserType = EnumInvolveUserType.客户;
                    x.InvUserName = x.InvEmail;//数据库里两者保持一致
                });


                return (true, detailInfo, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"解析AI响应失败: {response}");
                return (false, null, $"解析AI响应失败: {ex.Message}");
            }
        }
        /// <summary>
        /// 将识别到的用户信息转换成数据库标准的数据
        /// </summary>
        /// <param name="userEntities"></param>
        /// <returns></returns>
        private List<NetUserPosition> GetMappedUsers(List<NetUserPosition> userEntities)
        {
            var mappedUsers = new List<NetUserPosition>();

            foreach (var user in userEntities)
            {
                NetUserPosition mappedUser = null;

                // 根据昵称查询
                if (!string.IsNullOrWhiteSpace(user.NickName))
                {
                    var userByNickName = _positionRepository.Query(x => x.IsActived && x.NickName == user.NickName).ToList();
                    if (userByNickName.Count == 1)
                    {
                        //mappedUser = AutoMapperConfig.Mapper.Map<InvolveUserDto>(userByNickName.First());
                        mappedUser = userByNickName.First();
                    }
                }

                // 如果没有找到，根据电话查询
                if (mappedUser == null && !string.IsNullOrWhiteSpace(user.UserPhone))
                {
                    var userByPhone = _positionRepository.Query(x => x.IsActived && x.UserPhone == user.UserPhone).ToList();
                    if (userByPhone.Count == 1)
                    {
                        //mappedUser = AutoMapperConfig.Mapper.Map<InvolveUserDto>(userByPhone.First());
                        mappedUser = userByPhone.First();
                    }
                }

                // 如果仍然没有找到，尝试同时查询
                if (mappedUser == null)
                {
                    var combinedQuery = _positionRepository.Query(x => x.IsActived && 
                        (x.NickName == user.NickName || x.UserPhone == user.UserPhone)).ToList();
                    if (combinedQuery.Count == 1)
                    {
                        //mappedUser = AutoMapperConfig.Mapper.Map<InvolveUserDto>(combinedQuery.First());
                        mappedUser = combinedQuery.First();
                    }
                }

                // 添加到列表
                if (mappedUser != null)
                {
                    mappedUsers.Add(mappedUser);
                }
                else
                {
                    mappedUsers.Add(new NetUserPosition()); // 如果没有找到，添加空对象
                }
            }

            return mappedUsers;
        }
        private T DeserializeJson<T>(JObject jsonObject, string propertyName)
        {
            var settings = new JsonSerializerSettings
            {
                ContractResolver = new DefaultContractResolver
                {
                    NamingStrategy = new CamelCaseNamingStrategy()
                }
            };
            return jsonObject[propertyName].ToObject<T>(JsonSerializer.Create(settings));
        }
    }

    /// <summary>
    /// 网络匹配结果
    /// </summary>
    public class NetworkMatchResult
    {
        /// <summary>
        /// 标准网络名称
        /// </summary>
        public string StandardNetworkName { get; set; }

        /// <summary>
        /// 匹配置信度（0-100）
        /// </summary>
        public int MatchConfidence { get; set; }
    }

    /// <summary>
    /// 基础信息DTO
    /// </summary>
    public class BasicInfoDto
    {
        /// <summary>
        /// 省份
        /// </summary>
        public string Province { get; set; }
        /// <summary>
        /// 地市
        /// </summary>
        public string City { get; set; }

        /// <summary>
        /// 网络名称
        /// </summary>
        public string NetworkName { get; set; }

        /// <summary>
        /// 工单类型ID
        /// </summary>
        public int OrderType { get; set; }

        /// <summary>
        /// 工单类型名称
        /// </summary>
        public string OrderTypeName { get; set; }
        /// <summary>
        /// 专业
        /// </summary>
        public string ProductSpe { get; set; }
    }

    /// <summary>
    /// 网络信息DTO
    /// </summary>
    public class NetworkInfoDto
    {
        /// <summary>
        /// 标准网络名称
        /// </summary>
        public string StandardNetworkName { get; set; }

        /// <summary>
        /// CodeSk
        /// </summary>
        public string CodeSk { get; set; }

        /// <summary>
        /// 匹配的可信度
        /// </summary>

        public int MatchResult { get; set; }
    }

    public class CustomNameDto
    {
        public string Email { get; set; }
        public string NickName { get; set; }
        public string PhoneNumber { get; set; }
    }

    public class ReportordersFormDto
    {
        public Netprovider NetProvider { get; set; }
        public EnumDisOrderType OrderType { get; set; }
        public string OutputLine { get; set; }
        public string ProductLine { get; set; }
        public List<string> ProductLMT { get; set; }//["OTN网管"]
        public string LineRemark { get; set; }
        public string RiskLevel { get; set; }
        public DateTime? PlanStartTime { get; set; }
        public DateTime? PlanEndTime { get; set; }
        public string Title { get; set; }
        public CustomNameDto CustomName { get; set; }
        public string CustomEmail { get; set; }
        public string CustomPhone { get; set; }
        public string CodeSk { get; set; }
        public string TaskContent { get; set; }
        public bool IsReinstated { get; set; }
        public string SoftwareForm { get; set; }//["网管全量/增量补丁"]
        public string UpgradeReason { get; set; }//["基线版本"]
        public bool IsActived { get; set; }
        public NetUserPosition ApplyUser { get; set; }
        public List<NetUserPosition> TaskUsers { get; set; }
        public List<NetUserPosition> CheckUsers { get; set; }
        public List<InvolveDeviceDto> InvolveDevices { get; set; }
        public List<InvolveNmpinfoDto> InvolveNmpinfos { get; set; }
        public List<InvolveProductDto> InvolveProducts { get; set; }
        public DateTime? LocalTime { get; set; }
        public List<InvolveUserDto> CustomUsers { get; set; }
        public List<NetUserPosition> CopyUsers { get; set; }
        public EnumUpgradeMode UpgradeMode { get; set; }
    }
} 
