(function () {
  function Camera() {}
  // option
  // {
  //     CompressionQuality:90, //int
  //     MaxWidthHeight:800, //int
  //     SaveToAlbum:false, //bool
  //     NeedPosition:false //bool
  // }
  //success {"Photo":xxxxxx,"Position":{"Latitude":xxx,"Longitude":xxx,"Altitude":xxx}}
  //error    错误提示
  Camera.prototype.takePicture = function (
    successCallback,
    errorCallback,
    option
  ) {
    if (typeof errorCallback != "function") {
      console.error("parameter [errorCallback] should be function");
      return;
    }
    if (typeof successCallback != "function") {
      console.error("parameter [successCallback] should be function");
      return;
    }
    hub_mobile_drive.exec(
      function (result) {
        successCallback(result);
      },
      function (error) {
        errorCallback(error);
      },
      "Camera",
      "TakePhoto",
      option
    );
  };
  //option
  // {
  //     CompressionQuality:90, //int
  //     MaxWidthHeight:800, //int
  //     MultiSelect:false, //bool
  // }
  //success  base64字符串数组
  //error    错误提示
  Camera.prototype.pickPicture = function (
    successCallback,
    errorCallback,
    option
  ) {
    if (typeof errorCallback != "function") {
      console.error("parameter [errorCallback] should be function");
      return;
    }
    if (typeof successCallback != "function") {
      console.error("parameter [successCallback] should be function");
      return;
    }
    hub_mobile_drive.exec(
      function (result) {
        successCallback(result);
      },
      function (error) {
        errorCallback(error);
      },
      "Camera",
      "PickPhoto",
      option
    );
  };
    //option
  // {
  //     Url:"", //string  下载地址
  //     FileName:"", //string  文件名
  //     Md5:"", //string  文件md5值
  // }
  //success  返回照片存储相册的物理路径
  //error    错误提示
  Camera.prototype.saveToAlbum = function (
    successCallback,
    errorCallback,
    option
  ) {
    if (typeof errorCallback != "function") {
      console.error("parameter [errorCallback] should be function");
      return;
    }
    if (typeof successCallback != "function") {
      console.error("parameter [successCallback] should be function");
      return;
    }
    hub_mobile_drive.exec(
      function (result) {
        successCallback(result);
      },
      function (error) {
        errorCallback(error);
      },
      "Camera",
      "SaveToAlbum",
      option
    );
  };
  //添加插件
  hub_mobile_drive.addPlugin("camera", new Camera());
})();
