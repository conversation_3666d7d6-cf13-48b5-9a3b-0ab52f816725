﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class item_step_level_1_2 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "CustomerProgStepID",
                table: "TaskItems",
                newName: "Level2CustomerProgStepID");

            migrationBuilder.AddColumn<string>(
                name: "Level1CustomerProgStepID",
                table: "TaskItems",
                type: "varchar(32)",
                maxLength: 32,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Level1CustomerProgStepID",
                table: "TaskItems");

            migrationBuilder.RenameColumn(
                name: "Level2CustomerProgStepID",
                table: "TaskItems",
                newName: "CustomerProgStepID");
        }
    }
}
