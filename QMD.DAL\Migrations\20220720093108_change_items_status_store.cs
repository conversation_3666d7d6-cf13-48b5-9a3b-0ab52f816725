﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class change_items_status_store : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "LastUploadDateTime",
                table: "TaskItemAndTplRels",
                type: "datetime(6)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Level1PassedStepCount",
                table: "TaskItemAndTplRels",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "Level2PassedStepCount",
                table: "TaskItemAndTplRels",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "Level3PassedStepCount",
                table: "TaskItemAndTplRels",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "TotalFileCount",
                table: "TaskItemAndTplRels",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "TotalRequiredStepCount",
                table: "TaskItemAndTplRels",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "UploadedRequiredStepCount",
                table: "TaskItemAndTplRels",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "LastUploadDateTime",
                table: "TaskItemAndTplRels");

            migrationBuilder.DropColumn(
                name: "Level1PassedStepCount",
                table: "TaskItemAndTplRels");

            migrationBuilder.DropColumn(
                name: "Level2PassedStepCount",
                table: "TaskItemAndTplRels");

            migrationBuilder.DropColumn(
                name: "Level3PassedStepCount",
                table: "TaskItemAndTplRels");

            migrationBuilder.DropColumn(
                name: "TotalFileCount",
                table: "TaskItemAndTplRels");

            migrationBuilder.DropColumn(
                name: "TotalRequiredStepCount",
                table: "TaskItemAndTplRels");

            migrationBuilder.DropColumn(
                name: "UploadedRequiredStepCount",
                table: "TaskItemAndTplRels");
        }
    }
}
