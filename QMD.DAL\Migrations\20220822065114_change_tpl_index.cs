﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class change_tpl_index : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_TaskTpls_ProjectID_DisplayName",
                table: "TaskTpls");

            migrationBuilder.CreateIndex(
                name: "IX_TaskTpls_DisplayName",
                table: "TaskTpls",
                column: "DisplayName",
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_TaskTpls_DisplayName",
                table: "TaskTpls");

            migrationBuilder.CreateIndex(
                name: "IX_TaskTpls_ProjectID_DisplayName",
                table: "TaskTpls",
                columns: new[] { "ProjectID", "DisplayName" },
                unique: true);
        }
    }
}
