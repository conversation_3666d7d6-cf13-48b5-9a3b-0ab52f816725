﻿using NLog;
using Plugin.Geolocator;
using Plugin.Geolocator.Abstractions;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Hub.Mobile.Utility
{
    public class MobileGPSHelper
    {
        private static ILogger logger=LogManager.GetCurrentClassLogger();
        private static Position _currentPosition;
        public static void StartListenGPS()
        {
            try
            {
                CrossGeolocator.Current.DesiredAccuracy = 200;
                Task.Run(async () =>
                {
                    while (true)
                    {
                        try
                        {
                            var currentPosition = await CrossGeolocator.Current.GetPositionAsync(TimeSpan.FromSeconds(5));
                            if (currentPosition != null)
                            {
                                _currentPosition = currentPosition;
                                Thread.Sleep(1000 * 10);
                                continue;
                            }
                        }
                        catch (Exception ex)
                        {
                            _currentPosition = null;
                        }
                        Thread.Sleep(1000);
                    }
                });
            }
            catch (Exception ex)
            {
                logger.Error(ex);
            }
        }

        public static async Task<Tuple<bool, string, Position>> GetPosition()
        {
            if (_currentPosition == null)
            {
                if (await PermissionHelper.CheckGPSPermission())
                {
                    if (!CrossGeolocator.Current.IsGeolocationAvailable || !CrossGeolocator.Current.IsGeolocationEnabled)
                    {
                        return new Tuple<bool, string, Position>(false, "please open GPS", null);
                    }
                    else
                    {
                        if (_currentPosition == null)
                        {
                            //GPS 正在获取，Wait a few seconds
                            return new Tuple<bool, string, Position>(false, "Wait a few seconds to get position", null);
                        }
                        else
                        {
                            return new Tuple<bool, string, Position>(true, "", _currentPosition);
                        }
                    }
                }
                else
                {
                    return new Tuple<bool, string, Position>(false, "no GPS permission", null);
                }
            }
            else
            {
                return new Tuple<bool, string, Position>(true, "", _currentPosition);
            }
        }

    }
}
