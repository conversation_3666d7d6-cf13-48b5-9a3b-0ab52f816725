﻿using System;
using System.Collections.Generic;
using System.Text;
using Xamarin.Forms;

namespace Hub.Mobile.Model
{
    public class ModuleInfo: AppItem
    {
        private string backgroundColor;
        public string BackgroundColor
        {
            get
            {
                if (string.IsNullOrWhiteSpace(this.backgroundColor))
                {
                    return "#FFF9F0";
                }
                else
                {
                    return this.backgroundColor;
                }
            }
            set { this.backgroundColor = value; }
        }
        public ImageSource ImageSource { get; set; }

        public int RowNumber { get; set; }
        public int ColumnNumber { get; set; }
        public int RowSpan { get; set; }
        public int ColumnSpan { get; set; }
    }
}
