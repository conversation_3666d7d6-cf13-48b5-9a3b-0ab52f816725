﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class change_emt_tables : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "UserPhone",
                table: "NetUserPositions",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(20)",
                oldMaxLength: 20,
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "UserEmail",
                table: "NetUserPositions",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldMaxLength: 50,
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "NickName",
                table: "NetUserPositions",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldMaxLength: 50,
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "CompanyName",
                table: "NetUserPositions",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "Department1",
                table: "NetUserPositions",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "Department2",
                table: "NetUserPositions",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "Department3",
                table: "NetUserPositions",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "NetUserPositions",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "EmployeeCode",
                table: "NetUserPositions",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "IdCardName",
                table: "NetUserPositions",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "IdCardNumber",
                table: "NetUserPositions",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<bool>(
                name: "IsOnTheJob",
                table: "NetUserPositions",
                type: "tinyint(1)",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "ManageEmployeeCode",
                table: "NetUserPositions",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "OpenId",
                table: "NetUserPositions",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "Position",
                table: "NetUserPositions",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "SessionKey",
                table: "NetUserPositions",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "UserAreas",
                table: "NetUserPositions",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<int>(
                name: "UserDataType",
                table: "NetUserPositions",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "UserPhoto",
                table: "NetUserPositions",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<int>(
                name: "UserSource",
                table: "NetUserPositions",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "UserType",
                table: "NetUserPositions",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "WeixinId",
                table: "NetUserPositions",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<DateTime>(
                name: "lastFaceAuthDateTime",
                table: "NetUserPositions",
                type: "datetime(6)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "GuaranteeUsers",
                table: "emt_reportorders",
                type: "varchar(2000)",
                maxLength: 2000,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<int>(
                name: "TargetCount",
                table: "emt_reportorders",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "TargetUnit",
                table: "emt_reportorders",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "CustomerEmail",
                table: "emt_executetask",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "CustomerName",
                table: "emt_executetask",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "CustomerPhone",
                table: "emt_executetask",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CompanyName",
                table: "NetUserPositions");

            migrationBuilder.DropColumn(
                name: "Department1",
                table: "NetUserPositions");

            migrationBuilder.DropColumn(
                name: "Department2",
                table: "NetUserPositions");

            migrationBuilder.DropColumn(
                name: "Department3",
                table: "NetUserPositions");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "NetUserPositions");

            migrationBuilder.DropColumn(
                name: "EmployeeCode",
                table: "NetUserPositions");

            migrationBuilder.DropColumn(
                name: "IdCardName",
                table: "NetUserPositions");

            migrationBuilder.DropColumn(
                name: "IdCardNumber",
                table: "NetUserPositions");

            migrationBuilder.DropColumn(
                name: "IsOnTheJob",
                table: "NetUserPositions");

            migrationBuilder.DropColumn(
                name: "ManageEmployeeCode",
                table: "NetUserPositions");

            migrationBuilder.DropColumn(
                name: "OpenId",
                table: "NetUserPositions");

            migrationBuilder.DropColumn(
                name: "Position",
                table: "NetUserPositions");

            migrationBuilder.DropColumn(
                name: "SessionKey",
                table: "NetUserPositions");

            migrationBuilder.DropColumn(
                name: "UserAreas",
                table: "NetUserPositions");

            migrationBuilder.DropColumn(
                name: "UserDataType",
                table: "NetUserPositions");

            migrationBuilder.DropColumn(
                name: "UserPhoto",
                table: "NetUserPositions");

            migrationBuilder.DropColumn(
                name: "UserSource",
                table: "NetUserPositions");

            migrationBuilder.DropColumn(
                name: "UserType",
                table: "NetUserPositions");

            migrationBuilder.DropColumn(
                name: "WeixinId",
                table: "NetUserPositions");

            migrationBuilder.DropColumn(
                name: "lastFaceAuthDateTime",
                table: "NetUserPositions");

            migrationBuilder.DropColumn(
                name: "GuaranteeUsers",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "TargetCount",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "TargetUnit",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "CustomerEmail",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "CustomerName",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "CustomerPhone",
                table: "emt_executetask");

            migrationBuilder.AlterColumn<string>(
                name: "UserPhone",
                table: "NetUserPositions",
                type: "varchar(20)",
                maxLength: 20,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(200)",
                oldMaxLength: 200,
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "UserEmail",
                table: "NetUserPositions",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(200)",
                oldMaxLength: 200,
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "NickName",
                table: "NetUserPositions",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(200)",
                oldMaxLength: 200,
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");
        }
    }
}
