﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_column_itrId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_QaWorkOrders_TaskItemId_TplId_TaskStepID_TriggerDay",
                table: "QaWorkOrders");

            migrationBuilder.AlterColumn<string>(
                name: "CodeSk",
                table: "ItrWorkOrders",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(100)",
                oldMaxLength: 100,
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ItrId",
                table: "ItrWorkOrders",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_QaWorkOrders_TaskItemId_TplId_TaskStepID_TriggerDay_OrderNo",
                table: "QaWorkOrders",
                columns: new[] { "TaskItemId", "TplId", "TaskStepID", "TriggerDay", "OrderNo" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_QaWorkOrders_TaskItemId_TplId_TaskStepID_TriggerDay_OrderNo",
                table: "QaWorkOrders");

            migrationBuilder.DropColumn(
                name: "ItrId",
                table: "ItrWorkOrders");

            migrationBuilder.AlterColumn<string>(
                name: "CodeSk",
                table: "ItrWorkOrders",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(255)",
                oldMaxLength: 255,
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_QaWorkOrders_TaskItemId_TplId_TaskStepID_TriggerDay",
                table: "QaWorkOrders",
                columns: new[] { "TaskItemId", "TplId", "TaskStepID", "TriggerDay" },
                unique: true);
        }
    }
}
