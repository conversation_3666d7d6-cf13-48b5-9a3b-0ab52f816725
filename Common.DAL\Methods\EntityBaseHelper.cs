﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.DAL.Methods
{
    public static class EntityBaseHelper
    {
        public static TEntity Update<TContext, TEntity>(TContext context, TEntity entity, string name)
           where TEntity : EntityBase
           where TContext : DbContext
        {
            entity.ModifiedDateTime = DateTime.UtcNow;
            if (!string.IsNullOrWhiteSpace(name))
            {
                entity.ModifiedUser = name;
            }
            else
            {
                entity.ModifiedUser = HttpContextHelper.Current != null ? HttpContextHelper.Current.User.Identity.Name : "sys";
            }
            context.SaveChanges();
            return entity;
        }

        public static TEntity Add<TContext, TEntity>(TContext context, TEntity entity, string name)
         where TEntity : EntityBase
         where TContext : DbContext
        {
            if (string.IsNullOrWhiteSpace(entity.ID))
            {
                entity.ID = Guid.NewGuid().ToString();
            }
            entity.IsActived = true;
            if (entity.CreatedDateTime == null)
                entity.CreatedDateTime = DateTime.UtcNow;
            if (!string.IsNullOrWhiteSpace(name))
            {
                entity.CreatedUser = name;
            }
            else
            {
                entity.CreatedUser = HttpContextHelper.Current != null ? HttpContextHelper.Current.User.Identity.Name : "sys";
            }
            context.Set<TEntity>().Add(entity);
            context.SaveChanges();
            return entity;
        }

        public static TEntity Update<TContext, TEntity>(TContext context, TEntity entity)
            where TEntity : EntityBase
            where TContext : DbContext
        {
            entity.ModifiedDateTime = DateTime.UtcNow;
            entity.ModifiedUser = HttpContextHelper.Current != null ? HttpContextHelper.Current.User.Identity.Name : "sys";
            //entity.ModifiedUser = HttpContext.Current?.User != null ? HttpContext.Current.User.FindFirst(JwtClaimTypes.Subject)?.Value : "sys";
            context.SaveChanges();
            return entity;
        }

        public static TEntity Add<TContext, TEntity>(TContext context, TEntity entity)
         where TEntity : EntityBase
         where TContext : DbContext
        {
            if (string.IsNullOrWhiteSpace(entity.ID))
            {
                entity.ID = Guid.NewGuid().ToString();
            }
            entity.IsActived = true;
            if (entity.CreatedDateTime == null)
                entity.CreatedDateTime = DateTime.UtcNow;
            if (entity.CreatedUser == null)
                entity.CreatedUser = HttpContextHelper.Current != null ? HttpContextHelper.Current.User.Identity.Name : "sys";
            //entity.CreatedUser= HttpContext.Current?.User != null ? HttpContext.Current.User.FindFirst(JwtClaimTypes.Subject)?.Value : "sys";
            context.Set<TEntity>().Add(entity);
            context.SaveChanges();
            return entity;
        }

        public static void Delete<TContext, TEntity>(TContext context, TEntity entity, bool indeed = true)
         where TEntity : EntityBase
         where TContext : DbContext
        {
            if (!indeed)
            {
                entity.IsActived = false;
                Update<TContext, TEntity>(context, entity);
            }
            else
            {
                context.Set<TEntity>().Remove(entity);
                context.SaveChanges();
            }
        }
        public static void Delete<TContext, TEntity>(TContext context, List<TEntity> entities, bool indeed = true)
         where TEntity : EntityBase
         where TContext : DbContext
        {
            if (!indeed)
            {
                entities.ConvertAll(i => i.IsActived == false);
                Update<TContext, TEntity>(context, entities);
            }
            else
            {
                context.Set<TEntity>().RemoveRange(entities);
                context.SaveChanges();
            }
        }

        public static List<TEntity> Update<TContext, TEntity>(TContext context, List<TEntity> entities, bool espacially = false)
            where TEntity : EntityBase
            where TContext : DbContext
        {
            if (!espacially)
            {
                string modifiedUser = HttpContextHelper.Current != null ? HttpContextHelper.Current.User.Identity.Name : "sys";
                DateTime modifiedDateTime = DateTime.UtcNow;
                foreach (var entity in entities)
                {
                    entity.ModifiedDateTime = modifiedDateTime;
                    entity.ModifiedUser = modifiedUser;
                }
            }
            context.Set<TEntity>().UpdateRange(entities);
            context.SaveChanges();
            return entities;
        }
        public static List<TEntity> Add<TContext, TEntity>(TContext context, List<TEntity> entities, bool espacially = false)
         where TEntity : EntityBase
         where TContext : DbContext
        {
            foreach (var entity in entities)
            {
                if (string.IsNullOrWhiteSpace(entity.ID))
                {
                    entity.ID = Guid.NewGuid().ToString();
                }
                if (!espacially)
                {
                    entity.IsActived = true;
                    entity.CreatedDateTime = DateTime.UtcNow;
                    entity.CreatedUser = HttpContextHelper.Current != null ? HttpContextHelper.Current.User.Identity.Name : "sys";
                    //entity.CreatedUser= HttpContext.Current?.User != null ? HttpContext.Current.User.FindFirst(JwtClaimTypes.Subject)?.Value : "sys";
                }
            }
            context.Set<TEntity>().AddRange(entities);
            context.SaveChanges();
            return entities;
        }
        public static string GetCurrentUser() => HttpContextHelper.Current?.User?.Identity?.Name ?? "sys";

        public static string GetCurrentUserNickName()
        {
            string name = "";
            var user = HttpContextHelper.Current.User.Claims.Where(i => i.Type == "NickName").FirstOrDefault();
            if (user != null)
            {
                name = user.Value;
            }
            else
            {
                name = HttpContextHelper.Current?.User?.Identity?.Name ?? "sys";
                if (!String.IsNullOrWhiteSpace(name))
                    if (name.Contains("@"))
                        name = name.Split("@")[0];
            }
            return name;
        }
    }
}
