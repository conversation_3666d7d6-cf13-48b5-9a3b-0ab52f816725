﻿using Common.Model;
using Common.Repository;
using Common.Service;
using Common.Utility;
using Hub.DAL.Table;
using Hub.Env;
using Hub.Model;
using Hub.Repository;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hub.Service
{
    public class AppService : SimpleService<AppDto, App>
    {
        public AppService(AppRepository repository) : base(repository) { }

        public override AppDto Insert(AppDto entity)
        {
            if (entity.AppMode == HubAppMode.Hybrid)
                return UpdateOrAddApp(entity, true);
            else
                return UpdateOrAddApp(entity, false);
        }

        public async Task<AppDto> UploadHybridApp(AppFileDto req)
        {
            if (req?.File?.Length > 0)
            {
                if (req.AppMode == HubAppMode.Hybrid)
                {
                    var filename = Path.GetFileName(req.File.FileName);
                    var ext = Path.GetExtension(filename);
                    if (ext.ToLower() == ".zip")
                    {
                        var appcode = req.AppCode.ToLower();
                        var dir = ConfigEnvValues.HybridAppFolderFullPath;
                        var fullpath = Path.Combine(dir, appcode, filename);
                        if (!Directory.Exists(dir))
                        {
                            Directory.CreateDirectory(dir);
                        }
                        if (File.Exists(fullpath))
                        {
                            File.Delete(fullpath);
                        }
                        using (var fs = File.Create(fullpath))
                        {
                            await req.File.CopyToAsync(fs);
                        }
                        var cdnUrl = CdnAddressHelper.GetCdnAddress($"/{ConfigEnvValues.HybridAppFolderName}/{appcode}/{filename}");
                        req.URL = cdnUrl;
                    }
                    return UpdateOrAddApp(req, true);
                }
                else
                {
                    throw new MessageException("No need to upload an online app");
                }
            }
            else
            {
                throw new MessageException("File is empty");
            }
        }

        public override AppDto Update(AppDto entity)
        {
            return UpdateOrAddApp(entity, false);
        }

        public AppDownloadDto GetAppDownloadInfo(string appCode)
        {
            var query = from q in _repository.Query(q => q.IsActived && q.AppCode == appCode)
                        select new AppDownloadDto { AppCode = q.AppCode, Url = q.URL, Version = q.HybridAppVersion };
            return query.FirstOrDefault();
        }

        private AppDto UpdateOrAddApp(AppDto entity, bool versionChanged)
        {
            if (versionChanged)
            {
                entity.HybridAppVersion = 1;
            }

            var existApp = _repository.Query(x => x.IsActived && x.AppCode == entity.AppCode)
                    .OrderByDescending(x => x.HybridAppVersion).FirstOrDefault();

            //存在历史版本
            if (existApp != null)
            {
                if (existApp.AppMode == HubAppMode.Hybrid)
                {
                    var id = existApp.ID;
                    if (versionChanged)
                    {
                        var version = existApp.HybridAppVersion + 1;
                        existApp.HybridAppVersion = version;
                    }
                    AutoMapperConfig.Mapper.Map(entity, existApp);
                    existApp.ID = id;
                    return base.Update(entity);
                }
                else
                {
                    var id = existApp.ID;
                    AutoMapperConfig.Mapper.Map(entity, existApp);
                    existApp.ID = id;
                    return base.Update(existApp);
                }
            }
            else//新增小程序
            {
                entity.HybridAppVersion = 0;
                return base.Insert(entity);
            }
        }
    }
}
