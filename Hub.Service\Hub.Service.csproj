<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="11.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="11.0.0" />
    <PackageReference Include="NLog" Version="5.0.0" />
    <PackageReference Include="Z.EntityFramework.Plus.EFCore" Version="5.14.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Common.Model\Common.Model.csproj" />
    <ProjectReference Include="..\Common.Repository\Common.Repository.csproj" />
    <ProjectReference Include="..\Common.Service\Common.Service.csproj" />
    <ProjectReference Include="..\Common.Utility\Common.Utility.csproj" />
    <ProjectReference Include="..\Hub.DAL\Hub.DAL.csproj" />
    <ProjectReference Include="..\Hub.Env\Hub.Env.csproj" />
    <ProjectReference Include="..\Hub.Model\Hub.Model.csproj" />
    <ProjectReference Include="..\Hub.Repository\Hub.Repository.csproj" />
  </ItemGroup>

</Project>
