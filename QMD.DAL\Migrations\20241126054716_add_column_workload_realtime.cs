﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_column_workload_realtime : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "RealEndTime",
                table: "emt_netproviderrel",
                type: "datetime(6)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "RealStartTime",
                table: "emt_netproviderrel",
                type: "datetime(6)",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "WorkLoad",
                table: "emt_executetask",
                type: "double",
                nullable: false,
                defaultValue: 0.0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RealEndTime",
                table: "emt_netproviderrel");

            migrationBuilder.DropColumn(
                name: "RealStartTime",
                table: "emt_netproviderrel");

            migrationBuilder.DropColumn(
                name: "WorkLoad",
                table: "emt_executetask");
        }
    }
}
