﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_table_involveuser : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ApplyUserName",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "BoardSoftName",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "CloseStatus",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "CloseUserName",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "CopyUsers",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "<PERSON>urOperator",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "DeviceCount",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "FirstApprover",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "ForthApprover",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "GuaranteeUsers",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "MainNickName",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "MainUserName",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "SecondApprover",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "SystemSoftName",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "ThirdApprover",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "VersionCode",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "CurOperator",
                table: "emt_netproviderrel");

            migrationBuilder.DropColumn(
                name: "DelayApprover",
                table: "emt_netproviderrel");

            migrationBuilder.DropColumn(
                name: "OperateUser",
                table: "emt_netproviderrel");

            migrationBuilder.DropColumn(
                name: "RepOrderNo",
                table: "emt_netproviderrel");

            migrationBuilder.DropColumn(
                name: "ApplyDept",
                table: "emt_dispatchorders");

            migrationBuilder.DropColumn(
                name: "ApplyEmail",
                table: "emt_dispatchorders");

            migrationBuilder.DropColumn(
                name: "ApplyNickName",
                table: "emt_dispatchorders");

            migrationBuilder.DropColumn(
                name: "ApplyPhone",
                table: "emt_dispatchorders");

            migrationBuilder.DropColumn(
                name: "ApplyPostCode",
                table: "emt_dispatchorders");

            migrationBuilder.DropColumn(
                name: "ApplyPostName",
                table: "emt_dispatchorders");

            migrationBuilder.DropColumn(
                name: "ApplyUserName",
                table: "emt_dispatchorders");

            migrationBuilder.DropColumn(
                name: "CopyUsers",
                table: "emt_dispatchorders");

            migrationBuilder.DropColumn(
                name: "CurOperator",
                table: "emt_dispatchorders");

            migrationBuilder.DropColumn(
                name: "IsNeedGuarantee",
                table: "emt_dispatchorders");

            migrationBuilder.RenameColumn(
                name: "SystemSoftCompileTime",
                table: "emt_reportorders",
                newName: "RealStartTime");

            migrationBuilder.RenameColumn(
                name: "BoardSoftCompileTime",
                table: "emt_reportorders",
                newName: "RealEndTime");

            migrationBuilder.RenameColumn(
                name: "DepartMent",
                table: "emt_dispatchorders",
                newName: "NmpVersions");

            migrationBuilder.RenameColumn(
                name: "CheckResult",
                table: "emt_dispatchorders",
                newName: "NmpSeries");

            migrationBuilder.AlterColumn<string>(
                name: "AttchFileNames",
                table: "emt_reportorders",
                type: "varchar(1000)",
                maxLength: 1000,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(255)",
                oldMaxLength: 255,
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "AttachInfo",
                table: "emt_reportorders",
                type: "varchar(2000)",
                maxLength: 2000,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(255)",
                oldMaxLength: 255,
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<DateTime>(
                name: "ApplyTime",
                table: "emt_reportorders",
                type: "datetime(6)",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "DeviceTypes",
                table: "emt_dispatchorders",
                type: "varchar(1000)",
                maxLength: 1000,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(500)",
                oldMaxLength: 500,
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<DateTime>(
                name: "ApproveTime",
                table: "emt_dispatchorders",
                type: "datetime(6)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BoardNames",
                table: "emt_dispatchorders",
                type: "varchar(1000)",
                maxLength: 1000,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "HardWares",
                table: "emt_dispatchorders",
                type: "varchar(1000)",
                maxLength: 1000,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "NmpPatches",
                table: "emt_dispatchorders",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "SoftWares",
                table: "emt_dispatchorders",
                type: "varchar(1000)",
                maxLength: 1000,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "emt_involveuser",
                columns: table => new
                {
                    ID = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    OrderKind = table.Column<int>(type: "int", nullable: false),
                    OrderNo = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UserType = table.Column<int>(type: "int", nullable: false),
                    InvUserName = table.Column<string>(type: "varchar(255)", maxLength: 255, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    InvNickName = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    InvPhone = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    InvEmail = table.Column<string>(type: "varchar(255)", maxLength: 255, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    InvPostCode = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    InvPostName = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    InvDept = table.Column<string>(type: "varchar(200)", maxLength: 200, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    InvUserLevel = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    InvOrder = table.Column<int>(type: "int", nullable: false),
                    Remark = table.Column<string>(type: "varchar(255)", maxLength: 255, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    IsActived = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    ModifiedDateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedDateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedUser = table.Column<string>(type: "varchar(128)", maxLength: 128, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ModifiedUser = table.Column<string>(type: "varchar(128)", maxLength: 128, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_emt_involveuser", x => x.ID);
                })
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "emt_involveuser");

            migrationBuilder.DropColumn(
                name: "ApplyTime",
                table: "emt_reportorders");

            migrationBuilder.DropColumn(
                name: "ApproveTime",
                table: "emt_dispatchorders");

            migrationBuilder.DropColumn(
                name: "BoardNames",
                table: "emt_dispatchorders");

            migrationBuilder.DropColumn(
                name: "HardWares",
                table: "emt_dispatchorders");

            migrationBuilder.DropColumn(
                name: "NmpPatches",
                table: "emt_dispatchorders");

            migrationBuilder.DropColumn(
                name: "SoftWares",
                table: "emt_dispatchorders");

            migrationBuilder.RenameColumn(
                name: "RealStartTime",
                table: "emt_reportorders",
                newName: "SystemSoftCompileTime");

            migrationBuilder.RenameColumn(
                name: "RealEndTime",
                table: "emt_reportorders",
                newName: "BoardSoftCompileTime");

            migrationBuilder.RenameColumn(
                name: "NmpVersions",
                table: "emt_dispatchorders",
                newName: "DepartMent");

            migrationBuilder.RenameColumn(
                name: "NmpSeries",
                table: "emt_dispatchorders",
                newName: "CheckResult");

            migrationBuilder.AlterColumn<string>(
                name: "AttchFileNames",
                table: "emt_reportorders",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(1000)",
                oldMaxLength: 1000,
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "AttachInfo",
                table: "emt_reportorders",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(2000)",
                oldMaxLength: 2000,
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ApplyUserName",
                table: "emt_reportorders",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "BoardSoftName",
                table: "emt_reportorders",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<int>(
                name: "CloseStatus",
                table: "emt_reportorders",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "CloseUserName",
                table: "emt_reportorders",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "CopyUsers",
                table: "emt_reportorders",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "CurOperator",
                table: "emt_reportorders",
                type: "longtext",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<int>(
                name: "DeviceCount",
                table: "emt_reportorders",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "FirstApprover",
                table: "emt_reportorders",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ForthApprover",
                table: "emt_reportorders",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "GuaranteeUsers",
                table: "emt_reportorders",
                type: "varchar(2000)",
                maxLength: 2000,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "MainNickName",
                table: "emt_reportorders",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "MainUserName",
                table: "emt_reportorders",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "SecondApprover",
                table: "emt_reportorders",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "SystemSoftName",
                table: "emt_reportorders",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ThirdApprover",
                table: "emt_reportorders",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "VersionCode",
                table: "emt_reportorders",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "CurOperator",
                table: "emt_netproviderrel",
                type: "longtext",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "DelayApprover",
                table: "emt_netproviderrel",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "OperateUser",
                table: "emt_netproviderrel",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "RepOrderNo",
                table: "emt_netproviderrel",
                type: "varchar(32)",
                maxLength: 32,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "DeviceTypes",
                table: "emt_dispatchorders",
                type: "varchar(500)",
                maxLength: 500,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(1000)",
                oldMaxLength: 1000,
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ApplyDept",
                table: "emt_dispatchorders",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ApplyEmail",
                table: "emt_dispatchorders",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ApplyNickName",
                table: "emt_dispatchorders",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ApplyPhone",
                table: "emt_dispatchorders",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ApplyPostCode",
                table: "emt_dispatchorders",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ApplyPostName",
                table: "emt_dispatchorders",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ApplyUserName",
                table: "emt_dispatchorders",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "CopyUsers",
                table: "emt_dispatchorders",
                type: "varchar(500)",
                maxLength: 500,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "CurOperator",
                table: "emt_dispatchorders",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<bool>(
                name: "IsNeedGuarantee",
                table: "emt_dispatchorders",
                type: "tinyint(1)",
                nullable: false,
                defaultValue: false);
        }
    }
}
