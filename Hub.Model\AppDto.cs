﻿using Hub.DAL.Table;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hub.Model
{
    public class AppDto : App
    {
    }

    public class AppFileDto : AppDto
    {
        [JsonIgnore]
        public IFormFile File { get; set; }
    }

    public class AppDownloadDto
    {
        public string Url { get; set; }
        public int Version { get; set; }
        public string AppCode { get; set; }
    }
}
