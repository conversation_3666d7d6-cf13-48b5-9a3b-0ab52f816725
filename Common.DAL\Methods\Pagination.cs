﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Linq.Dynamic.Core;

namespace Common.DAL.Methods
{
    public static class Pagination
    {
        public static PageRes<T> PageAndSort<T>(this IQueryable<T> query, PageCriteria criteria) where T : EntityBase
        {
            string orderCondition = string.Empty;
            if (criteria.Sort?.Count() > 0)
            {
                orderCondition = string.Join(',', criteria.Sort.OrderBy(p => p.SortOrder).Select(p => { return p.IsDescending ? $"{p.SortField} desc" : $"{p.SortField} asc"; }));
            }
            else
            {
                if (criteria.GetDefaultSort()?.Count() > 0)
                {
                    orderCondition = string.Join(',', criteria.GetDefaultSort().OrderBy(p => p.SortOrder).Select(p => { return p.IsDescending ? $"{p.SortField} desc" : $"{p.SortField} asc"; }));
                }
            }
            if (!string.IsNullOrEmpty(orderCondition))
            {
                query = query.OrderBy(orderCondition);
            }
            var count = query.Count();
            if (criteria.PageNumber.GetValueOrDefault(0) > 0)
            {
                if (criteria.PageNumber.GetValueOrDefault(0) > 1)
                {
                    int skipCount = criteria.PageNumber.Value - 1;
                    query = query.Skip(skipCount * criteria.PageSize.Value).Take(criteria.PageSize.Value);
                }
                else
                {
                    query = query.Take(criteria.PageSize.Value);
                }
            }
            PageRes<T> param = new PageRes<T>();
            param.Data = query.ToList();
            param.TotalCount = count;
            return param;
        }

        public static List<T> PageAndSort<T>(this List<T> data, PageCriteria criteria, out int totalCount)
        {
            if (data == null)
            {
                throw new ArgumentNullException(nameof(data));
            }
            if (criteria == null)
            {
                throw new ArgumentNullException(nameof(criteria));
            }

            IOrderedEnumerable<T> sortedData = data.OrderBy(x => 0);

            if (criteria.Sort != null)
            {
                foreach (var sortCriteria in criteria.Sort)
                {
                    if (sortCriteria.IsDescending)
                    {
                        sortedData = sortedData.ThenByDescending(item => item.GetType().GetProperty(sortCriteria.SortField)?.GetValue(item, null));
                    }
                    else
                    {
                        sortedData = sortedData.ThenBy(item => item.GetType().GetProperty(sortCriteria.SortField)?.GetValue(item, null));
                    }
                }
            }

            totalCount = sortedData.Count();

            List<T> pagedData = sortedData
                .Skip((criteria.PageNumber.GetValueOrDefault(1) - 1) * criteria.PageSize.GetValueOrDefault(10))
                .Take(criteria.PageSize.GetValueOrDefault(10))
                .ToList();
            return pagedData;
        }
    }

    public class PageRes<T>:BaseRes
    {
        public List<T> Data { get; set; }
        public int TotalCount { get; set; }
    }
}
