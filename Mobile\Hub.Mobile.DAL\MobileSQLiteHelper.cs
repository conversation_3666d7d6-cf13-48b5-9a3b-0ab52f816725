﻿using Hub.Mobile.Const;
using Hub.Mobile.DAL.Tables;
using SQLite;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace Hub.Mobile.DAL
{
    public class MobileSQLiteHelper
    {
        private static SQLiteAsyncConnection connectionAsync;
        private static readonly object locker = new object();
        private static MobileSQLiteHelper instance = null;
        private static Lazy<SQLiteAsyncConnection> _db =
            new Lazy<SQLiteAsyncConnection>(() =>
            {
                if (connectionAsync == null)
                {
                    lock (locker)
                    {
                        if (connectionAsync == null)
                        {
                            string documentsPath = System.Environment.GetFolderPath(System.Environment.SpecialFolder.LocalApplicationData); // Documents folder
                            var dbPath = Path.Combine(documentsPath, MobileCommonConsts.SqliteDBFileName);
                            connectionAsync = new SQLiteAsyncConnection(dbPath,
                                SQLite.SQLiteOpenFlags.ReadWrite
                                | SQLite.SQLiteOpenFlags.Create
                                | SQLite.SQLiteOpenFlags.SharedCache);
                        }
                    }
                }
                return connectionAsync;
            });
        public void CreateOrUpdateTables()
        {
            _db.Value.CreateTableAsync<Tables.DataCache>().Wait();
            _db.Value.CreateTableAsync<Tables.LoginUser>().Wait();

        }
        static MobileSQLiteHelper()
        {
            instance = new MobileSQLiteHelper();
            instance.CreateOrUpdateTables();
        }

        public SQLiteAsyncConnection DbContext
        {
            get
            {
                return _db.Value;
            }
        }

        public static MobileSQLiteHelper Current
        {
            get
            {
                return instance;
            }
        }

        public async Task<T> Query<T>(string id) where T : TableBase, new()
        {
            return await _db.Value.Table<T>().Where(a => a.Id == id).FirstOrDefaultAsync();
        }

        public async Task<T> Query<T>(System.Linq.Expressions.Expression<Func<T, bool>> query) where T : TableBase, new()
        {
            return await _db.Value.Table<T>().Where(query).FirstOrDefaultAsync();
        }


        public async Task<List<T>> QueryList<T>() where T : TableBase, new()
        {
            return await _db.Value.Table<T>().OrderByDescending(a => a.ModifiedTime).ToListAsync();
        }

        public async Task<List<T>> QueryList<T>(System.Linq.Expressions.Expression<Func<T, bool>> query) where T : TableBase, new()
        {
            try
            {

                return await _db.Value.Table<T>().Where(query).OrderByDescending(i => i.ModifiedTime).ToListAsync();
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<List<T>> QueryList<T, U>(System.Linq.Expressions.Expression<Func<T, bool>> query, System.Linq.Expressions.Expression<Func<T, U>> orderBy) where T : TableBase, new()
        {
            return await _db.Value.Table<T>().Where(query).OrderByDescending(orderBy).ToListAsync();
        }

        public async Task<List<T>> QueryList<T>(int takeCount) where T : TableBase, new()
        {
            return await _db.Value.Table<T>().OrderByDescending(a => a.ModifiedTime).Take(takeCount).ToListAsync();
        }

        public async Task<List<T>> QueryList<T>(System.Linq.Expressions.Expression<Func<T, bool>> query, int takeCount) where T : TableBase, new()
        {
            return await _db.Value.Table<T>().Where(query).OrderByDescending(i => i.ModifiedTime).Take(takeCount).ToListAsync();
        }

        public async Task<List<T>> QueryList<T, U>(System.Linq.Expressions.Expression<Func<T, bool>> query, System.Linq.Expressions.Expression<Func<T, U>> orderBy, int takeCount) where T : TableBase, new()
        {
            return await _db.Value.Table<T>().Where(query).OrderByDescending(orderBy).Take(takeCount).ToListAsync();
        }

        public async Task<List<T>> QueryList<T>(int takeCount, int skipCount) where T : TableBase, new()
        {
            return await _db.Value.Table<T>().OrderByDescending(a => a.ModifiedTime).Skip(skipCount).Take(takeCount).ToListAsync();
        }

        public async Task<List<T>> QueryList<T, U>(System.Linq.Expressions.Expression<Func<T, bool>> query, int takeCount, int skipCount) where T : TableBase, new()
        {
            return await _db.Value.Table<T>().Where(query).OrderByDescending(i => i.ModifiedTime).Skip(skipCount).Take(takeCount).ToListAsync();
        }

        public async Task<List<T>> QueryList<T, U>(System.Linq.Expressions.Expression<Func<T, bool>> query, System.Linq.Expressions.Expression<Func<T, U>> orderBy, int takeCount, int skipCount) where T : TableBase, new()
        {
            return await _db.Value.Table<T>().Where(query).OrderByDescending(orderBy).Skip(skipCount).Take(takeCount).ToListAsync();
        }

        public async Task<T> FirstOrDefaultByCreatedTime<T>() where T : TableBase, new()
        {
            return await _db.Value.Table<T>().OrderByDescending(i => i.CreatedTime).FirstOrDefaultAsync();
        }

        public async Task<T> FirstOrDefaultByModifiedTime<T>() where T : TableBase, new()
        {
            return await _db.Value.Table<T>().OrderByDescending(i => i.ModifiedTime).FirstOrDefaultAsync();
        }

        public async Task Update<T>(T model, bool updateModifiedTime = true) where T : TableBase
        {
            try
            {
                if (updateModifiedTime)
                {
                    model.ModifiedTime = DateTime.UtcNow;
                }
                if (model.CreatedTime == null)
                {
                    model.CreatedTime = DateTime.UtcNow;
                }
                await _db.Value.UpdateAsync(model);
            }
            catch (Exception ex)
            {

            }
        }

        public async Task UpdateBatch<T>(List<T> models, bool updateModifiedTime = true) where T : TableBase
        {
            try
            {
                models.ForEach(x => {
                    if (updateModifiedTime)
                    {
                        x.ModifiedTime = DateTime.UtcNow;
                    }
                    if (x.CreatedTime == null)
                    {
                        x.CreatedTime = DateTime.UtcNow;
                    }
                });
                await _db.Value.UpdateAllAsync(models);
            }
            catch (Exception ex)
            {

            }
        }

        public async Task Insert<T>(T model) where T : TableBase
        {
            model.ModifiedTime = model.CreatedTime = DateTime.UtcNow;
            await _db.Value.InsertAsync(model);
        }


        public async Task<int> Insert<T>(List<T> models) where T : TableBase
        {
            if (models != null && models.Count > 0)
            {
                models.ForEach(i =>
                {
                    i.CreatedTime = DateTime.UtcNow;
                    i.ModifiedTime = DateTime.UtcNow;
                });
                return await _db.Value.InsertAllAsync(models, true);
            }
            return 0;
        }

        public async Task<int> Delete<T>(T model) where T : TableBase
        {
            int rowDeleted = await _db.Value.DeleteAsync(model);
            return rowDeleted;
        }

        public async Task<int> Delete<T>(string Id) where T : TableBase
        {
            int rowDeleted = await _db.Value.DeleteAsync<T>(Id);
            return rowDeleted;
        }


        public async Task<int> DeleteAll<T>() where T : TableBase
        {
            return await _db.Value.DeleteAllAsync<T>();
        }

        ///// <summary>
        ///// 批量删除数据
        ///// </summary>
        ///// <typeparam name="T"></typeparam>
        ///// <param name="modelCollections"></param>
        ///// <returns></returns>
        //public async Task<int> DeleteBatch<T>(List<T> modelCollections) where T : TableBase
        //{
        //    int result = 0;
        //    modelCollections.ForEach(async (x) =>
        //    {
        //        result += await Delete<T>(x);
        //    });
        //    return result;
        //}
    }
}
