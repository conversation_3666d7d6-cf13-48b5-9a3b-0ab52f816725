﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_itrworkorder_field_1 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<double>(
                name: "CurOrderProgress",
                table: "ItrWorkOrders",
                type: "double",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "RemainSLATime",
                table: "ItrWorkOrders",
                type: "double",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CurOrderProgress",
                table: "ItrWorkOrders");

            migrationBuilder.DropColumn(
                name: "RemainSLATime",
                table: "ItrWorkOrders");
        }
    }
}
