﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class change_table_column_length : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_TaskStepValues_ProjectID_TaskItemID_TaskTplID_IsActived",
                table: "TaskStepValues",
                columns: new[] { "ProjectID", "TaskItemID", "TaskTplID", "IsActived" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_TaskStepValues_ProjectID_TaskItemID_TaskTplID_IsActived",
                table: "TaskStepValues");
        }
    }
}
