﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_dispatchorder_somecolumns : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ApplyDepName",
                table: "emt_dispatchorders",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<int>(
                name: "RoleDataType",
                table: "emt_dispatchorders",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "TechNoticeType",
                table: "emt_dispatchorders",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ApplyDepName",
                table: "emt_dispatchorders");

            migrationBuilder.DropColumn(
                name: "RoleDataType",
                table: "emt_dispatchorders");

            migrationBuilder.DropColumn(
                name: "TechNoticeType",
                table: "emt_dispatchorders");
        }
    }
}
