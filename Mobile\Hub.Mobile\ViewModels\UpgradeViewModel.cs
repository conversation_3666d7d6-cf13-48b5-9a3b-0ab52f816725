﻿using Hub.Mobile.ApiClient;
using Hub.Mobile.Const;
using Hub.Mobile.Interface;
using Hub.Mobile.Model;
using Hub.Mobile.Resources;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using WebApiClient;
using Xamarin.Forms;

namespace Hub.Mobile.ViewModels
{
    public class UpgradeViewModel : BasePageViewModel
    {
        public Command CloseCommand { get; }
        public UpgradeViewModel(string fileName)
        {
            CloseCommand = new Command(OnCloseClicked);
            Task.Factory.StartNew(() =>
            {
                try
                {
                    var response = HttpApi.Resolve<IHubApiClient>().GetAppDownloadUrlResponse(fileName).Result;
                    if (response.Flag && !string.IsNullOrWhiteSpace(response.Data))
                    {
                        DependencyService.Get<IUpgrade>().DoUpgrade(response.Data, fileName, UpdateCallback);
                    }
                }
                catch (Exception ex)
                {
                    Tip = ex?.Message;
                }
                finally
                {
                    BtnCloseEnabled = true;
                }

            });
        }

        private async void OnCloseClicked(object obj)
        {
            await Navigation.PopPopupAsync();
        }

        private string tip = string.Empty;
        public string Tip
        {
            get { return tip; }
            set
            {
                SetProperty(ref tip, value);
            }
        }
        bool btnCloseEnabled = false;
        public bool BtnCloseEnabled { get { return btnCloseEnabled; } set { SetProperty(ref btnCloseEnabled, value); } }
        private double progress = 0;
        public double Progress
        {
            get { return progress; }
            set
            {
                SetProperty(ref progress, value);
            }
        }
        public void UpdateCallback(EnumUpgradeState state, object data)
        {
            switch (state)
            {
                case EnumUpgradeState.正在下载:
                    Tip = UIResources.Downloading;
                    Progress = (double)data;
                    break;
                case EnumUpgradeState.下载失败:
                    Tip = UIResources.DownloadFailed;
                    BtnCloseEnabled = true;
                    break;
                case EnumUpgradeState.正在解压:
                    Tip = UIResources.Decompressing;
                    break;
                case EnumUpgradeState.解压失败:
                    Tip = UIResources.DecompressFailed;
                    BtnCloseEnabled = true;
                    break;
                case EnumUpgradeState.安装失败:
                    Tip = UIResources.InstallFailed;
                    BtnCloseEnabled = true;
                    break;
                case EnumUpgradeState.解压完成:
                    Tip = UIResources.DecompressCompleted;
                    break;
                case EnumUpgradeState.已启动安装:
                    BtnCloseEnabled = true;
                    break;
            }
        }
    }
}
