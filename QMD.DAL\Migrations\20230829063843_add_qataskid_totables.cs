﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_qataskid_totables : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ExtValue7",
                table: "TaskStepValues",
                type: "varchar(512)",
                maxLength: 512,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ExtValue8",
                table: "TaskStepValues",
                type: "varchar(512)",
                maxLength: 512,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "QaTaskId",
                table: "QaWorkOrders",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "QaTaskId",
                table: "QaOrders",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ExtValue7",
                table: "TaskStepValues");

            migrationBuilder.DropColumn(
                name: "ExtValue8",
                table: "TaskStepValues");

            migrationBuilder.DropColumn(
                name: "QaTaskId",
                table: "QaWorkOrders");

            migrationBuilder.DropColumn(
                name: "QaTaskId",
                table: "QaOrders");
        }
    }
}
