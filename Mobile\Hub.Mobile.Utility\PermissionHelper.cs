﻿using Plugin.Permissions;
using Plugin.Permissions.Abstractions;
using System;
using System.Threading.Tasks;

namespace Hub.Mobile.Utility
{
    public static class PermissionHelper
    {
        public static async Task<bool> CheckPermission<T>() where T : BasePermission, new()
        {
            var permissions = CrossPermissions.Current;

            var permissionStatus = await permissions.CheckPermissionStatusAsync<T>();

            if (permissionStatus != PermissionStatus.Granted)
            {
                permissionStatus = await permissions.RequestPermissionAsync<T>();
            }

            return permissionStatus == PermissionStatus.Granted;
        }

        public static async Task<bool> CheckStoragePermission()
        {
            var permissions = CrossPermissions.Current;

            var storageStatus = await permissions.CheckPermissionStatusAsync<StoragePermission>();

            if (storageStatus != PermissionStatus.Granted)
            {
                storageStatus = await permissions.RequestPermissionAsync<StoragePermission>();
            }

            return storageStatus == PermissionStatus.Granted;
        }

        public static async Task<bool> CheckGPSPermission()
        {
            var permissions = CrossPermissions.Current;

            var storageStatus = await permissions.CheckPermissionStatusAsync<LocationPermission>();

            if (storageStatus != PermissionStatus.Granted)
            {
                storageStatus = await permissions.RequestPermissionAsync<LocationPermission>();
            }

            return storageStatus == PermissionStatus.Granted;
        }
    }
}
