﻿using Hub.Mobile.ApiClient;
using Hub.Mobile.ApiClient.Hub;
using Hub.Mobile.Const;
using Hub.Mobile.Interface;
using Hub.Mobile.Model;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WebApiClient;

[assembly: Xamarin.Forms.Dependency(typeof(Hub.Mobile.Services.AppService))]
namespace Hub.Mobile.Services
{
    public class AppService:IApp
    {
        private string appConfigFilePath = Path.Combine(System.Environment.GetFolderPath(System.Environment.SpecialFolder.LocalApplicationData), "apps_config.json");
        private static string hybridAppBaseDir = Path.Combine(System.Environment.GetFolderPath(System.Environment.SpecialFolder.LocalApplicationData),"Web");
        private string hybridVersionFilePath =Path.Combine(hybridAppBaseDir, "package.json");
        static AppService()
        {
            if (!Directory.Exists(hybridAppBaseDir))
            {
                Directory.CreateDirectory(hybridAppBaseDir);
            }
        }
        /// <summary>
        /// 存储App配置
        /// </summary>
        /// <param name="apps"></param>
        /// <returns>true 配置有变化， false 配置没有变化</returns>
        public bool SaveAppConfig(List<AppItem> apps)
        {
            bool changed = false;
            if (apps != null)
            {
                string currentConfigData = string.Empty;
                if (File.Exists(appConfigFilePath))
                {
                    currentConfigData = File.ReadAllText(appConfigFilePath);
                }
                var newConfigData = JsonConvert.SerializeObject(apps.OrderBy(p => p.Order));
                changed= currentConfigData != newConfigData;
                if (changed)
                {
                    File.WriteAllText(appConfigFilePath, newConfigData);
                }
            }
            return changed;
        }
        public List<AppItem> GetAppConfig()
        {
            if (File.Exists(appConfigFilePath))
            {
                return JsonConvert.DeserializeObject<List<AppItem>>(File.ReadAllText(appConfigFilePath));
            }
            else
            {
                return new List<AppItem>();
            }
        }
        public EnumAppVersionState GetAppVersionState(string appCode)
        {
            if (File.Exists(hybridVersionFilePath))
            {
                List<HybridAppVersion> hybridAppsVersion= JsonConvert.DeserializeObject<List<HybridAppVersion>>(File.ReadAllText(hybridVersionFilePath));
                var appVersion = hybridAppsVersion.Where(p => string.Compare(p.AppCode, appCode, true) == 0).FirstOrDefault()?.Version;
                if (appVersion == null)
                {
                    return EnumAppVersionState.待下载;
                }
                else
                {
                   var appConfig= GetAppConfig().Where(p => string.Compare(p.AppCode, appCode, true) == 0).FirstOrDefault();
                   if (appConfig != null)
                   {
                        if (appConfig.HybridAppVersion > appVersion)
                        {
                            return EnumAppVersionState.待升级;
                        }
                   }
                }
            }
            else
            {
                return EnumAppVersionState.待下载;
            }
            return EnumAppVersionState.最新;
        }
        public void UpdateAppVersion(HybridAppVersion newAppVersion)
        {
            List<HybridAppVersion> hybridAppsVersion = new List<HybridAppVersion>();
            if (File.Exists(hybridVersionFilePath))
            {
                hybridAppsVersion = JsonConvert.DeserializeObject<List<HybridAppVersion>>(File.ReadAllText(hybridVersionFilePath));
            }
            hybridAppsVersion.RemoveAll(p => string.Compare(p.AppCode, newAppVersion.AppCode, true) == 0);
            hybridAppsVersion.Add(newAppVersion);
            File.WriteAllText(hybridVersionFilePath, JsonConvert.SerializeObject(hybridAppsVersion));
        }
        public string GetHybridStartUrl(string appCode,string startPage)
        {
            string formatStartPage = string.IsNullOrWhiteSpace(startPage) ? "index.html" : startPage.Trim().Trim(new char[] { '/', '\\' });
            return $"https://{MobileCommonConsts.HybridRootDomain}.{appCode.Trim().ToLower()}/{formatStartPage}";
        }
        public string GetHybridAppBasePath()
        {
            return hybridAppBaseDir;
        }
        public string GetHybridAppPhysicalPath(string appCode)
        {
            return Path.Combine(hybridAppBaseDir, appCode.Trim().ToLower());
        }
    }
}
