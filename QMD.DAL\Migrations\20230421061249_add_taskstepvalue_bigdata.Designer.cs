﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using QMD.DAL;

namespace QMD.DAL.Migrations
{
    [DbContext(typeof(QmdDbContext))]
    [Migration("20230421061249_add_taskstepvalue_bigdata")]
    partial class add_taskstepvalue_bigdata
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("Relational:MaxIdentifierLength", 64)
                .HasAnnotation("ProductVersion", "5.0.17");

            modelBuilder.Entity("QMD.DAL.Table.ActionRecord", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Action")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Desc")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Operator")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("ProjectID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Property1")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Property2")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Property3")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Property4")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Property5")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Property6")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("Property7")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Property8")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("TriggerDay")
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)");

                    b.HasKey("ID");

                    b.ToTable("ActionRecords");
                });

            modelBuilder.Entity("QMD.DAL.Table.AppSolution", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Code")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("FirstDisName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("FirstEnName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("FirstNode")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("ProjectType")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)");

                    b.Property<string>("SecondDisName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("SecondEnName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("SecondNode")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ThirdDisName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ThirdEnName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ThirdNode")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.HasKey("ID");

                    b.ToTable("AppSolutions");
                });

            modelBuilder.Entity("QMD.DAL.Table.Approval", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int>("ApprovalStatus")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("GroupID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSerious")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("Level")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Remark")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)");

                    b.Property<string>("StepID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int>("TargetType")
                        .HasColumnType("int");

                    b.Property<string>("TaskItemID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TplID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TriggerDay")
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)");

                    b.Property<string>("ValueID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.HasKey("ID");

                    b.HasIndex("TaskItemID", "StepID", "Level", "TargetType", "ValueID")
                        .IsUnique();

                    b.ToTable("Approvals");
                });

            modelBuilder.Entity("QMD.DAL.Table.Approver", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int>("AllowLevel")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ProjectID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("UserEmail")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("UserName")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.HasKey("ID");

                    b.HasIndex("UserEmail", "AllowLevel", "ProjectID")
                        .IsUnique();

                    b.ToTable("Approvers");
                });

            modelBuilder.Entity("QMD.DAL.Table.Contractor", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Code")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("DisplayName")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ProjectID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Source")
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)");

                    b.HasKey("ID");

                    b.ToTable("Contractors");
                });

            modelBuilder.Entity("QMD.DAL.Table.CycleTaskRecord", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastUploadDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("Level1NotPassedFileCount")
                        .HasColumnType("int");

                    b.Property<int>("Level1NotPassedStepCount")
                        .HasColumnType("int");

                    b.Property<int>("Level1PassedFileCount")
                        .HasColumnType("int");

                    b.Property<int>("Level1PassedStepCount")
                        .HasColumnType("int");

                    b.Property<int>("Level2NotPassedFileCount")
                        .HasColumnType("int");

                    b.Property<int>("Level2NotPassedStepCount")
                        .HasColumnType("int");

                    b.Property<int>("Level2PassedFileCount")
                        .HasColumnType("int");

                    b.Property<int>("Level2PassedStepCount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("SubmitStatus")
                        .HasColumnType("int");

                    b.Property<string>("TaskItemID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int>("TotalFileCount")
                        .HasColumnType("int");

                    b.Property<int>("TotalRequiredStepCount")
                        .HasColumnType("int");

                    b.Property<string>("TplID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TriggerDay")
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)");

                    b.Property<int>("UploadedRequiredStepCount")
                        .HasColumnType("int");

                    b.Property<int>("WaitLevel1ApproveFileCount")
                        .HasColumnType("int");

                    b.Property<int>("WaitLevel1ApproveStepCount")
                        .HasColumnType("int");

                    b.Property<int>("WaitLevel2ApproveFileCount")
                        .HasColumnType("int");

                    b.Property<int>("WaitLevel2ApproveStepCount")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.HasIndex("TplID", "TaskItemID", "TriggerDay")
                        .IsUnique();

                    b.ToTable("CycleTaskRecords");
                });

            modelBuilder.Entity("QMD.DAL.Table.ItrWorkOrder", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("CodeSk")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("FirstSolution")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<double>("LeftTime")
                        .HasColumnType("double");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NetName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("NetType")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("OrderLevel")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("OrderNo")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("OrderStatus")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("OrderType")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("ProductLine")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ProductModel")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ProgramProvider")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ProviderEmail")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ProviderPhone")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<DateTime?>("SLATime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("SecondSolution")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<DateTime?>("SolutionTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("UploadEmail")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("UploadPhone")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<DateTime>("UploadTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("UploadUser")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.HasKey("ID");

                    b.HasIndex("OrderNo")
                        .IsUnique();

                    b.ToTable("ItrWorkOrders");
                });

            modelBuilder.Entity("QMD.DAL.Table.Lang", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("CultureCode")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Title")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("UsedFor")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.HasKey("ID");

                    b.HasIndex("Key", "CultureCode")
                        .IsUnique();

                    b.ToTable("Langs");
                });

            modelBuilder.Entity("QMD.DAL.Table.NetUserPosition", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("DepName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NickName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("UserEmail")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("UserPhone")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<int>("UserPosition")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.ToTable("NetUserPositions");
                });

            modelBuilder.Entity("QMD.DAL.Table.Netprovider", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("ChargePersonEmail")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ChargePersonName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ChargePersonNo")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ChargePersonPhone")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("City")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("CodeSk")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Country")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("DepName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsQaNet")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NetName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("NetProperties")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("Operator")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ProductSpe")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Province")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Region")
                        .HasMaxLength(10)
                        .HasColumnType("varchar(10)");

                    b.HasKey("ID");

                    b.HasIndex("CodeSk")
                        .IsUnique();

                    b.ToTable("Netproviders");
                });

            modelBuilder.Entity("QMD.DAL.Table.ObjTypeLevel", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("LevelColor")
                        .HasMaxLength(10)
                        .HasColumnType("varchar(10)");

                    b.Property<string>("LevelName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<int>("LevelType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("ObjTypeLevels");
                });

            modelBuilder.Entity("QMD.DAL.Table.ObjTypeLevelCfg", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int>("CfgType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ObjTypeLevelId")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("ObjTypeName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.HasKey("ID");

                    b.ToTable("ObjTypeLevelCfgs");
                });

            modelBuilder.Entity("QMD.DAL.Table.Project", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Code")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsPrivate")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("ProjectStatus")
                        .HasColumnType("int");

                    b.Property<int>("ProjectType")
                        .HasColumnType("int");

                    b.Property<string>("RegionCode")
                        .IsRequired()
                        .HasMaxLength(12)
                        .HasColumnType("varchar(12)");

                    b.Property<string>("Remark")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)");

                    b.Property<string>("ServiceLineID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("StartedTime")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("StopAutoImportWosContract")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("TaskType")
                        .HasColumnType("int");

                    b.Property<string>("WOSProjNames")
                        .HasColumnType("text");

                    b.Property<string>("Year")
                        .HasMaxLength(4)
                        .HasColumnType("varchar(4)");

                    b.HasKey("ID");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("DisplayName")
                        .IsUnique();

                    b.ToTable("Projects");
                });

            modelBuilder.Entity("QMD.DAL.Table.Region", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(12)
                        .HasColumnType("varchar(12)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int?>("TimeZoneOffset")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("Regions");
                });

            modelBuilder.Entity("QMD.DAL.Table.RelAdminUser", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ProjectID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("UserEmail")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("UserName")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.HasKey("ID");

                    b.ToTable("RelAdminUser");
                });

            modelBuilder.Entity("QMD.DAL.Table.RelAppSolutionAndTpl", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("AppSolutionID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("TplID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.HasKey("ID");

                    b.ToTable("AppSolutionAndTplRels");
                });

            modelBuilder.Entity("QMD.DAL.Table.RelContractorTaskItemTpl", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("ContractorID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("TaskItemID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TplID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.HasKey("ID");

                    b.ToTable("ContractorTaskItemTplRels");
                });

            modelBuilder.Entity("QMD.DAL.Table.RelContractorUser", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("ContractorID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("UserEmail")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("UserName")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.HasKey("ID");

                    b.ToTable("ContractorUserRels");
                });

            modelBuilder.Entity("QMD.DAL.Table.RelProjectUser", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ProjectID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("UserEmail")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("UserName")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.HasKey("ID");

                    b.HasIndex("ProjectID", "UserEmail")
                        .IsUnique();

                    b.ToTable("RelProjectUser");
                });

            modelBuilder.Entity("QMD.DAL.Table.RelTaskItemAndTpl", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastUploadDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Level1ApproverID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int>("Level1NotPassedFileCount")
                        .HasColumnType("int");

                    b.Property<int>("Level1NotPassedStepCount")
                        .HasColumnType("int");

                    b.Property<int>("Level1PassedFileCount")
                        .HasColumnType("int");

                    b.Property<int>("Level1PassedStepCount")
                        .HasColumnType("int");

                    b.Property<string>("Level2ApproverID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int>("Level2NotPassedFileCount")
                        .HasColumnType("int");

                    b.Property<int>("Level2NotPassedStepCount")
                        .HasColumnType("int");

                    b.Property<int>("Level2PassedFileCount")
                        .HasColumnType("int");

                    b.Property<int>("Level2PassedStepCount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("SubmitStatus")
                        .HasColumnType("int");

                    b.Property<string>("TaskItemID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int>("TotalFileCount")
                        .HasColumnType("int");

                    b.Property<int>("TotalRequiredStepCount")
                        .HasColumnType("int");

                    b.Property<string>("TplID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int>("UploadedRequiredStepCount")
                        .HasColumnType("int");

                    b.Property<int>("WaitLevel1ApproveFileCount")
                        .HasColumnType("int");

                    b.Property<int>("WaitLevel1ApproveStepCount")
                        .HasColumnType("int");

                    b.Property<int>("WaitLevel2ApproveFileCount")
                        .HasColumnType("int");

                    b.Property<int>("WaitLevel2ApproveStepCount")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.HasIndex("TplID", "TaskItemID")
                        .IsUnique();

                    b.ToTable("TaskItemAndTplRels");
                });

            modelBuilder.Entity("QMD.DAL.Table.ServiceLine", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.HasKey("ID");

                    b.ToTable("ServiceLines");
                });

            modelBuilder.Entity("QMD.DAL.Table.TaskGroupTpl", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsHide")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsTemporary")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<string>("TplID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.HasKey("ID");

                    b.ToTable("TaskGroupTpls");
                });

            modelBuilder.Entity("QMD.DAL.Table.TaskItem", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Addr1")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Addr2")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Addr3")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Addr4")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("AuthCode")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<bool>("BelongLevel2Sampling")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("BigDataId")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Contractor")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<bool>("HasLevel2Sampling")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("InspectTime")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("KeyPropertyChangedTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("LastEmailRemindTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("LastUploadDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<float?>("Lat")
                        .HasColumnType("float");

                    b.Property<string>("Level1CustomerProgStepID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Level2CustomerProgStepID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<float?>("Lng")
                        .HasColumnType("float");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NmpId")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ProjectID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Region")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Remark")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ServiceLineID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Source")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("SubRegion")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<int>("TaskItemStatus")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.HasIndex("Code", "ProjectID")
                        .IsUnique();

                    b.ToTable("TaskItems");
                });

            modelBuilder.Entity("QMD.DAL.Table.TaskItemDimValue", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("DimDepth")
                        .HasColumnType("int");

                    b.Property<string>("DimTag")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Level1")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Level2")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Level3")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Value1")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Value2")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Value3")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.HasKey("ID");

                    b.ToTable("TaskItemDimValues");
                });

            modelBuilder.Entity("QMD.DAL.Table.TaskStepTpl", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<bool>("AllowMany")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("AllowSelect")
                        .HasColumnType("tinyint(1)");

                    b.Property<int?>("CountMaxLimit")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("DataType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DateTimeMaxLimit")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("DateTimeMinLimit")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("GroupID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int?>("HybridModeOrder")
                        .HasColumnType("int");

                    b.Property<string>("HybridModeParentID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int?>("IntegerMaxLimit")
                        .HasColumnType("int");

                    b.Property<int?>("IntegerMinLimit")
                        .HasColumnType("int");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsHide")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsRequired")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsTemporary")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)");

                    b.Property<int>("SupportFileTypes")
                        .HasColumnType("int");

                    b.Property<int?>("TextMaxLimit")
                        .HasColumnType("int");

                    b.Property<int?>("TextMinLimit")
                        .HasColumnType("int");

                    b.Property<string>("TplID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.HasKey("ID");

                    b.ToTable("TaskStepTpls");
                });

            modelBuilder.Entity("QMD.DAL.Table.TaskStepValue", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("BigDataId")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<string>("ContractorID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("DataType")
                        .HasColumnType("int");

                    b.Property<string>("ExtValue1")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ExtValue2")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ExtValue3")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ExtValue4")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ExtValue5")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ExtValue6")
                        .HasColumnType("longtext");

                    b.Property<int?>("FileVersion")
                        .HasColumnType("int");

                    b.Property<bool>("ForceRectify")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("HybridOrder")
                        .HasColumnType("int");

                    b.Property<DateTime?>("InspectTime")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Lat")
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)");

                    b.Property<string>("Lng")
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)");

                    b.Property<string>("MD5")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<string>("ProjectID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("RegionCode")
                        .IsRequired()
                        .HasMaxLength(12)
                        .HasColumnType("varchar(12)");

                    b.Property<string>("Remark")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)");

                    b.Property<string>("ServiceLineID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int>("SupportFileTypes")
                        .HasColumnType("int");

                    b.Property<string>("TaskItemID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TaskStepID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TaskTplID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TriggerDay")
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)");

                    b.Property<string>("Value")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.HasKey("ID");

                    b.ToTable("TaskStepValues");
                });

            modelBuilder.Entity("QMD.DAL.Table.TaskStepValueSample", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("DataType")
                        .HasColumnType("int");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("MD5")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)");

                    b.Property<string>("TaskStepID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TaskTplID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Value")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.HasKey("ID");

                    b.ToTable("TaskStepSamples");
                });

            modelBuilder.Entity("QMD.DAL.Table.TaskTpl", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("CycleType")
                        .HasColumnType("int");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<bool>("GroupPublished")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Milestone")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("NoRequiredStep")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ObjTypeLevelId")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("ProjectID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<bool>("Published")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("RegionCode")
                        .IsRequired()
                        .HasMaxLength(12)
                        .HasColumnType("varchar(12)");

                    b.Property<int>("RequiredStepCount")
                        .HasColumnType("int");

                    b.Property<string>("ServiceLineID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<bool>("StepPublished")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("TriggerDay")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("TriggerTimes")
                        .HasColumnType("int");

                    b.Property<int>("UnrequiredStepCount")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.HasIndex("DisplayName")
                        .IsUnique();

                    b.ToTable("TaskTpls");
                });

            modelBuilder.Entity("QMD.DAL.Table.WosContractor", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("ImportedToQmd")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Mail")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Phone")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("SubconCode")
                        .IsRequired()
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)");

                    b.Property<string>("SubconName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.HasKey("ID");

                    b.HasIndex("SubconCode")
                        .IsUnique();

                    b.ToTable("WosContractors");
                });

            modelBuilder.Entity("QMD.DAL.Table.WosRelSiteAndContractor", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("ImportedToQmd")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("SubconCode")
                        .IsRequired()
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)");

                    b.Property<string>("WbsCode")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.HasKey("ID");

                    b.HasIndex("SubconCode", "WbsCode")
                        .IsUnique();

                    b.ToTable("WosRelSiteAndContractors");
                });

            modelBuilder.Entity("QMD.DAL.Table.WosSiteInfo", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("City")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("District")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<bool>("ImportedToQmd")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ProjectName")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ProjectTeam")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Province")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Region")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("SiteId")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("SiteName")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("SubProjectName")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Village")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("WbsCode")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.HasKey("ID");

                    b.HasIndex("WbsCode")
                        .IsUnique();

                    b.ToTable("WosSiteInfos");
                });
#pragma warning restore 612, 618
        }
    }
}
