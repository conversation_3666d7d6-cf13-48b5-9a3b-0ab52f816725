﻿using Common.DAL.Methods;
using Common.Model;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Hub.Web
{
    public class GlobalExceptionHandlerMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger _logger;
        public GlobalExceptionHandlerMiddleware(RequestDelegate next, ILogger<GlobalExceptionHandlerMiddleware> logger)
        {
            this._next = next;
            this._logger = logger;
        }

        public async Task<Task> Invoke(HttpContext context)
        {
            try
            {
                _logger.LogInformation($"Request:{context.Request.Path}");
                await this._next(context);
            }
            catch (Exception ex)
            {
                if (ex is not MessageException)
                {
                    _logger.LogError(ex, ex.Message);
                }
                var msg = ex.Message ?? "发生未知错误";
                if (ex.InnerException != null && !string.IsNullOrWhiteSpace(ex.InnerException.Message))
                {
                    msg = ex.InnerException.Message;
                }

                context.Response.ContentType = "application/json;charset=utf-8";
                await context.Response.WriteAsync(Newtonsoft.Json.JsonConvert.SerializeObject(
                  new BaseRes { Flag = false, Message = msg }
                , new JsonSerializerSettings { ContractResolver = new CamelCasePropertyNamesContractResolver() }
                ));
            }
            return Task.CompletedTask;
        }
    }
}
