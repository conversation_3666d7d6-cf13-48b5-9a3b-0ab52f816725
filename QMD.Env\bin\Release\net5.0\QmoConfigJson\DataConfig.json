{"NoticeTaskTodo": [{"DepName": "湖北技术服务中心", "ShowColumns": [{"ColumnName": "operator", "PropertyName": "Operator", "DisplayName": "运营商"}, {"ColumnName": "riskLevel", "PropertyName": "RiskLevel", "DisplayName": "风险级别"}, {"ColumnName": "city", "PropertyName": "City", "DisplayName": "地市"}, {"ColumnName": "taskTitle", "PropertyName": "TaskTitle", "DisplayName": "工单主题"}, {"ColumnName": "chargeUserName", "PropertyName": "ChargeUserName", "DisplayName": "网络负责人"}, {"ColumnName": "taskStatusStr", "PropertyName": "TaskStatusStr", "DisplayName": "工单状态"}]}], "NoticeTaskTodoDefault": [{"ColumnName": "region", "PropertyName": "Region", "DisplayName": "区域"}, {"ColumnName": "province", "PropertyName": "Province", "DisplayName": "省份"}, {"ColumnName": "operator", "PropertyName": "Operator", "DisplayName": "运营商"}, {"ColumnName": "city", "PropertyName": "City", "DisplayName": "地市"}, {"ColumnName": "netName", "PropertyName": "NetName", "DisplayName": "网络名称"}, {"ColumnName": "chargeUserName", "PropertyName": "ChargeUserName", "DisplayName": "网络负责人"}, {"ColumnName": "chargeUserMail", "PropertyName": "ChargeUserMail", "DisplayName": "负责人邮箱"}, {"ColumnName": "taskNo", "PropertyName": "TaskNo", "DisplayName": "工单编号"}, {"ColumnName": "taskTitle", "PropertyName": "TaskTitle", "DisplayName": "工单主题"}, {"ColumnName": "oprTypeStr", "PropertyName": "OprTypeStr", "DisplayName": "工单类型"}, {"ColumnName": "riskLevel", "PropertyName": "RiskLevel", "DisplayName": "风险级别"}, {"ColumnName": "utcStartTime", "PropertyName": "UtcStartTime", "DisplayName": "计划开始时间"}, {"ColumnName": "utcEndTime", "PropertyName": "UtcEndTime", "DisplayName": "计划结束时间"}, {"ColumnName": "taskStatusStr", "PropertyName": "TaskStatusStr", "DisplayName": "工单状态"}, {"ColumnName": "taskUserStr", "PropertyName": "TaskUserStr", "DisplayName": "实施人"}, {"ColumnName": "checkUserStr", "PropertyName": "CheckUserStr", "DisplayName": "核查人"}, {"ColumnName": "fstLineUserStr", "PropertyName": "FstLineUserStr", "DisplayName": "一线保障人"}, {"ColumnName": "secLineUserStr", "PropertyName": "SecLineUserStr", "DisplayName": "二线保障人"}, {"ColumnName": "thdLineUserStr", "PropertyName": "ThdLineUserStr", "DisplayName": "三线保障人"}, {"ColumnName": "fouLineUserStr", "PropertyName": "FouLineUserStr", "DisplayName": "四线保障人"}], "NoticeTaskResult": [{"DepName": "湖北技术服务中心", "ShowColumns": [{"ColumnName": "operator", "PropertyName": "Operator", "DisplayName": "运营商"}, {"ColumnName": "riskLevel", "PropertyName": "RiskLevel", "DisplayName": "风险级别"}, {"ColumnName": "city", "PropertyName": "City", "DisplayName": "地市"}, {"ColumnName": "taskTitle", "PropertyName": "TaskTitle", "DisplayName": "工单主题"}, {"ColumnName": "chargeUserName", "PropertyName": "ChargeUserName", "DisplayName": "网络负责人"}, {"ColumnName": "taskStatusStr", "PropertyName": "TaskStatusStr", "DisplayName": "工单状态"}]}], "NoticeTaskResultDefault": [{"ColumnName": "region", "PropertyName": "Region", "DisplayName": "区域"}, {"ColumnName": "province", "PropertyName": "Province", "DisplayName": "省份"}, {"ColumnName": "operator", "PropertyName": "Operator", "DisplayName": "运营商"}, {"ColumnName": "city", "PropertyName": "City", "DisplayName": "地市"}, {"ColumnName": "netName", "PropertyName": "NetName", "DisplayName": "网络名称"}, {"ColumnName": "chargeUserName", "PropertyName": "ChargeUserName", "DisplayName": "网络负责人"}, {"ColumnName": "chargeUserMail", "PropertyName": "ChargeUserMail", "DisplayName": "负责人邮箱"}, {"ColumnName": "taskNo", "PropertyName": "TaskNo", "DisplayName": "工单编号"}, {"ColumnName": "taskTitle", "PropertyName": "TaskTitle", "DisplayName": "工单主题"}, {"ColumnName": "oprTypeStr", "PropertyName": "OprTypeStr", "DisplayName": "工单类型"}, {"ColumnName": "riskLevel", "PropertyName": "RiskLevel", "DisplayName": "风险级别"}, {"ColumnName": "utcStartTime", "PropertyName": "UtcStartTime", "DisplayName": "实际开始时间"}, {"ColumnName": "utcEndTime", "PropertyName": "UtcEndTime", "DisplayName": "实际结束时间"}, {"ColumnName": "taskStatusStr", "PropertyName": "TaskStatusStr", "DisplayName": "工单状态"}, {"ColumnName": "taskRemark", "PropertyName": "TaskRemark", "DisplayName": "对应遗留问题或原因"}, {"ColumnName": "taskUserStr", "PropertyName": "TaskUserStr", "DisplayName": "实施人"}, {"ColumnName": "checkUserStr", "PropertyName": "CheckUserStr", "DisplayName": "核查人"}, {"ColumnName": "fstLineUserStr", "PropertyName": "FstLineUserStr", "DisplayName": "一线保障人"}, {"ColumnName": "secLineUserStr", "PropertyName": "SecLineUserStr", "DisplayName": "二线保障人"}, {"ColumnName": "thdLineUserStr", "PropertyName": "ThdLineUserStr", "DisplayName": "三线保障人"}, {"ColumnName": "fouLineUserStr", "PropertyName": "FouLineUserStr", "DisplayName": "四线保障人"}]}