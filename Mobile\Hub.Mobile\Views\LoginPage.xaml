﻿<?xml version="1.0" encoding="utf-8" ?>
<pages:PopupPage xmlns="http://xamarin.com/schemas/2014/forms"
                 xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
                 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                 xmlns:d="http://xamarin.com/schemas/2014/forms/design"
                 xmlns:animations="clr-namespace:Rg.Plugins.Popup.Animations;assembly=Rg.Plugins.Popup"
                 xmlns:pages="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup"
                 xmlns:suggestbox="clr-namespace:dotMorten.Xamarin.Forms;assembly=dotMorten.Xamarin.Forms.AutoSuggestBox"
                 mc:Ignorable="d"
             x:Class="Hub.Mobile.Views.LoginPage" CloseWhenBackgroundIsClicked="False">
    <pages:PopupPage.Animation>
        <animations:ScaleAnimation DurationIn="400"
                                   DurationOut="300"
                                   EasingIn="SinOut"
                                   EasingOut="SinIn"
                                   HasBackgroundAnimation="True"
                                   PositionIn="Center"
                                   PositionOut="Center"
                                   ScaleIn="1.2"
                                   ScaleOut="0.8" />
    </pages:PopupPage.Animation>
    <Grid VerticalOptions="Center" HorizontalOptions="Center">
        <AbsoluteLayout>
            <Frame  BackgroundColor="White" Padding="15">
                <StackLayout Spacing="20"  HorizontalOptions="Center" VerticalOptions="Center" WidthRequest="280">
                    <!--<Image Source="person.png" HeightRequest="50" VerticalOptions="End"/>-->
                    <!--<Entry x:Name="entryEmail" Text="{Binding Email}" Placeholder="邮箱"
                       PlaceholderColor="#bababa" FontSize="16"/>-->
                    <suggestbox:AutoSuggestBox PlaceholderText="{Binding PlaceHolderText}"  PlaceholderTextColor="#bababa"  TextColor="Black"  Focused="EmailSuggestBox_Focused"
                TextChanged="AutoSuggestBox_TextChanged"
                QuerySubmitted="AutoSuggestBox_QuerySubmitted"
                SuggestionChosen="AutoSuggestBox_SuggestionChosen" />
                    <StackLayout IsVisible="{Binding PasswordLogin,Converter={StaticResource InverseBoolConverter}}">
                        <StackLayout Orientation="Horizontal">
                            <Entry  Text="{Binding ImageCode}" Placeholder="{DynamicResource ImageVerificationCode}" WidthRequest="150"
                       PlaceholderColor="#bababa" TextColor="Black" />
                            <ImageButton HorizontalOptions="FillAndExpand" Source="{Binding ImageCodeUrl}" Aspect="Fill" Command="{Binding ImageCodeCommand}">
                                <!--<ImageButton.Source>
                            <UriImageSource Uri="{Binding ImageCodeUrl}"></UriImageSource>
                        </ImageButton.Source>-->
                            </ImageButton>
                        </StackLayout>
                        <StackLayout Orientation="Horizontal">
                            <Entry Text="{Binding EmailCode}" Placeholder="{DynamicResource EmailVerificationCode}"  WidthRequest="150" 
                       PlaceholderColor="#bababa" TextColor="Black"/>
                            <Button Text="{Binding BtnEmailCodeText,Mode=TwoWay}" TextColor="White" HorizontalOptions="FillAndExpand" IsEnabled="{Binding BtnEmailCodeEnabled}" Command="{Binding EmailCodeCommand}"></Button>
                        </StackLayout>
                    </StackLayout>
                    <StackLayout IsVisible="{Binding PasswordLogin}">
                        <Entry IsPassword="True" Text="{Binding Password}"  Placeholder="{DynamicResource Password}" PlaceholderColor="#bababa" TextColor="Black"></Entry>
                    </StackLayout>
                    <!--<Button Margin="0,10,0,0" Text="登录" BackgroundColor="{StaticResource Primary}" 
                        TextColor="White" HeightRequest="50" VerticalOptions="Start"
                        Command="{Binding LoginCommand}"/>-->
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                            <ColumnDefinition Width="40"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <Button Grid.Column="0" Text="{DynamicResource Login}"  Command="{Binding LoginCommand}" IsEnabled="{Binding BtnLoginEnabled}" TextColor="White"></Button>
                        <Image Grid.Column="1" Source="logout.xml">
                            <Image.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding SwitchLoginCommand}"></TapGestureRecognizer>
                            </Image.GestureRecognizers>
                        </Image>
                    </Grid>
                    <Label Text="{Binding ValidateMsg}" TextColor="Red" HorizontalOptions="Center"/>
                </StackLayout>
            </Frame>
            <ImageButton Command="{Binding CloseCommand}" BackgroundColor="Transparent"
          Source="icon_close.png"
          HeightRequest="30"
                  AbsoluteLayout.LayoutFlags="PositionProportional"
          WidthRequest="30" AbsoluteLayout.LayoutBounds="1, 0, -1, -1"/>
        </AbsoluteLayout>
    </Grid>
</pages:PopupPage>