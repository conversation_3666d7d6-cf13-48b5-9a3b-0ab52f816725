{"runtimeTarget": {"name": ".NETStandard,Version=v2.0/", "signature": ""}, "compilationOptions": {}, "targets": {".NETStandard,Version=v2.0": {}, ".NETStandard,Version=v2.0/": {"Hub.Mobile.Utility/1.0.0": {"dependencies": {"NETStandard.Library": "2.0.3", "NLog": "4.7.9", "Plugin.Permissions": "6.0.1", "Xam.Plugin.Geolocator": "4.5.0.6"}, "runtime": {"Hub.Mobile.Utility.dll": {}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "NETStandard.Library/2.0.3": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0"}}, "NLog/4.7.9": {"runtime": {"lib/netstandard2.0/NLog.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.7.9.12899"}}}, "Plugin.Permissions/6.0.1": {"dependencies": {"Xamarin.Essentials": "1.5.2"}, "runtime": {"lib/netstandard2.0/Plugin.Permissions.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "System.Numerics.Vectors/4.5.0": {"runtime": {"lib/netstandard2.0/System.Numerics.Vectors.dll": {"assemblyVersion": "4.1.4.0", "fileVersion": "4.6.26515.6"}}}, "Xam.Plugin.Geolocator/4.5.0.6": {"runtime": {"lib/netstandard2.0/Plugin.Geolocator.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Xamarin.Essentials/1.5.2": {"dependencies": {"System.Numerics.Vectors": "4.5.0"}, "runtime": {"lib/netstandard2.0/Xamarin.Essentials.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"Hub.Mobile.Utility/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "NETStandard.Library/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-st47PosZSHrjECdjeIzZQbzivYBJFv6P2nv4cj2ypdI204DO+vZ7l5raGMiX4eXMJ53RfOIg+/s4DHVZ54Nu2A==", "path": "netstandard.library/2.0.3", "hashPath": "netstandard.library.2.0.3.nupkg.sha512"}, "NLog/4.7.9": {"type": "package", "serviceable": true, "sha512": "sha512-Y0dKY5d506ZVcb8dTMp3BSb+jUGaFu/ArvRkgn4daDWKf7qqFXjEFYjqoyTrWkcNguqS6avkwICS0hsFsr4BzA==", "path": "nlog/4.7.9", "hashPath": "nlog.4.7.9.nupkg.sha512"}, "Plugin.Permissions/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-na/0FNhyKmSicANWZMLSM0y/FW2AvLoawX3lsvBpELwP15sdJRyuBhRKk1OQjRoNrmTIewcON1tJFyydjMGWcA==", "path": "plugin.permissions/6.0.1", "hashPath": "plugin.permissions.6.0.1.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "Xam.Plugin.Geolocator/4.5.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Ro7peBDxsDXPc7J7yLx0ekAcXIfVTAXj/rXiBKiTgZ4Lm7KZJIU8PXOCcI4JztzT3MQrL8Rmka48il/ix0pwoA==", "path": "xam.plugin.geolocator/4.5.0.6", "hashPath": "xam.plugin.geolocator.4.5.0.6.nupkg.sha512"}, "Xamarin.Essentials/1.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-qHxkrkx5jIC0X/qWhxCPZ3MuuOJm8bfCzAoUtlpOBSE4oV0JhrkTezK0A+IV+k/MeJ3zKc8FexaIXah9rlCTIw==", "path": "xamarin.essentials/1.5.2", "hashPath": "xamarin.essentials.1.5.2.nupkg.sha512"}}}