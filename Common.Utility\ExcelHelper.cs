﻿using Microsoft.AspNetCore.Mvc.Razor.Internal;
using Npoi.Mapper;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.XSSF.UserModel;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing.Imaging;
using System.Drawing;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using static Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary;
using Spire.Xls;
using System.Data;
using NPOI.HSSF.Util;
using OfficeOpenXml;

namespace Common.Utility
{
    public class ExcelHelper
    {
        /// <summary>
        /// 导出excel
        /// </summary>
        /// <param name="entitys">动态对象列表</param>
        /// <param name="title">列名</param>
        /// <param name="style">样式(列)</param>
        /// <param name="horizontal">水平对齐方式</param>
        /// <returns></returns>
        public static byte[] OutputExcel(List<ExpandoObject> entitys, string[] title, int style = 1, HorizontalAlignment horizontal = HorizontalAlignment.Left)
        {
            IWorkbook workbook = new XSSFWorkbook();
            ISheet sheet = workbook.CreateSheet("sheet");
            IRow Title = null;
            IRow rows = null;
            IDictionary<string, object> entityTmp = entitys[0];
            List<string> keyList = new();
            foreach (var item in entityTmp)
            {
                keyList.Add(item.Key);
            }
            //通用的表头样式
            ICellStyle headStyle = workbook.CreateCellStyle();//创建样式对象
            IFont headFont = workbook.CreateFont(); //创建一个字体样式对象
            headFont.IsBold = true;
            headFont.Color = IndexedColors.White.Index;//字体颜色
            headFont.FontHeightInPoints = 14;//字体大小
            headStyle.SetFont(headFont); //将字体样式赋给样式对象
            headStyle.Alignment = HorizontalAlignment.Center;//水平居中
            headStyle.BorderTop = NPOI.SS.UserModel.BorderStyle.Thick;
            headStyle.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thick;
            headStyle.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thick;
            headStyle.BorderRight = NPOI.SS.UserModel.BorderStyle.Thick;
            headStyle.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.Black.Index;//背景底色
            headStyle.FillPattern = FillPattern.SolidForeground;
            //设置正文文字样式
            ICellStyle contentStyle = workbook.CreateCellStyle();//创建样式对象
            IFont contentFont = workbook.CreateFont(); //创建一个字体样式对象
            contentFont.FontHeightInPoints = 10;//字体大小
            contentStyle.SetFont(contentFont); //将字体样式赋给样式对象
            contentStyle.Alignment = horizontal;//水平居中
            contentStyle.VerticalAlignment = VerticalAlignment.Center;//垂直居中
            contentStyle.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;
            contentStyle.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
            contentStyle.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
            contentStyle.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
            //设置列宽
            switch (style)
            {
                case 1://第一种样式为导出清单列表的样式
                    sheet.SetColumnWidth(0, 5 * 256);//第0列宽度为
                    sheet.SetColumnWidth(1, 30 * 256);//第1列宽度为
                    sheet.SetColumnWidth(2, 15 * 256);//第2列宽度为
                    sheet.SetColumnWidth(3, 20 * 256);//第3列宽度为
                    sheet.SetColumnWidth(4, 20 * 256);//第4列宽度为
                    sheet.SetColumnWidth(5, 20 * 256);//第5列宽度为
                    sheet.SetColumnWidth(6, 25 * 256);//第6列宽度为
                    sheet.SetColumnWidth(7, 25 * 256);//第7列宽度为
                    sheet.SetColumnWidth(8, 40 * 256);//第8列宽度为
                    sheet.SetColumnWidth(9, 50 * 256);//第8列宽度为
                    sheet.SetColumnWidth(10, 20 * 256);//第8列宽度为
                    sheet.SetColumnWidth(11, 20 * 256);//第8列宽度为
                    break;
                default:
                    break;
            }

            for (int i = 0; i <= entitys.Count; i++)
            {
                if (i == 0)
                {
                    Title = sheet.CreateRow(0);
                    for (int k = 1; k < title.Length + 1; k++)
                    {
                        Title.CreateCell(0).SetCellValue("No.");
                        Title.CreateCell(k).SetCellValue(title[k - 1]);
                        Title.Cells[0].CellStyle = headStyle;
                        Title.Cells[k].CellStyle = headStyle;
                    }
                    continue;
                }
                else
                {
                    rows = sheet.CreateRow(i);
                    IDictionary<string, object> entity = entitys[i - 1];
                    for (int j = 1; j <= keyList.Count; j++)
                    {
                        object[] entityValues = new object[keyList.Count];
                        string tmpKey = null;

                        if (style == 1) //如果第一种通用导出样式
                        {
                            tmpKey = keyList.Where(t => (title[j - 1].Replace(" ", "").ToUpper()).Contains(t.ToUpper())).FirstOrDefault();
                        }
                        else
                        {
                            tmpKey = keyList.Where(t => title[j - 1] == t).FirstOrDefault();
                        }

                        entityValues[j - 1] = entity[tmpKey];
                        rows.CreateCell(0).SetCellValue(i);
                        string value = entityValues[j - 1] == null ? "" : entityValues[j - 1].ToString();
                        rows.CreateCell(j).SetCellValue(value);

                        rows.Cells[0].CellStyle = contentStyle;
                        rows.Cells[j].CellStyle = contentStyle;
                    }
                }
            }
            byte[] buffer = new byte[1024 * 2];
            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                buffer = ms.ToArray();
                ms.Close();
            }
            return buffer;
        }

        /// <summary>
        ///将对象转成可扩充的对象
        /// </summary>
        public static dynamic ConvertToDynamic(object obj)
        {
            IDictionary<string, object> result = new ExpandoObject();
            foreach (PropertyDescriptor pro in TypeDescriptor.GetProperties(obj.GetType()))
            {
                var protype = pro.PropertyType.Name;
                if (!protype.Contains("List"))
                {
                    result.Add(pro.Name, pro.GetValue(obj));
                }
            }
            return result as ExpandoObject;
        }

        /// <summary>
        /// Excel插入页眉页脚
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="data">需要导出的对象集</param>
        /// <param name="fielPath">导出路径</param>
        /// <param name="picPath">页眉图片路径(NPOI无法操作页眉图片,第一行插入图片模拟)</param>
        /// <param name="header">页眉内容</param>
        /// <param name="footer">页脚内容</param>
        /// <returns></returns>
        public static string ExportExcel<T>(List<T> data, string fielPath, string picPath, string header, string footer)
        {
            var mapper = new Mapper();
            mapper.Put(data, "sheet1", overwrite: true);
            var sheet = mapper.Workbook.GetSheet($"sheet1");

            List<PropertyInfo> propertyInfos = data.First().GetType().GetProperties().ToList();

            if (!string.IsNullOrEmpty(header))
            {
                XSSFOddHeader Header = (XSSFOddHeader)sheet.Header;
                Header.Left = header;
            }

            if (!string.IsNullOrEmpty(footer))
            {
                XSSFOddFooter Footer = (XSSFOddFooter)sheet.Footer;
                Footer.Left = footer;
            }

            if (!string.IsNullOrEmpty(picPath) && File.Exists(picPath))
            {
                IRow newRow = sheet.CreateRow(0); // 这将自动将所有现有行向下移动 
                newRow.HeightInPoints = 30;
                int index = 0;

                propertyInfos.ForEach(prop =>
                {
                    ICell cell1_0 = newRow.CreateCell(index, NPOI.SS.UserModel.CellType.String);
                    index++;
                });

                //默认合并9列（对象属性数量大于8则根据对象属性数量合并）
                int endCol = propertyInfos.Count < 8 ? 8 : propertyInfos.Count;
                //合并第一行(列)
                sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, endCol));

                byte[] imageData = File.ReadAllBytes(picPath);
                // 插入图片到工作表的顶部，模拟页眉
                int pictureIndex = sheet.Workbook.AddPicture(imageData, NPOI.SS.UserModel.PictureType.PNG);
                IDrawing patriarch = sheet.CreateDrawingPatriarch();
                IClientAnchor anchor = patriarch.CreateAnchor(0, 0, 0, 0, 0, 0, 8, 0); // 调整这些值以适应图片位置和大小                                                       
                //var anchor = patriarch.CreateAnchor(0, 0, 0, 0, 0, 0, 8, 8); // 示例坐标，根据需要调整
                IPicture picture = patriarch.CreatePicture(anchor, pictureIndex);
                picture.Resize(1.5, 1.0); // 可能需要根据图片尺寸进行自定义调整
            }

            //表头处理
            int indexRow = 0;
            sheet.ShiftRows(1, sheet.LastRowNum + 1, 1, true, false);//第1行
            IRow newRow3 = sheet.CreateRow(1);//插入第1行
            propertyInfos.ForEach(prop =>
            {
                ICell cell1_1 = newRow3.CreateCell(indexRow, NPOI.SS.UserModel.CellType.String);
                cell1_1.SetCellValue(prop.Name);
                indexRow++;
            });
            mapper.Save(fielPath);
            return fielPath;
        }

        /// <summary>
        /// 指定路径excel插入页眉页脚
        /// </summary>
        /// <param name="filePath"></param>
        /// <param name="headerTxt"></param>
        /// <param name="footerTxt"></param>
        /// <returns></returns>
        public static string AddHeaderFooterToXls(string filePath, string headerTxt, string footerTxt, string picPath)
        {
            IWorkbook workbook = GetWorkbookType(filePath);
            if (workbook is not null)
            {

                ISheet sheet = workbook.GetSheetAt(0);
                if (sheet is not null)
                {
                    sheet.Header.Left = headerTxt; // 简单的页眉设置  
                    sheet.Footer.Left = footerTxt; // 简单的页脚设置  
                }

                //创建行写入logo

                using (FileStream file = new FileStream(filePath, FileMode.Open, FileAccess.Write))
                {
                    workbook.Write(file);
                }
            }
            return filePath;
        }

        private static IWorkbook GetWorkbookType(string filePath)
        {
            IWorkbook workbook;
            using (FileStream file = new FileStream(filePath, FileMode.Open, FileAccess.Read))
            {
                if (filePath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase))
                {
                    workbook = new XSSFWorkbook(file);
                }
                else if (filePath.EndsWith(".xls", StringComparison.OrdinalIgnoreCase))
                {
                    workbook = new HSSFWorkbook(file);
                }
                else
                {
                    throw new ArgumentException("不支持的文件类型，仅支持.xlsx和.xls");
                }
            }
            return workbook;
        }



        /// <summary>
        /// Excel插入页眉页脚及logo0
        /// </summary>
        /// <param name="filePath"></param>
        /// <param name="headTxt"></param>
        /// <param name="footTxt"></param>
        /// <param name="imgPath"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>

        public static string AddHeaderFooterWithLogo(string filePath, string headTxt, string footTxt, string imgPath)
        {
            if (!File.Exists(filePath) || !File.Exists(imgPath))
            {
                throw new Exception("the file or logo is not exist");
            }
            //创建一个Workbook,加载文档
            Workbook wb = new Workbook();
            wb.LoadFromFile(filePath);
            //获取第一个工作表
            Worksheet sheet = wb.Worksheets[0];

            //添加文本到页眉、页脚
            ////设置文本字体、字号、颜色等
            sheet.PageSetup.LeftHeader = headTxt;
            sheet.PageSetup.LeftFooter = footTxt;

            MemoryStream stream = ExcelHelper.ResizeImageAndConvertToFileStream(imgPath, 6, 6, imgPath);

            sheet.PageSetup.CenterHeaderImage = SKBitmap.Decode(stream);
            sheet.PageSetup.CenterHeader = "&G";

            //保存并打开文档
            wb.SaveToFile(filePath, ExcelVersion.Version2013);
            return filePath;
        }

        private static MemoryStream ResizeImageAndConvertToFileStream(string inputPath, int maxWidth, int maxHeight, string outputPath)
        {
            using (Image originalImage = Image.FromFile(inputPath))
            {
                int newWidth = originalImage.Width;
                int newHeight = originalImage.Height;
                if (originalImage.Width > maxWidth || originalImage.Height > maxHeight)
                {

                    newWidth = (int)(originalImage.Width);
                    newHeight = (int)(originalImage.Height);
                }
                MemoryStream memoryStream = new MemoryStream();
                // 创建一个新的Bitmap对象，用于保存缩放后的图片  
                using (Bitmap newImage = new Bitmap(newWidth, newHeight))
                {
                    using (Graphics graphic = Graphics.FromImage(newImage))
                    {
                        // 设置高质量插值法  
                        graphic.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                        graphic.DrawImage(originalImage, 0, 0, newWidth, newHeight);

                        using (FileStream fileStream = new FileStream(outputPath, FileMode.Create))
                        {
                            newImage.Save(fileStream, ImageFormat.Png); // 你可以根据需要更改ImageFormat  

                            fileStream.Position = 0; // 重置位置  
                            fileStream.CopyTo(memoryStream);
                            memoryStream.Position = 0; // 重置位置以便读取  
                            return memoryStream; // 注意：这里返回的是MemoryStream，但你可以根据需要调整  
                        }
                    }
                }
            }

        }


        /// <summary>
        /// Excel导入Datable，返回datatable格式数据。也可以自行转换为二维数据
        /// </summary>
        /// <param name="file">存储excle的路径</param>
        /// <returns></returns>
        public static DataTable ExcelToTable(string file)
        {
            Workbook workbook1 = new Workbook();
            workbook1.LoadFromFile(file);
            var sheet = workbook1.Worksheets[0];
            var rowCount = sheet.LastDataRow;//.Length;
            DataTable dt = new DataTable();
            for (int i = 0; i <= rowCount; i++)
            {
                var tempRow = sheet.Rows[i];
                var cellCount = tempRow.LastColumn;
                for (int j = 0; j <= cellCount; j++)
                {
                    var cellValue = tempRow?.CellList[j]?.Value;
                    //表头，列名
                    if (i == 0)
                    {
                        if (!string.IsNullOrEmpty(cellValue))
                        {
                            dt.Columns.Add(cellValue);
                        }
                    }
                    else
                    {
                        DataRow tempDr = dt.NewRow();
                        tempDr[dt.Columns[j].ColumnName] = cellValue;
                        dt.Rows.Add(tempDr);
                    }
                }
            }
            return dt;
        }

        /// <summary>
        /// Excel导入Datable，返回datatable格式数据。也可以自行转换为二维数据
        /// </summary>
        /// <param name="file">存储excle的路径</param>
        /// <returns></returns>
        public static DataTable ExcelToTable(Stream file)
        {
            Workbook workbook1 = new Workbook();
            workbook1.LoadFromStream(file);
            var sheet = workbook1.Worksheets[0];
            var rowCount = sheet.LastDataRow;//.Length;
            DataTable dt = new DataTable();
            for (int i = 0; i < rowCount; i++)
            {
                var tempRow = sheet.Rows[i];
                var cellCount = tempRow.LastColumn;
                DataRow tempDr = dt.NewRow();
                bool hasValue = false;
                for (int j = 0; j < cellCount; j++)
                {
                    var cellValue = tempRow?.CellList[j]?.Value;
                    //表头，列名
                    if (i == 0)
                    {
                        if (!string.IsNullOrEmpty(cellValue))
                        {
                            dt.Columns.Add(cellValue);
                        }
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(cellValue))
                        {
                            hasValue = true;
                            tempDr[dt.Columns[j].ColumnName] = cellValue;
                        }
                    }
                }
                if (i > 0 && hasValue)
                    dt.Rows.Add(tempDr);
            }
            return dt;
        }

        /// <summary>
        /// Excel导入，返回对象集合格式数据。注意：对象中所有的属性必须为字符串
        /// </summary>
        /// <param name="file">存储excle的路径</param>
        /// <returns></returns>
        public static List<T> ExcelToTable<T>(Stream file) where T : class
        {
            List<T> result = new List<T>();
            var props = ReflectHelper.GetProperties<T>();
            var colunmNames = ReflectHelper.GetColumnName<T>();
            Workbook workbook1 = new Workbook();
            workbook1.LoadFromStream(file);
            var sheet = workbook1.Worksheets[0];
            var rowCount = sheet.LastDataRow;//.Length;
            List<string> headerNames = new List<string>();
            var fstRow = sheet.Rows[0];
            var cellCount = fstRow.LastColumn;
            //获取表头信息
            for (int i = 0; i < cellCount; i++)
            {
                if (!string.IsNullOrEmpty(fstRow?.CellList[i]?.Value))
                {
                    headerNames.Add(fstRow?.CellList[i]?.Value);
                }
            }
            //获取数据信息
            for (int i = 1; i < rowCount; i++)
            {
                bool hasValue = false;
                T newObj = System.Activator.CreateInstance<T>();
                var tempRow = sheet.Rows[i];
                for (int j = 0; j < cellCount; j++)
                {
                    //tempRow?.CellList[j].FormulaStringValue
                    bool hasForm = tempRow?.CellList[j]?.HasFormula ?? false;
                    string cellValue = string.Empty;
                    if (hasForm)
                    {
                        cellValue = tempRow?.CellList[j]?.FormulaValue==null?"" : tempRow?.CellList[j]?.FormulaValue.ToString();
                    }
                    else
                    {
                        cellValue = tempRow?.CellList[j]?.Value;
                    }
                    if (!string.IsNullOrEmpty(cellValue))
                    {
                        int curIndex = -1;
                        for (int k = 0; k < colunmNames.Count(); k++)
                        {
                            if (headerNames[j] == colunmNames[k])
                            {
                                curIndex = k;
                                break;
                            }
                        }
                        if (curIndex == -1)
                        {
                            continue;
                        }
                        props[curIndex].SetValue(newObj, cellValue);
                        hasValue = true;
                    }
                }
                if (i > 0 && hasValue)
                    result.Add(newObj);
            }
            return result;
        }

        /// <summary>
        /// 将数据集合存储到excel中
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="dataList"></param>
        /// <param name="excelPath"></param>
        public static void ListToExcel<T>(List<T> dataList, string excelPath) where T : class
        {
            try
            {
                IWorkbook workbook;
                if (excelPath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase))
                {
                    workbook = new XSSFWorkbook();
                }
                else
                {
                    workbook = new HSSFWorkbook();
                }
                var sheet = workbook.CreateSheet("sheet1");
                //写入表头
                var headerRow = sheet.CreateRow(0);
                List<PropertyInfo> propertyInfos = dataList.First().GetType().GetProperties().ToList();
                int headIndex = 0;
                propertyInfos.ForEach(prop =>
                {
                    ICell cell1_1 = headerRow.CreateCell(headIndex, NPOI.SS.UserModel.CellType.String);
                    cell1_1.SetCellValue(prop.Name);
                    headIndex++;
                });
                //写入数据
                for (int i = 0; i < dataList.Count; i++)
                {
                    var dataRow = sheet.CreateRow(i + 1);
                    for (int j = 0; j < propertyInfos.Count; j++)
                    {
                        var tempVal = propertyInfos[j].GetValue(dataList[i]);
                        dataRow.CreateCell(j).SetCellValue(tempVal == null ? "" : tempVal.ToString());
                    }
                }
                // 自动调整列宽
                for (int i = 0; i <= headerRow.LastCellNum; i++)
                {
                    sheet.AutoSizeColumn(i);
                }
                // 保存文件
                using (var stream = new FileStream(excelPath, FileMode.Create, FileAccess.Write))
                {
                    workbook.Write(stream);
                }

            }
            catch (Exception ex)
            {

            }
        }

        /// <summary>
        /// 将数据集合存储到excel中
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="dataList"></param>
        /// <param name="excelPath"></param>
        public static void ListToExcel(List<Dictionary<string,object>> dataList, string excelPath)
        {
            try
            {
                IWorkbook workbook;
                if (excelPath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase))
                {
                    workbook = new XSSFWorkbook();
                }
                else
                {
                    workbook = new HSSFWorkbook();
                }
                var sheet = workbook.CreateSheet("sheet1");
                //写入表头
                var headerRow = sheet.CreateRow(0);
                if (dataList.Count == 0)
                {
                    //dataList.Add(new Dictionary<string, object> {
                    //    { "暂无数据","暂无数据" }
                    //});
                }
                else
                {
                    List<string> headerColumns = dataList.FirstOrDefault().Keys.ToList();
                    int headIndex = 0;
                    headerColumns.ForEach(prop =>
                    {
                        ICell cell1_1 = headerRow.CreateCell(headIndex, NPOI.SS.UserModel.CellType.String);
                        cell1_1.SetCellValue(prop);
                        headIndex++;
                    });
                    //写入数据
                    for (int i = 0; i < dataList.Count; i++)
                    {
                        var dataRow = sheet.CreateRow(i + 1);
                        for (int j = 0; j < headerColumns.Count; j++)
                        {
                            var tempVal = dataList[i][headerColumns[j]];
                            dataRow.CreateCell(j).SetCellValue(tempVal == null ? "" : tempVal.ToString());
                        }
                    }
                    // 自动调整列宽
                    for (int i = 0; i <= headerRow.LastCellNum; i++)
                    {
                        sheet.AutoSizeColumn(i);
                    }

                    // 保存文件
                    using (var stream = new FileStream(excelPath, FileMode.Create, FileAccess.Write))
                    {
                        workbook.Write(stream);
                    }
                }
                
                //自动设置列宽，使用计算字节长度来设置
                SetAutoSizeColumnByContent(excelPath);
            }
            catch (Exception ex)
            {

            }
        }

        /// <summary>
        /// 将数据集合存储到excel中
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="dataList"></param>
        /// <param name="excelPath"></param>
        public static void ListToExcelSheets(List<List<Dictionary<string, object>>> dataList, string excelPath)
        {
            try
            {
                IWorkbook workbook;
                if (excelPath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase))
                {
                    workbook = new XSSFWorkbook();
                }
                else
                {
                    workbook = new HSSFWorkbook();
                }

                for (int i = 0; i < dataList.Count; i++)
                {
                    var sheet = workbook.CreateSheet($"sheet{i + 1}");
                    //写入表头
                    var headerRow = sheet.CreateRow(0);
                    if (dataList[i].Count == 0)
                    {
                        //dataList.Add(new Dictionary<string, object> {
                        //    { "暂无数据","暂无数据" }
                        //});
                    }
                    else
                    {
                        List<string> headerColumns = dataList[i].FirstOrDefault().Keys.ToList();
                        int headIndex = 0;
                        headerColumns.ForEach(prop =>
                        {
                            ICell cell1_1 = headerRow.CreateCell(headIndex, NPOI.SS.UserModel.CellType.String);
                            cell1_1.SetCellValue(prop);
                            headIndex++;
                        });
                        //写入数据
                        for (int j = 0; j < dataList[i].Count; j++)
                        {
                            var dataRow = sheet.CreateRow(j + 1);
                            for (int k = 0; k < headerColumns.Count; k++)
                            {
                                var tempVal = dataList[i][j][headerColumns[k]];
                                dataRow.CreateCell(k).SetCellValue(tempVal == null ? "" : tempVal.ToString());
                            }
                        }
                        // 自动调整列宽
                        for (int col = 0; col <= headerRow.LastCellNum; col++)
                        {
                            sheet.AutoSizeColumn(col);
                        }
                    }
                }
                // 保存文件
                using (var stream = new FileStream(excelPath, FileMode.Create, FileAccess.Write))
                {
                    workbook.Write(stream);
                }
                //自动设置列宽，使用计算字节长度来设置
                SetAutoSizeColumnByContent(excelPath);
            }
            catch (Exception ex)
            {

            }
        }

        /// <summary>
        /// 该方法在linux下支持度不够，会提示字体问题
        /// </summary>
        /// <param name="excelPath"></param>
        /// <param name="imagePath"></param>
        public static void ExcelToImage(string excelPath, string imagePath)
        {
            // 加载Excel文件
            Workbook workbook = new Workbook();
            workbook.LoadFromFile(excelPath);
            workbook.CustomFontFilePaths = new string[] {
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Lang", "simsun.ttc")
            };// "DejaVu Sans";
            // 获取第一个工作表
            Worksheet worksheet = workbook.Worksheets[0];
            // 创建SaveShapeTypeOption对象
            var pageSetup = worksheet.PageSetup;
            pageSetup.TopMargin = 0;
            pageSetup.BottomMargin = 0;
            pageSetup.LeftMargin = 0;
            pageSetup.RightMargin = 0;
            Stream imageStream = worksheet.ToImage(worksheet.FirstRow, worksheet.FirstColumn, worksheet.LastRow, worksheet.LastColumn);
            imageStream.Seek(0, SeekOrigin.Begin);
            Image image = Image.FromStream(imageStream);
            image.Save(imagePath);
            imageStream.Close();
            image.Dispose();
            workbook.Dispose();
        }

        /// <summary>
        /// 根据列最大值自动适应列宽设置
        /// </summary>
        /// <param name="excelPath"></param>
        public static void SetAutoSizeColumnByContent(string excelPath, string newPath =null)
        {
            IWorkbook workbook;
            using (FileStream file = new FileStream(excelPath, FileMode.OpenOrCreate, FileAccess.ReadWrite))
            {
                if (excelPath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase))
                {
                    workbook = new XSSFWorkbook(file);
                }
                else
                {
                    workbook = new HSSFWorkbook(file);
                }
                var sheet = workbook.GetSheetAt(0);
                var headRow = sheet.GetRow(0);
                for (int i = 0; i < headRow.LastCellNum; i++)
                {
                    int length = 0;
                    for (int j = 0; j < sheet.LastRowNum; j++)
                    {
                        var tempRow = sheet.GetRow(j);
                        var tempCell = tempRow.GetCell(i);
                        var cellContent = tempCell?.ToString() ?? string.Empty;
                        if (!string.IsNullOrEmpty(cellContent))
                        {
                            var byteLength = Encoding.Default.GetBytes(cellContent).Length;
                            if (length < byteLength)
                            {
                                length = byteLength;
                            }
                        }
                    }
                    sheet.SetColumnWidth(i, length * 256);
                }
            }
            if (string.IsNullOrEmpty(newPath))
            {
                newPath = excelPath;
            }
            // 保存文件
            using (var stream = new FileStream(newPath, FileMode.Create, FileAccess.Write))
            {
                workbook.Write(stream);
            }
        }

        /// <summary>
        /// 导入excel时，需要移除产权信息
        /// </summary>
        /// <param name="fileStream"></param>
        /// <returns></returns>
        public static void RemoveCopyRightFromExcel(Stream fileStream, string newExcelPath)
        {
            IWorkbook workbook1 = new XSSFWorkbook(fileStream);
            var sheet = workbook1.GetSheetAt(0);
            sheet.ShiftRows(2, sheet.LastRowNum, -2);
            // 保存文件
            using (var stream = new FileStream(newExcelPath, FileMode.Create, FileAccess.Write))
            {
                workbook1.Write(stream);
            }
            workbook1.Close();
        }

        /// <summary>
        /// 锁定excel指定的列
        /// </summary>
        /// <param name="excelPath"></param>
        /// <param name="columnIndex"></param>
        public static void LockExcelColumn(IWorkbook workbook, List<int> columnIndex)
        {
            var sheet = workbook.GetSheetAt(0);
            var lastRowNum = sheet.LastRowNum;
            var lastRow = sheet.GetRow(lastRowNum);
            var maxColumn = lastRow.LastCellNum;
            for (int columnNum = 0; columnNum <= maxColumn; columnNum++)
            {
                int columnWidth = sheet.GetColumnWidth(columnNum) / 256;
                for (int rowNum = 0; rowNum <= sheet.LastRowNum; rowNum++)
                {
                    IRow currentRow;
                    if (sheet.GetRow(rowNum) == null)
                    {
                        currentRow = sheet.CreateRow(rowNum);
                    }
                    else
                    {
                        currentRow = sheet.GetRow(rowNum);
                    }

                    if (currentRow.GetCell(columnNum) != null)
                    {
                        ICell currentCell = currentRow.GetCell(columnNum);
                        int length = Encoding.UTF8.GetBytes(currentCell.ToString()).Length;
                        if (columnWidth < length)
                        {
                            columnWidth = length;
                        }
                    }
                }
                sheet.SetColumnWidth(columnNum, columnWidth * 256);
            }
            sheet.ProtectSheet("Fiberhome@2025");
            XSSFCellStyle style = CreateStyle((XSSFWorkbook)workbook, HorizontalAlignment.Center, VerticalAlignment.Center, 10, true, false, 0, "宋体",
                  true, false, true, true, FillPattern.SolidForeground, HSSFColor.Grey25Percent.Index, HSSFColor.Black.Index, NPOI.SS.UserModel.FontUnderlineType.None, FontSuperScript.None, false);
            style.IsLocked = true;

            XSSFCellStyle dataStyle = CreateStyle((XSSFWorkbook)workbook, HorizontalAlignment.Center, VerticalAlignment.Center, 10, true, false, 0, "宋体",
                 true, false, true, true, FillPattern.SolidForeground, HSSFColor.LightGreen.Index, HSSFColor.Black.Index, NPOI.SS.UserModel.FontUnderlineType.None, FontSuperScript.None, false);
            dataStyle.IsLocked = false;

            for (int i = 0; i <= lastRowNum; i++)
            {
                var curRow = sheet.GetRow(i);
                var cellNum = curRow.LastCellNum;
                for (int j = 0; j < cellNum; j++)
                {
                    if (columnIndex.Contains(j))
                    {
                        curRow.GetCell(j).CellStyle = style;
                    }
                    else
                    {
                        curRow.GetCell(j).CellStyle = dataStyle;
                    }
                }
            }
        }


        /// <summary>
        /// 行内单元格常用样式设置
        /// </summary>
        /// <param name="workbook">Excel文件对象</param>
        /// <param name="hAlignment">水平布局方式</param>
        /// <param name="vAlignment">垂直布局方式</param>
        /// <param name="fontHeightInPoints">字体大小</param>
        /// <param name="isAddBorder">是否需要边框</param>
        /// <param name="isBold">是否加粗</param>
        /// <param name="boldWeight">字体加粗 (None = 0,Normal = 400，Bold = 700</param>
        /// <param name="fontName">字体（仿宋，楷体，宋体，微软雅黑...与Excel主题字体相对应）</param>
        /// <param name="isAddBorderColor">是否增加边框颜色</param>
        /// <param name="isItalic">是否将文字变为斜体</param>
        /// <param name="isLineFeed">是否自动换行</param>
        /// <param name="isAddCellBackground">是否增加单元格背景颜色</param>
        /// <param name="fillPattern">填充图案样式(FineDots 细点，SolidForeground立体前景，isAddFillPattern=true时存在)</param>
        /// <param name="cellBackgroundColor">单元格背景颜色（当isAddCellBackground=true时存在）</param>
        /// <param name="fontColor">字体颜色</param>
        /// <param name="underlineStyle">下划线样式（无下划线[None],单下划线[Single],双下划线[Double],会计用单下划线[SingleAccounting],会计用双下划线[DoubleAccounting]）</param>
        /// <param name="typeOffset">字体上标下标(普通默认值[None],上标[Sub],下标[Super]),即字体在单元格内的上下偏移量</param>
        /// <param name="isStrikeout">是否显示删除线</param>
        /// <returns></returns>
        public static XSSFCellStyle CreateStyle(XSSFWorkbook workbook, HorizontalAlignment hAlignment, VerticalAlignment vAlignment, short fontHeightInPoints, bool isAddBorder, bool isBold, short boldWeight, string fontName, bool isAddBorderColor, bool isItalic, bool isLineFeed, bool isAddCellBackground, FillPattern fillPattern, short cellBackgroundColor, short fontColor, NPOI.SS.UserModel.FontUnderlineType underlineStyle, FontSuperScript typeOffset = FontSuperScript.None, bool isStrikeout = false)
        {
            XSSFCellStyle cellStyle = (XSSFCellStyle)workbook.CreateCellStyle(); //创建列头单元格实例样式
            cellStyle.Alignment = hAlignment; //水平居中
            cellStyle.VerticalAlignment = vAlignment; //垂直居中
            cellStyle.WrapText = isLineFeed;//自动换行
            //背景颜色，边框颜色，字体颜色都是使用 XSSFColor属性中的对应调色板索引，关于 XSSFColor 颜色索引对照表，详情参考：https://www.cnblogs.com/Brainpan/p/5804167.html

            //TODO：引用了NPOI后可通过ICellStyle 接口的 FillForegroundColor 属性实现 Excel 单元格的背景色设置，FillPattern 为单元格背景色的填充样式

            //TODO:十分注意，要设置单元格背景色必须是FillForegroundColor和FillPattern两个属性同时设置，否则是不会显示背景颜色
            if (isAddCellBackground)
            {
                cellStyle.FillForegroundColor = cellBackgroundColor;//单元格背景颜色
                cellStyle.FillPattern = fillPattern;//填充图案样式(FineDots 细点，SolidForeground立体前景)
            }


            //是否增加边框
            if (isAddBorder)
            {
                //常用的边框样式 None(没有),Thin(细边框，瘦的),Medium(中等),Dashed(虚线),Dotted(星罗棋布的),Thick(厚的),Double(双倍),Hair(头发)[上右下左顺序设置]
                cellStyle.BorderBottom = BorderStyle.Thin;
                cellStyle.BorderRight = BorderStyle.Thin;
                cellStyle.BorderTop = BorderStyle.Thin;
                cellStyle.BorderLeft = BorderStyle.Thin;
            }

            //是否设置边框颜色
            if (isAddBorderColor)
            {
                //边框颜色[上右下左顺序设置]
                cellStyle.TopBorderColor = IndexedColors.DarkGreen.Index;//DarkGreen(黑绿色)
                cellStyle.RightBorderColor = IndexedColors.DarkGreen.Index;
                cellStyle.BottomBorderColor = IndexedColors.DarkGreen.Index;
                cellStyle.LeftBorderColor = IndexedColors.DarkGreen.Index;
            }

            /**
             * 设置相关字体样式
             */
            var cellStyleFont = (XSSFFont)workbook.CreateFont(); //创建字体

            //假如字体大小只需要是粗体的话直接使用下面该属性即可
            //cellStyleFont.IsBold = true;

            cellStyleFont.IsBold = isBold; //字体加粗
            cellStyleFont.FontHeightInPoints = fontHeightInPoints; //字体大小
            cellStyleFont.FontName = fontName;//字体（仿宋，楷体，宋体 ）
            cellStyleFont.Color = fontColor;//设置字体颜色
            cellStyleFont.IsItalic = isItalic;//是否将文字变为斜体
            cellStyleFont.Underline = underlineStyle;//字体下划线
            cellStyleFont.TypeOffset = typeOffset;//字体上标下标
            cellStyleFont.IsStrikeout = isStrikeout;//是否有删除线

            cellStyle.SetFont(cellStyleFont); //将字体绑定到样式
            return cellStyle;
        }

    }
}
