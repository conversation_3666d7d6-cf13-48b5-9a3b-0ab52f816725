﻿using Hub.Mobile.Interface;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Text;
using Xamarin.Forms;

namespace Hub.Mobile.ApiClient
{
    public class BaseRequest
    {
        static IUIResourceService uIResourceService = DependencyService.Get<IUIResourceService>();
        public string Culture { get { string appCulture = uIResourceService.GetCurrentCultureName(); return string.IsNullOrWhiteSpace(appCulture) ? "en" : (appCulture == "id" ? "idn" : appCulture); } }
    }
}
