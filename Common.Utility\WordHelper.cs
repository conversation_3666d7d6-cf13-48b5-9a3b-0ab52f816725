﻿using Spire.Doc.Documents;
using Spire.Doc.Fields;
using Spire.Doc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Drawing;

namespace Common.Utility
{
    public class WordHelper
    {
        public static void AddHeaderFooterWithLogo(string filePath, string headerTxt, string footerTxt, string imgPath)
        {
            Spire.Doc.Document document = new Spire.Doc.Document(filePath);
            Section sec = document.AddSection();
            Paragraph para = sec.AddParagraph();

            //声明一个HeaderFooter类对象，添加页眉、页脚
            HeaderFooter header = sec.HeadersFooters.Header;
            Paragraph headerPara = header.AddParagraph();

            //添加图片和文本到页眉，并设置文本格式
            TextRange TR = headerPara.AppendText(headerTxt);
            TR.CharacterFormat.FontSize = 12;
            TR.CharacterFormat.Bold = false;
            headerPara.Format.HorizontalAlignment = HorizontalAlignment.Left;

            DocPicture headerImage = headerPara.AppendPicture(imgPath);
            TR.CharacterFormat.FontSize = 12;
            TR.CharacterFormat.Bold = false;
            headerImage.TextWrappingType = TextWrappingType.Left;


            //添加文本到页脚，并设置格式
            HeaderFooter footer = sec.HeadersFooters.Footer;
            Paragraph footerPara = footer.AddParagraph();
            TR = footerPara.AppendText(footerTxt);
            TR.CharacterFormat.Bold = false;
            TR.CharacterFormat.FontSize = 9;


            //添加页码
            HeaderFooter footer1 = sec.HeadersFooters.Footer;
            Paragraph footerPara1 = footer1.AddParagraph();
            TR = footerPara1.AppendField("page number", FieldType.FieldPage);
            TR = footerPara1.AppendText("/");
            TR = footerPara1.AppendField("number of pages", FieldType.FieldNumPages);
            footerPara1.Format.HorizontalAlignment = HorizontalAlignment.Center;
            TR.CharacterFormat.Bold = false;
            TR.CharacterFormat.FontSize = 6;

            //保存文档并运行该文档
            document.SaveToFile(filePath, FileFormat.Docx);

            //string Mark = "Evaluation Warning: The document was created with Spire.Doc for .NET.";
            //ReplaceTextInWord(filePath, Mark, "");
        }

        /// <summary>
        /// 提取Word文档（doc/docx）中的所有文本内容（使用Spire.Doc，兼容Linux/Windows）
        /// </summary>
        /// <param name="filePath">Word文件路径</param>
        /// <returns>文档全部文本内容，失败返回null</returns>
        public static string ExtractTextFromWord(string filePath)
        {
            if (string.IsNullOrEmpty(filePath) || !System.IO.File.Exists(filePath))
                return null;
            try
            {
                var document = new Spire.Doc.Document();
                document.LoadFromFile(filePath);
                var sb = new System.Text.StringBuilder();
                foreach (Spire.Doc.Section section in document.Sections)
                {
                    foreach (Spire.Doc.Documents.Paragraph paragraph in section.Paragraphs)
                    {
                        sb.AppendLine(paragraph.Text);
                    }
                }
                return sb.ToString();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 提取所有表格内容，每行为一个List<string>，每个单元格为一个元素
        /// </summary>
        /// <param name="filePath">Word文件路径</param>
        /// <returns>所有表格的所有行内容</returns>
        public static List<List<string>> ExtractAllTables(string filePath)
        {
            var result = new List<List<string>>();
            if (string.IsNullOrEmpty(filePath) || !System.IO.File.Exists(filePath))
                return result;
            try
            {
                var document = new Spire.Doc.Document();
                document.LoadFromFile(filePath);
                foreach (Spire.Doc.Section section in document.Sections)
                {
                    foreach (Spire.Doc.Table table in section.Tables)
                    {
                        foreach (Spire.Doc.TableRow row in table.Rows)
                        {
                            var rowData = new List<string>();
                            foreach (Spire.Doc.TableCell cell in row.Cells)
                            {
                                string cellText = "";
                                foreach (Paragraph p in cell.Paragraphs)
                                {
                                    cellText += p.Text;
                                }
                                rowData.Add(cellText.Trim());
                            }
                            result.Add(rowData);
                        }
                    }
                }
            }
            catch { }
            return result;
        }

        /// <summary>
        /// 提取正文所有文本内容
        /// </summary>
        /// <param name="filePath">Word文件路径</param>
        /// <returns>正文全部文本内容</returns>
        public static string ExtractAllText(string filePath)
        {
            if (string.IsNullOrEmpty(filePath) || !System.IO.File.Exists(filePath))
                return string.Empty;
            try
            {
                var document = new Spire.Doc.Document();
                document.LoadFromFile(filePath);
                var sb = new System.Text.StringBuilder();
                foreach (Spire.Doc.Section section in document.Sections)
                {
                    foreach (Spire.Doc.Documents.Paragraph paragraph in section.Paragraphs)
                    {
                        sb.AppendLine(paragraph.Text);
                    }
                }
                return sb.ToString();
            }
            catch { return string.Empty; }
        }
    }
}
