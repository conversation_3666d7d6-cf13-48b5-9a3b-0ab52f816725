﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_timezoneinfo : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<double>(
                name: "OffSets",
                table: "Netproviders",
                type: "double",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<string>(
                name: "TZNameKey",
                table: "Netproviders",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "TimeZoneId",
                table: "Netproviders",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "OffSets",
                table: "Netproviders");

            migrationBuilder.DropColumn(
                name: "TZ<PERSON>ame<PERSON><PERSON>",
                table: "Netproviders");

            migrationBuilder.DropColumn(
                name: "TimeZoneId",
                table: "Netproviders");
        }
    }
}
