﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class change_table_index : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_TaskTpls_DisplayName",
                table: "TaskTpls");

            migrationBuilder.DropIndex(
                name: "IX_TaskItemAndTplRels_TplID_TaskItemID",
                table: "TaskItemAndTplRels");

            migrationBuilder.DropIndex(
                name: "IX_RelProjectUser_ProjectID_UserEmail",
                table: "RelProjectUser");

            migrationBuilder.DropIndex(
                name: "IX_Regions_Code",
                table: "Regions");

            migrationBuilder.DropIndex(
                name: "IX_Projects_Code",
                table: "Projects");

            migrationBuilder.DropIndex(
                name: "IX_Projects_DisplayName",
                table: "Projects");

            migrationBuilder.DropIndex(
                name: "IX_ObjTypeLevels_Code",
                table: "ObjTypeLevels");

            migrationBuilder.DropIndex(
                name: "IX_Netproviders_CodeSk",
                table: "Netproviders");

            migrationBuilder.DropIndex(
                name: "IX_ContractorTaskItemTplRels_ContractorID_TaskItemID_TplID",
                table: "ContractorTaskItemTplRels");

            migrationBuilder.DropIndex(
                name: "IX_Approvers_UserEmail_AllowLevel_ProjectID",
                table: "Approvers");

            migrationBuilder.DropIndex(
                name: "IX_Approvals_TaskItemID_StepID_Level_TargetType_ValueID",
                table: "Approvals");

            migrationBuilder.AlterColumn<string>(
                name: "MsgMessage",
                table: "emt_noticerecords",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int",
                oldMaxLength: 255)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "MailMessage",
                table: "emt_noticerecords",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int",
                oldMaxLength: 255)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "DingDingMessage",
                table: "emt_noticerecords",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int",
                oldMaxLength: 255)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_TaskTpls_DisplayName",
                table: "TaskTpls",
                column: "DisplayName");

            migrationBuilder.CreateIndex(
                name: "IX_TaskItemAndTplRels_TplID_TaskItemID",
                table: "TaskItemAndTplRels",
                columns: new[] { "TplID", "TaskItemID" });

            migrationBuilder.CreateIndex(
                name: "IX_RelProjectUser_ProjectID_UserEmail",
                table: "RelProjectUser",
                columns: new[] { "ProjectID", "UserEmail" });

            migrationBuilder.CreateIndex(
                name: "IX_Regions_Code",
                table: "Regions",
                column: "Code");

            migrationBuilder.CreateIndex(
                name: "IX_Projects_Code",
                table: "Projects",
                column: "Code");

            migrationBuilder.CreateIndex(
                name: "IX_Projects_DisplayName",
                table: "Projects",
                column: "DisplayName");

            migrationBuilder.CreateIndex(
                name: "IX_ObjTypeLevels_Code",
                table: "ObjTypeLevels",
                column: "Code");

            migrationBuilder.CreateIndex(
                name: "IX_Netproviders_CodeSk",
                table: "Netproviders",
                column: "CodeSk");

            migrationBuilder.CreateIndex(
                name: "IX_emt_reportorders_OrderNo",
                table: "emt_reportorders",
                column: "OrderNo");

            migrationBuilder.CreateIndex(
                name: "IX_emt_operationrecords_ObjectId",
                table: "emt_operationrecords",
                column: "ObjectId");

            migrationBuilder.CreateIndex(
                name: "IX_emt_noticerecords_ObjectId",
                table: "emt_noticerecords",
                column: "ObjectId");

            migrationBuilder.CreateIndex(
                name: "IX_emt_netproviderrel_CodeSk",
                table: "emt_netproviderrel",
                column: "CodeSk");

            migrationBuilder.CreateIndex(
                name: "IX_emt_netproviderrel_DisOrderNo",
                table: "emt_netproviderrel",
                column: "DisOrderNo");

            migrationBuilder.CreateIndex(
                name: "IX_emt_netproviderrel_OrderNo",
                table: "emt_netproviderrel",
                column: "OrderNo");

            migrationBuilder.CreateIndex(
                name: "IX_emt_involveuser_OrderNo",
                table: "emt_involveuser",
                column: "OrderNo");

            migrationBuilder.CreateIndex(
                name: "IX_emt_extvalues_OrderNo",
                table: "emt_extvalues",
                column: "OrderNo");

            migrationBuilder.CreateIndex(
                name: "IX_emt_executetask_DisOrderNo",
                table: "emt_executetask",
                column: "DisOrderNo");

            migrationBuilder.CreateIndex(
                name: "IX_emt_executetask_NetOrderNo",
                table: "emt_executetask",
                column: "NetOrderNo");

            migrationBuilder.CreateIndex(
                name: "IX_emt_executetask_RepOrderNo",
                table: "emt_executetask",
                column: "RepOrderNo");

            migrationBuilder.CreateIndex(
                name: "IX_emt_executetask_TaskNo",
                table: "emt_executetask",
                column: "TaskNo");

            migrationBuilder.CreateIndex(
                name: "IX_emt_dispatchorders_OrderNo",
                table: "emt_dispatchorders",
                column: "OrderNo");

            migrationBuilder.CreateIndex(
                name: "IX_ContractorTaskItemTplRels_ContractorID_TaskItemID_TplID",
                table: "ContractorTaskItemTplRels",
                columns: new[] { "ContractorID", "TaskItemID", "TplID" });

            migrationBuilder.CreateIndex(
                name: "IX_Approvers_UserEmail_AllowLevel_ProjectID",
                table: "Approvers",
                columns: new[] { "UserEmail", "AllowLevel", "ProjectID" });

            migrationBuilder.CreateIndex(
                name: "IX_Approvals_TaskItemID_StepID_Level_TargetType_ValueID",
                table: "Approvals",
                columns: new[] { "TaskItemID", "StepID", "Level", "TargetType", "ValueID" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_TaskTpls_DisplayName",
                table: "TaskTpls");

            migrationBuilder.DropIndex(
                name: "IX_TaskItemAndTplRels_TplID_TaskItemID",
                table: "TaskItemAndTplRels");

            migrationBuilder.DropIndex(
                name: "IX_RelProjectUser_ProjectID_UserEmail",
                table: "RelProjectUser");

            migrationBuilder.DropIndex(
                name: "IX_Regions_Code",
                table: "Regions");

            migrationBuilder.DropIndex(
                name: "IX_Projects_Code",
                table: "Projects");

            migrationBuilder.DropIndex(
                name: "IX_Projects_DisplayName",
                table: "Projects");

            migrationBuilder.DropIndex(
                name: "IX_ObjTypeLevels_Code",
                table: "ObjTypeLevels");

            migrationBuilder.DropIndex(
                name: "IX_Netproviders_CodeSk",
                table: "Netproviders");

            migrationBuilder.DropIndex(
                name: "IX_emt_reportorders_OrderNo",
                table: "emt_reportorders");

            migrationBuilder.DropIndex(
                name: "IX_emt_operationrecords_ObjectId",
                table: "emt_operationrecords");

            migrationBuilder.DropIndex(
                name: "IX_emt_noticerecords_ObjectId",
                table: "emt_noticerecords");

            migrationBuilder.DropIndex(
                name: "IX_emt_netproviderrel_CodeSk",
                table: "emt_netproviderrel");

            migrationBuilder.DropIndex(
                name: "IX_emt_netproviderrel_DisOrderNo",
                table: "emt_netproviderrel");

            migrationBuilder.DropIndex(
                name: "IX_emt_netproviderrel_OrderNo",
                table: "emt_netproviderrel");

            migrationBuilder.DropIndex(
                name: "IX_emt_involveuser_OrderNo",
                table: "emt_involveuser");

            migrationBuilder.DropIndex(
                name: "IX_emt_extvalues_OrderNo",
                table: "emt_extvalues");

            migrationBuilder.DropIndex(
                name: "IX_emt_executetask_DisOrderNo",
                table: "emt_executetask");

            migrationBuilder.DropIndex(
                name: "IX_emt_executetask_NetOrderNo",
                table: "emt_executetask");

            migrationBuilder.DropIndex(
                name: "IX_emt_executetask_RepOrderNo",
                table: "emt_executetask");

            migrationBuilder.DropIndex(
                name: "IX_emt_executetask_TaskNo",
                table: "emt_executetask");

            migrationBuilder.DropIndex(
                name: "IX_emt_dispatchorders_OrderNo",
                table: "emt_dispatchorders");

            migrationBuilder.DropIndex(
                name: "IX_ContractorTaskItemTplRels_ContractorID_TaskItemID_TplID",
                table: "ContractorTaskItemTplRels");

            migrationBuilder.DropIndex(
                name: "IX_Approvers_UserEmail_AllowLevel_ProjectID",
                table: "Approvers");

            migrationBuilder.DropIndex(
                name: "IX_Approvals_TaskItemID_StepID_Level_TargetType_ValueID",
                table: "Approvals");

            migrationBuilder.AlterColumn<int>(
                name: "MsgMessage",
                table: "emt_noticerecords",
                type: "int",
                maxLength: 255,
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(string),
                oldType: "varchar(255)",
                oldMaxLength: 255,
                oldNullable: true)
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<int>(
                name: "MailMessage",
                table: "emt_noticerecords",
                type: "int",
                maxLength: 255,
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(string),
                oldType: "varchar(255)",
                oldMaxLength: 255,
                oldNullable: true)
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<int>(
                name: "DingDingMessage",
                table: "emt_noticerecords",
                type: "int",
                maxLength: 255,
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(string),
                oldType: "varchar(255)",
                oldMaxLength: 255,
                oldNullable: true)
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_TaskTpls_DisplayName",
                table: "TaskTpls",
                column: "DisplayName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TaskItemAndTplRels_TplID_TaskItemID",
                table: "TaskItemAndTplRels",
                columns: new[] { "TplID", "TaskItemID" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_RelProjectUser_ProjectID_UserEmail",
                table: "RelProjectUser",
                columns: new[] { "ProjectID", "UserEmail" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Regions_Code",
                table: "Regions",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Projects_Code",
                table: "Projects",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Projects_DisplayName",
                table: "Projects",
                column: "DisplayName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ObjTypeLevels_Code",
                table: "ObjTypeLevels",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Netproviders_CodeSk",
                table: "Netproviders",
                column: "CodeSk",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ContractorTaskItemTplRels_ContractorID_TaskItemID_TplID",
                table: "ContractorTaskItemTplRels",
                columns: new[] { "ContractorID", "TaskItemID", "TplID" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Approvers_UserEmail_AllowLevel_ProjectID",
                table: "Approvers",
                columns: new[] { "UserEmail", "AllowLevel", "ProjectID" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Approvals_TaskItemID_StepID_Level_TargetType_ValueID",
                table: "Approvals",
                columns: new[] { "TaskItemID", "StepID", "Level", "TargetType", "ValueID" },
                unique: true);
        }
    }
}
