﻿using Hub.Mobile.Utility;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Hub.Mobile.Plugin.Plugins
{
    public class Position:IPlugin
    {
        public async Task<ExecResult> GetGPS()
        {
            ExecResult result = new ExecResult();
            var position = await MobileGPSHelper.GetPosition();
            result.Success = position.Item1;
            if (!position.Item1)
            {
                result.Message = position.Item2;
            }
            else
            {
                result.Result = JsonConvert.SerializeObject(new { position.Item3.Latitude, position.Item3.Longitude, position.Item3.Altitude });
            }
            return result;
        }
    }
}
