﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_task_item_fields : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Value",
                table: "TaskItemDimValues",
                newName: "Value3");

            migrationBuilder.AddColumn<int>(
                name: "SupportFileTypes",
                table: "TaskStepValues",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "Value1",
                table: "TaskItemDimValues",
                type: "varchar(64)",
                maxLength: 64,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "Value2",
                table: "TaskItemDimValues",
                type: "varchar(64)",
                maxLength: 64,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "SupportFileTypes",
                table: "TaskStepValues");

            migrationBuilder.DropColumn(
                name: "Value1",
                table: "TaskItemDimValues");

            migrationBuilder.DropColumn(
                name: "Value2",
                table: "TaskItemDimValues");

            migrationBuilder.RenameColumn(
                name: "Value3",
                table: "TaskItemDimValues",
                newName: "Value");
        }
    }
}
