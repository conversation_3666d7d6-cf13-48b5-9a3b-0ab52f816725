﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_executetask_column : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "EndRemindCount",
                table: "emt_executetask",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "MiddleRemindCount",
                table: "emt_executetask",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "StartRemindCount",
                table: "emt_executetask",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EndRemindCount",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "MiddleRemindCount",
                table: "emt_executetask");

            migrationBuilder.DropColumn(
                name: "StartRemindCount",
                table: "emt_executetask");
        }
    }
}
