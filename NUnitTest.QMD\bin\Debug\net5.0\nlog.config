﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<targets>
		<!--此部分中的所有目标将自动异步-->
		<target name="asyncFile" xsi:type="AsyncWrapper">
			<!--项目日志保存文件路径说明fileName="${basedir}/保存目录，以年月日的格式创建/${shortdate}/${记录器名称}-${单级记录}-${shortdate}.txt"-->
			<target name="log_file" xsi:type="File"
					fileName="${basedir}/Logs/${shortdate}/${level}-${shortdate}.txt"
					layout="${longdate} | ${message} ${onexception:${exception:format=message} ${newline} ${stacktrace} ${newline}"
					archiveFileName="${basedir}/archives/${logger}-${level}-${shortdate}-{#####}.txt"
					archiveAboveSize="102400"
					archiveNumbering="Sequence"
					concurrentWrites="true"
					keepFileOpen="false" />
		</target>
		<!--使用可自定义的着色将日志消息写入控制台-->
		<target name="colorConsole" xsi:type="ColoredConsole" layout="[${date:format=HH\:mm\:ss}]:${message} ${exception:format=message}" />
	</targets>

	<!--规则配置,final - 最终规则匹配后不处理任何规则-->
	<rules>
		<logger name="Microsoft.*" minlevel="Info" writeTo="" final="true" />
		<logger name="*" minlevel="Info" writeTo="asyncFile" />
		<logger name="*" minlevel="Info" writeTo="colorConsole" />
	</rules>
</nlog>