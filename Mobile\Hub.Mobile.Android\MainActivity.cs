﻿using System;

using Android.App;
using Android.Content.PM;
using Android.Runtime;
using Android.Views;
using Android.Widget;
using Android.OS;
using Plugin.CurrentActivity;
using Plugin.Permissions;
using System.Linq;
using Hub.Mobile.CommonControl;
using Acr.UserDialogs;

namespace Hub.Mobile.Droid
{
    [Activity(Theme = "@style/MainTheme", MainLauncher = true, ConfigurationChanges = ConfigChanges.ScreenSize | ConfigChanges.Orientation | ConfigChanges.UiMode | ConfigChanges.ScreenLayout | ConfigChanges.SmallestScreenSize )]
    public class MainActivity : global::Xamarin.Forms.Platform.Android.FormsAppCompatActivity
    {
        public static Android.Content.Context AppContext;
        private Xamarin.Forms.Page lastPage;
        protected override void OnCreate(Bundle savedInstanceState)
        {
            this.RequestedOrientation = Android.Content.PM.ScreenOrientation.Portrait;
            AppContext = ApplicationContext;
            TabLayoutResource = Resource.Layout.Tabbar;
            ToolbarResource = Resource.Layout.Toolbar;

            base.OnCreate(savedInstanceState);
            Rg.Plugins.Popup.Popup.Init(this);
            UserDialogs.Init(this);
            CrossCurrentActivity.Current.Init(this, savedInstanceState);
            Xamarin.Essentials.Platform.Init(this, savedInstanceState);
            global::Xamarin.Forms.Forms.Init(this, savedInstanceState);
            LoadApplication(new App());
            AndroidX.AppCompat.Widget.Toolbar toolbar
 = this.FindViewById<AndroidX.AppCompat.Widget.Toolbar>(Resource.Id.toolbar);
            SetSupportActionBar(toolbar);
        }
        public override void OnRequestPermissionsResult(int requestCode, string[] permissions, [GeneratedEnum] Android.Content.PM.Permission[] grantResults)
        {
            PermissionsImplementation.Current.OnRequestPermissionsResult(requestCode, permissions, grantResults);
            base.OnRequestPermissionsResult(requestCode, permissions, grantResults);
        }
        public override bool OnOptionsItemSelected(IMenuItem item)
        {
            if (item.ItemId == 16908332)
            {
                var page = Xamarin.Forms.Application.Current.
                     MainPage.Navigation.NavigationStack.LastOrDefault();
                if (page == lastPage)
                {
                    return false;
                }
                lastPage = page;
                if (page is HybridPage)
                {
                    var hybirdPage = page as HybridPage;
                    hybirdPage.OnBackFire(UserBackCancelCallBack);
                    return false;
                }
                return base.OnOptionsItemSelected(item);
            }
            else
            {
                return base.OnOptionsItemSelected(item);
            }
        }
        private void UserBackCancelCallBack(HybridPage customContentPage)
        {
            if (lastPage == customContentPage)
            {
                lastPage = null;
            }
        }
        public override void OnBackPressed()
        {
            var page = Xamarin.Forms.Application.Current.
                     MainPage.Navigation.NavigationStack.LastOrDefault();
            if (page == lastPage)
            {
                return;
            }
            lastPage = page;
            if (page is HybridPage)
            {
                var currentpage = page as HybridPage;
                currentpage.OnBackFire(UserBackCancelCallBack);
            }
            else
            {
                base.OnBackPressed();
            }
        }
    }
}