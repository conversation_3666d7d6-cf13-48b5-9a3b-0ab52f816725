﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Common.Utility
{
    /// <summary>
    /// CDN地址
    /// </summary>
    public static class CdnAddressHelper
    {
        private const string _privateKey = "Fts20190701903904909";
        private static string _domainUrl = string.Empty;
        private static string _cdnRootUrl = string.Empty;
        private static string _cdnProjectName = string.Empty;
        private static char[] _invalidUrlChars =
            new[] { '\"', '<', '>', '\'', '$', '&', '|', '%', '#', '?', '/', ';', '+', '[', ']', '{', '}', '=', '@', ':' };

        public static string ReplaceInvalidUrlChars(string source, char replaceChar)
        {
            if (source == null)
            {
                return "";
            }
            else
            {
                var result = string.Empty;
                source = source.Trim();
                foreach (var c in source)
                {
                    if (_invalidUrlChars.Contains(c))
                    {
                        result += replaceChar;
                    }
                    else
                    {
                        result += c;
                    }
                }
                return result;
            }
        }

        public static void SetCDNRootUrl(string cdnRootUrl, string cdnProjectName,string domainUrl)
        {
            _cdnProjectName = cdnProjectName;
            _cdnRootUrl = cdnRootUrl;
            _domainUrl = domainUrl;
        }

        /// <summary>
        /// 因为每个工序值的ID都是唯一的，所以所有的值可以放到同一个目录下进行保存，
        /// 并且每一个工序值只允许保存一个文件，不再使用分隔符共享字段，所以统计照片数量只需要查询数据库即可
        /// 这样的好处是减少目录层级，用工序值的ID作为目录包含文件也可以在保留原始文件名的前提下避免文件复杂的重名处理场景
        /// </summary>
        /// <param name="stepValueFolderName">fts_industry的所有工序值保存的硬盘统一目录</param>
        /// <param name="stepId">工序值的数据库唯一ID</param>
        /// <param name="filename">文件名</param>
        public static string GetStepFileCdnAddress(string stepValueFolderName, string stepId, string filename)
        {
            var relativePath = $"/{stepValueFolderName}/{stepId}/{filename}";
            return GetCdnAddress(relativePath);
        }

        public static string GetCdnAddress(string relativePath, int timestamp = 0)
        {
            //if (!relativePath.StartsWith("/"))
            //{
            //    relativePath = "/" + relativePath;
            //}
            //if (relativePath.EndsWith("/"))
            //{
            //    relativePath = relativePath.Substring(0, relativePath.Length - 1);
            //}
            //var filename = relativePath.Split('/')[relativePath.Split('/').Length - 1];
            //var spath = relativePath.Substring(0, relativePath.Length - filename.Length);
            //String addressName = System.Web.HttpUtility.UrlEncode(filename);
            //String softwareAddress = addressName.Replace("%2F", "/");
            ////+号替换成空格
            //softwareAddress = softwareAddress.Replace("+", "%20");
            ////原来是+号的还原
            //softwareAddress = softwareAddress.Replace("%2b", "+");
            //var enspath = System.Web.HttpUtility.UrlEncode(spath);
            //String xxspath = enspath.Replace("%2F", "/");
            ////+号替换成空格
            //xxspath = xxspath.Replace("+", "%20");
            ////原来是+号的还原
            //xxspath = xxspath.Replace("%2b", "+").Replace("%2f", "/");
            //string sourceurl = "/" + _cdnProjectName + xxspath + softwareAddress;
            //long time = (long)(DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)).TotalMilliseconds;
            //if (timestamp > 0)
            //{
            //    time = time + timestamp;
            //}
            //var returnurl = "https://qmo-test.fiberhome.work" + sourceurl + "?auth_key=" + time + "-0-0-"
            //    + (sourceurl + "-" + time + "-0-0-" + _privateKey).MD5Encrypt32();
            //return returnurl;

            if (!relativePath.StartsWith("/"))
            {
                relativePath = "/" + relativePath;
            }
            if (relativePath.EndsWith("/"))
            {
                relativePath = relativePath.Substring(0, relativePath.Length - 1);
            }
            var filename = relativePath.Split('/')[relativePath.Split('/').Length - 1];
            var spath = relativePath.Substring(0, relativePath.Length - filename.Length);
            String addressName = Uri.EscapeUriString(filename);
            string sourceurl = "/" + _cdnProjectName + spath + addressName;
            long time = (long)(DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)).TotalMilliseconds;
            if (timestamp > 0)
            {
                time = time + timestamp;
            }
            var returnurl = _domainUrl + sourceurl + "?auth_key=" + time + "-0-0-"
                + (sourceurl + "-" + time + "-0-0-" + _privateKey).MD5Encrypt32();
            return returnurl;
        }
    }
}
