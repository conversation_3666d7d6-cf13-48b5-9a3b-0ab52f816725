﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Hub.Mobile.Views.PersonalInformationPage" Title="{DynamicResource PersonalInformation}">
    <ContentPage.Resources>
        <ResourceDictionary>
            <x:Double x:Key="RowHeight">50</x:Double>
            <x:Double x:Key="LineHeight">1</x:Double>
            <Style TargetType="Label">
                <Setter Property="VerticalOptions" Value="Center"></Setter>
                <Setter Property="TextColor" Value="Black"></Setter>
            </Style>
        </ResourceDictionary>
    </ContentPage.Resources>
    <ContentPage.Content>
        <Grid BackgroundColor="White" Padding="20,10,20,10" VerticalOptions="Start">
            <Grid.RowDefinitions>
                <RowDefinition Height="{StaticResource RowHeight}"></RowDefinition>
                <RowDefinition Height="{StaticResource LineHeight}"></RowDefinition>
                <RowDefinition Height="{StaticResource RowHeight}"></RowDefinition>
                <RowDefinition Height="{StaticResource LineHeight}"></RowDefinition>
                <RowDefinition Height="{StaticResource RowHeight}"></RowDefinition>
                <RowDefinition Height="{StaticResource LineHeight}"></RowDefinition>
                <RowDefinition Height="{StaticResource RowHeight}"></RowDefinition>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="100"></ColumnDefinition>
                <ColumnDefinition></ColumnDefinition>
            </Grid.ColumnDefinitions>
            <Label Grid.Row="0" Grid.Column="0" Text="{DynamicResource Name}" FontSize="Subtitle"></Label>
            <Label Grid.Row="0" Grid.Column="1" Text="{Binding Name}" HorizontalOptions="End"></Label>
            <Line Grid.Row="1" Grid.ColumnSpan="2" Background="#F7F8FA"></Line>
            <Label Grid.Row="2" Grid.Column="0" Text="{DynamicResource Email}"  FontSize="Subtitle"></Label>
            <Label Grid.Row="2" Grid.Column="1" Text="{Binding Email}" HorizontalOptions="End"></Label>
            <Line Grid.Row="3" Grid.ColumnSpan="2" Background="#F7F8FA"></Line>
            <Label Grid.Row="4" Grid.Column="0" Text="{DynamicResource Position}"  FontSize="Subtitle"></Label>
            <Label Grid.Row="4" Grid.Column="1" Text="{Binding Position}" HorizontalOptions="End"></Label>
            <Line Grid.Row="5" Grid.ColumnSpan="2" Background="#F7F8FA"></Line>
            <Label Grid.Row="6" Grid.Column="0" Text="{DynamicResource Area}"  FontSize="Subtitle"></Label>
            <Label Grid.Row="6" Grid.Column="1" Text="{Binding Area}" HorizontalOptions="End"></Label>
        </Grid>
    </ContentPage.Content>
</ContentPage>