﻿using Common.DAL.Methods;
using Flurl;
using Flurl.Http;
using Org.BouncyCastle.Asn1.X509.Qualified;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace Common.Utility
{
    public static class HttpHelper
    {
        public static async Task<T> Post<T>(string url, object body, Dictionary<string, string> dicParams = null)
        {
            return await url.WithHeader("Authorization", HttpContextHelper.Current?.Request.Headers["Authorization"].ToString())
                .SetQueryParams(dicParams).PostJsonAsync(body).ReceiveJson<T>();
        }
        public static async Task<T> Get<T>(string url, Dictionary<string, string> dicParams = null)
        {
            return await url.WithHeader("Cookie", "authState=1;").WithHeader("Authorization", HttpContextHelper.Current?.Request.Headers["Authorization"].ToString())
                 .SetQueryParams(dicParams).GetJsonAsync<T>();
        }

        public static async Task<T> Post<T>(string url, object body, string contentType, Dictionary<string, string> dicParams = null,Dictionary<string,string> headParams= null)
        {
            IFlurlRequest req = null;
            //添加请求头
            if (headParams != null)
            {
                foreach (KeyValuePair<string, string> item in headParams)
                {
                    if (req == null)
                    {
                        req = url.WithHeader(item.Key, item.Value);
                    }
                    else
                    {
                        req = req.WithHeader(item.Key, item.Value);
                    }
                }
            }
            // 设置header
            if (req == null)
            {
                req = url.WithHeader(Guid.NewGuid().ToString("N"), Guid.NewGuid().ToString("N"));
            }
            // 设置contentType
            if (string.IsNullOrWhiteSpace(contentType))
            {
                contentType = "application/json";
            }
            //请求数据
            if (contentType == "application/x-www-form-urlencoded")
            {
                return await req.WithHeader("Authorization", HttpContextHelper.Current?.Request.Headers["Authorization"].ToString())
                 .WithHeader("Content-Type", contentType)
                 .PostUrlEncodedAsync(dicParams)
                 .ReceiveJson<T>();
            }
            else
            {
                return await req.WithHeader("Authorization", HttpContextHelper.Current?.Request.Headers["Authorization"].ToString())
                 .WithHeader("Content-Type", contentType)
                 .SetQueryParams(dicParams).PostJsonAsync(body).ReceiveJson<T>();
            }
        }
        public static async Task<T> Get<T>(string url, string contentType, Dictionary<string, string> dicParams = null)
        {
            return await url.WithHeader("Authorization", HttpContextHelper.Current?.Request.Headers["Authorization"].ToString())
                  .WithHeader("Content-Type", contentType)
                  .SetQueryParams(dicParams).GetJsonAsync<T>();
        }

        public static async Task<T> PostWithAuth<T>(string url, object body, string authorization, Dictionary<string, string> dicParams = null)
        {
            return await url.WithHeader("Authorization", authorization)
                .SetQueryParams(dicParams).PostJsonAsync(body).ReceiveJson<T>();
        }
        public static async Task<T> PostAsync<T>(string url, object body, string contentType, Dictionary<string, string> dicParams = null)
        {
            int tryTimes = 3;
            int i = 1;
            T result = default;
            while (i <= tryTimes)
            {
                try
                {
                    result = await url.WithHeader("Cookie", "authState=1;")
                     .WithHeader("Content-Type", "application/json")
                     .SetQueryParams(dicParams)
                     .PostJsonAsync(body)
                     .ReceiveJson<T>();
                    break;
                }
                catch
                {
                    if (i == tryTimes)
                        throw;
                    Thread.Sleep(i * 1000);
                    i++;
                }
            }
            return result;
        }


        public static async Task<string> PostStringAsync(string url, object body, string contentType, Dictionary<string, string> dicParams = null)
        {
            int tryTimes = 3;
            int i = 1;
            string result = string.Empty;
            while (i <= tryTimes)
            {
                try
                {
                    result = await url.WithHeader("Cookie", "authState=1;")
                     .WithHeader("Content-Type", "application/json")
                     .SetQueryParams(dicParams)
                     .PostJsonAsync(body)
                     .ReceiveString();
                    break;
                }
                catch 
                {
                    if (i == tryTimes)
                        throw;
                    Thread.Sleep(i * 1000);
                    i++;
                }
            }
            return result;
        }


        public static async Task<string> PostStringAsync(string url, object body, string contentType, Dictionary<string, string> dicParams = null, Dictionary<string,string> dicHeaders = null)
        {
            int tryTimes = 3;
            int i = 1;
            string result = string.Empty;
            while (i <= tryTimes)
            {
                try
                {
                    IFlurlRequest request = url.WithHeader("Cookie", "authState=1;");
                    if (dicHeaders != null)
                    {
                        foreach (KeyValuePair<string,string> item in dicHeaders)
                        {
                            request = request.WithHeader(item.Key, item.Value);
                        }
                    }
                    result = await request.SetQueryParams(dicParams)
                     .PostJsonAsync(body)
                     .ReceiveString();
                    break;
                }
                catch
                {
                    if (i == tryTimes)
                        throw;
                    Thread.Sleep(i * 1000);
                    i++;
                }
            }
            return result;
        }


        /// <summary>
        /// 接收文件
        /// </summary>
        /// <param name="url"></param>
        /// <param name="body"></param>
        /// <param name="dicParams"></param>
        /// <returns></returns>
        public static async Task<byte[]> PostFileAsync(string url, object body, Dictionary<string, string> dicParams = null)
        {
            return await url.WithHeader("Cookie", "authState=1;").SetQueryParams(dicParams).PostJsonAsync(body).ReceiveBytes();
        }

        /// <summary>
        /// 接收文件
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public static async Task<string> DownloadFileAsync(string url, string localDirPath,string fileName)
        {
            return await url.DownloadFileAsync(localDirPath, fileName);
        }

        public static async Task<T> PostWithHeader<T>(string url, object body, Dictionary<string, string> dicParams = null, Dictionary<string, string> header = null)
        {
            IFlurlRequest req = null;
            if (header != null)
            {
                foreach (KeyValuePair<string, string> item in header)
                {
                    if (req == null)
                    {
                        req = url.WithHeader(item.Key, item.Value);
                    }
                    else
                    {
                        req = req.WithHeader(item.Key, item.Value);
                    }
                }
            }
            if (req == null)
            {
                req = url.WithHeader("Authorization", "");
            }
            return await req.SetQueryParams(dicParams).PostJsonAsync(body).ReceiveJson<T>();
        }

        public static async Task<string> PostStringWithHeader(string url, object body, Dictionary<string, string> dicParams = null, Dictionary<string, string> header = null)
        {
            IFlurlRequest req = null;
            if (header != null)
            {
                foreach (KeyValuePair<string, string> item in header)
                {
                    if (req == null)
                    {
                        req = url.WithHeader(item.Key, item.Value);
                    }
                    else
                    {
                        req = req.WithHeader(item.Key, item.Value);
                    }
                }
            }
            if (req == null)
            {
                req = url.WithHeader("Authorization", "");
            }
            return await req.SetQueryParams(dicParams).PostJsonAsync(body).ReceiveString();
        }

    }
}
