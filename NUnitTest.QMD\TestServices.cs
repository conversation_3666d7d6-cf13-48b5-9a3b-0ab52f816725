using AutoMapper;
using Common.DAL.Methods;
using Common.Service;
using Common.Utility;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NUnit.Framework;
using QMD.DAL;
using QMD.DAL.Table;
using QMD.Env;
using QMD.Model;
using QMD.Model.Cycle;
using QMD.Model.QA;
using QMD.Repository;
using QMD.Service;
using QMD.Service.QMOV2Service;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace NUnitTest.QMD
{
    public class TestServices
    {
        public IServiceCollection _services = null;
        public ServiceProvider _serviceProvider = null;
        private IConfiguration _configuration = null;

        [SetUp]
        public void Setup()
        {
            _services = new ServiceCollection();

            var builder = new ConfigurationBuilder()
               .SetBasePath(AppContext.BaseDirectory)
               .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
               .AddEnvironmentVariables();
            _configuration = builder.Build();

            _services.AddSingleton<IConfiguration>(_configuration);

            _services.AddDbContext<QmdDbContext>(
                options =>
         options.UseMySql(
             _configuration.GetConnectionString("DefaultConnection"),
             ServerVersion.AutoDetect(_configuration.GetConnectionString("DefaultConnection")
                 ))
             );

            _services.RegisterRepositories();
            _services.RegisterServices();

            ConfigEnvValues.Init(_configuration);
            AutoMapperConfig.Configure(new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<AutoMapperProfile>();
            }));

            _serviceProvider = _services.BuildServiceProvider();

            HttpContextHelper.InitHttpContext(_serviceProvider);
            NLog.LogManager.LoadConfiguration("nlog.config");

            CdnAddressHelper.SetCDNRootUrl(ConfigEnvValues.CDNRootUrl, ConfigEnvValues.CDNProjectName, ConfigEnvValues.DomainURL);
        }
        [Test]
        public void TestInserTaskItem()
        {
            DateTime time = DateTime.Now;
            DateTime startTime = time.AddDays(1 - time.Day).Date;
            DateTime endTime = time.AddMonths(1).AddDays(1 - time.Day).Date;


            var _appSolutionRepository = _serviceProvider.GetService<AppSolutionRepository>();

            var result1 = _appSolutionRepository.Query(t => t.IsActived).AsEnumerable().GroupBy(t => t.FirstNode).Select(t => t.FirstOrDefault()).ToList();

            var _taskItemRepository = _serviceProvider.GetService<TaskItemRepository>();

            TaskItem taskItem = new TaskItem()
            {
                ServiceLineID = "72c0746af84111ecae4800ff7aadab79",
                ProjectID = "a201a967f1db402fb81931c1a4a0800d",
                Code = "nmp_ct_xz_rkz_o_ipran",
                DisplayName = "日喀则中国电信IPRAN本地网",
                Addr1 = "西藏",
                Addr2 = "日喀则",
                Addr3 = "PTN/IPRAN巡检工具",
                Addr4 = "6CB3535777015F005C015E028054901A63A55165672C57307F51",
                Region = "ipran",
                SubRegion = "中国电信",
                Source = "SYNC_QA",
                NmpId = Guid.NewGuid().ToString(),
                InspectTime = DateTime.Now
            };

            var result = _taskItemRepository.Insert(taskItem);
            Assert.AreEqual(result.SubRegion, "开封中国联通PON本地网");


        }
        [Test]
        public void XXX()
        {
            //ServiceLineService serviceLineService = _serviceProvider.GetService<ServiceLineService>();
            //RegionService regionService = _serviceProvider.GetService<RegionService>();
            //ProjectService projectService = _serviceProvider.GetService<ProjectService>();
            //TaskItemService taskItemService = _serviceProvider.GetService<TaskItemService>();
            TaskTplService tplService = _serviceProvider.GetService<TaskTplService>();

            var xx = tplService.GetMultiType("zh", "5b172e2d529749d48ddae25ad44b3456");
        }

        /// <summary>
        /// 测试qa网络同步，并写入模板，操作组，审批人等相关关联数据
        /// </summary>
        [Test]
        public void Sync()
        {


            List<NetInfoDto> nets = new List<NetInfoDto>() {
                new NetInfoDto(){
                    nodeName="nmp_cun_hn_kaf_o_ipran",
                    nmpProviderName="日喀则中国电信IPRAN本地网",
                    provinceCH="西藏",
                    cityCH="日喀则",
                    appCode="ipran_xj",
                    displayName="PTN/IPRAN巡检工具",
                    bigDataId="6CB3535777015F005C015E028054901A63A55165672C57307F51",
                    netType="ipran",
                    providerSource="中国电信",
                    id=Guid.NewGuid().ToString(),
                    //inspectTime=DateTime.Now,inspectTimeStr=DateTime.Now.ToString() },
              //  new NetInfoDto(){nodeName="nmp_cun_hn_kaf_o_ipran",nmpProviderName="开封中国联通IPRAN本地网",provinceCH="河南",cityCH="开封",displayName="OLT运维检工具",bigDataId="2CB3535777015F005C015E028054901A63A55165672C57307F51",netType="pon",providerSource="中国联通",id=Guid.NewGuid().ToString(),inspectTime=DateTime.Now },
            } };


            DateTime time = DateTime.Now;
            DateTime startTime = time.AddDays(1 - time.Day).AddMonths(-1).Date;
            DateTime endTime = time.AddDays(1 - time.Day).AddDays(-1).Date.AddHours(23).AddMinutes(59).AddSeconds(59);

            var response = HttpHelper.PostAsync<QaBaseDto<NetInfoDto>>(
               "https://qa.fiberhome.work/api/externally/getInspectNetInfo1"
                , new { startTime = startTime, endTime = endTime }, null
                ).GetAwaiter().GetResult();






            var _cycleService = _serviceProvider.GetService<TaskCycleService>();
            var _regionRepository = _serviceProvider.GetService<RegionRepository>();
            var _projRepo = _serviceProvider.GetService<ProjectRepository>();
            var _tplRepo = _serviceProvider.GetService<TaskTplRepository>();
            var _relTaskTpl = _serviceProvider.GetService<RelTaskItemAndTplRepository>();
            var _itemRepository = _serviceProvider.GetService<TaskItemRepository>();

            var _approverRepository = _serviceProvider.GetService<ApproverRepository>();
            var _contractorRepository = _serviceProvider.GetService<ContractorRepository>();
            var _relContractorUserRepository = _serviceProvider.GetService<RelContractorUserRepository>();
            var _relContractorTaskItemTplRepository = _serviceProvider.GetService<RelContractorTaskItemTplRepository>();
            var _netproviderRepository = _serviceProvider.GetService<NetproviderRepository>();
            var _netUserPositionRepository = _serviceProvider.GetService<NetUserPositionRepository>();

            var _appSolutionRepository = _serviceProvider.GetService<AppSolutionRepository>();
            var _relAppSolutionAndTplRepository = _serviceProvider.GetService<RelAppSolutionAndTplRepository>();

            // _logger.LogInformation($"{startTime}-{endTime}同步QA网络获得{response.data.Count}条数据");
            List<Approver> approvers = new List<Approver>();
            List<Contractor> contractors = new List<Contractor>();
            List<RelContractorUser> relContractorUsers = new List<RelContractorUser>();
            List<RelContractorTaskItemTpl> relTaskTplContractors = new List<RelContractorTaskItemTpl>();
            List<RelTaskItemAndTpl> taskTplRels = new List<RelTaskItemAndTpl>();
            List<TaskItem> exceptTaskItem = new List<TaskItem>();

            Project project = _projRepo.Query(t => t.IsActived && t.ProjectType == ProjectType.ForQA).FirstOrDefault();
            var timeOffset = _regionRepository.GetRegionTimeZoneOffset(project.RegionCode);

            //网络表(codesk与部门)
            var codeSkDepRels = _netproviderRepository.Query(t => t.IsActived).Select(t => new { t.CodeSk, t.DepName }).ToList();
            //部门人员岗位
            var netUserPositons = _netUserPositionRepository.Query(t => t.IsActived).ToList();
            //模板
            var tpls = _tplRepo.Query(t => t.IsActived && t.Published && t.ServiceLineID == project.ServiceLineID && t.ProjectID == project.ID).ToList();
            //网络
            var lstTaskItems = _itemRepository.Query(t => t.IsActived && t.ServiceLineID == project.ServiceLineID && t.ProjectID == project.ID).Select(t => new { t.Region, t.DisplayName, t.Addr3, t.AuthCode }).ToList();
            //app
            var appSolutions = _appSolutionRepository.Query(t => t.IsActived && t.ProjectType == ProjectType.ForQA).Select(t => new { t.ID, t.FirstNode, t.SecondNode, t.ThirdNode }).ToList();
            var appSolutionIds = appSolutions.Select(t => t.ID);
            var relAppAndTpls = _relAppSolutionAndTplRepository.Query(t => t.IsActived && appSolutionIds.Contains(t.ID)).ToList();

            List<string> allAuthCodes = lstTaskItems.Select(t => t.AuthCode).ToList();


            List<TaskItem> taskItems = response.data.Select(t =>
            {
                string authCode = UtilHelper.GetRandomCode("", 6, 6);
                while (allAuthCodes.Contains(authCode))
                {
                    authCode = UtilHelper.GetRandomCode("", 6, 6);
                }

                bool exist = lstTaskItems.Any(x => x.Region == t.netType && x.DisplayName == t.nmpProviderName && x.Addr3 == t.displayName);
                if (!exist)
                {
                    return new TaskItem()
                    {
                        ServiceLineID = project.ServiceLineID,
                        ProjectID = project.ID,
                        Code = string.IsNullOrWhiteSpace(t.nodeName) ? $"{t.nmpProviderName}" : $"{t.nodeName}",// t.nmpProviderName : t.nodeName,
                        DisplayName = t.nmpProviderName,
                        Addr1 = t.provinceCH,
                        Addr2 = t.cityCH,
                        Addr3 = t.displayName,
                        Addr4 = t.appCode,
                        Region = t.netType,
                        SubRegion = t.providerSource,
                        Source = "SYNC_QA",
                        NmpId = t.id,
                        BigDataId = t.bigDataId,
                        InspectTime = DateTime.Parse(t.inspectTimeStr),
                        AuthCode = authCode
                    };
                }
                else
                {
                    return null;
                }
            }).ToList();
            taskItems.RemoveAll(t => t == null);
            List<TaskItem> items = taskItems.GroupBy(t => new { t.BigDataId, t.ProjectID, t.Code, t.ID, t.InspectTime }).Select(t => t.FirstOrDefault()).ToList();
            var lstTaskItem = _itemRepository.Insert(items);

            if (lstTaskItem.Count() > 0)
            {
                foreach (var item in lstTaskItem)
                {
                    bool include = false;
                    //2.1.根据{专业类型：网络类型，模板名：APPCode}找到当前任务的模板集合
                    //var taskTpls = tpls.Where(x => true);// .Where(t => t.TplType.ToString().ToLower().EndsWith(item.Region.ToLower()) && t.AppCodes.ToLower().Contains(item.Addr4));

                    var appSoluIds = appSolutions.Where(t => t.FirstNode.ToLower().Equals(item.Region.ToLower()) && t.ThirdNode.ToLower().Equals(item.Addr4.ToLower())).Select(t => t.ID);
                    var tplIds = relAppAndTpls.Where(t => appSoluIds.Contains(t.AppSolutionID)).Select(t => t.TplID);
                    var taskTpls = tpls.Where(t => tplIds.Contains(t.ID));
                    if (taskTpls.Count() > 0)
                    {
                        //2.2.根据模板是否为周期性模板判断该网络的巡检时间是否在周期模板的周期之内(记录非周期内网络并过滤)
                        foreach (var tpl in taskTpls)
                        {
                            if (tpl.CycleType != CycleType.Once)
                            {
                                List<CycleRecord> cycles = _cycleService.CalCycleRecords(tpl.ModifiedDateTime.Value, timeOffset, tpl.CycleType, tpl.TriggerDay, tpl.TriggerTimes, true, null);
                                string inspectTime = item.InspectTime.Value.ToString("yyyy-MM-dd");
                                bool isInculude = cycles.Any(t => t.Date.Contains(inspectTime));
                                if (isInculude)
                                {
                                    taskTplRels.Add(new RelTaskItemAndTpl() { TplID = tpl.ID, TaskItemID = item.ID });
                                    relTaskTplContractors.Add(new RelContractorTaskItemTpl() { TaskItemID = item.ID, TplID = tpl.ID });
                                    include = true;
                                }
                            }
                            else
                            {
                                taskTplRels.Add(new RelTaskItemAndTpl() { TplID = tpl.ID, TaskItemID = item.ID });
                                relTaskTplContractors.Add(new RelContractorTaskItemTpl() { TaskItemID = item.ID, TplID = tpl.ID });
                                include = true;
                            }
                        }
                        if (!include)
                        {
                            //记录不符合周期模板时间范围内的站点(过滤:待处理zh0327)
                            exceptTaskItem.Add(item);
                        }
                        //2.3.操作组:(网络名为操作组名)
                        contractors.Add(new Contractor() { DisplayName = item.DisplayName, ProjectID = project.ID });
                        //2.4审批人:(维护主管为审批人且仅有一级审批)
                        string depName = codeSkDepRels.FirstOrDefault(t => t.CodeSk == item.BigDataId)?.DepName;
                        var userPositons = netUserPositons.Where(t => t.IsActived && t.DepName == depName && t.UserPosition == EnumUserPosition.MainSupervisor);
                        if (userPositons.Count() > 0)
                        {
                            foreach (var user in userPositons)
                            {
                                approvers.Add(new Approver() { UserEmail = user.UserEmail, UserName = user.NickName, AllowLevel = 1, ProjectID = project.ID });
                            }
                        }
                    }
                    else
                    {
                        //  _logger.LogInformation($"{item.ID}:{item.DisplayName} can not find the template");
                    }
                }

                //任务模板关系
                var relTaskTpls = _relTaskTpl.Insert(taskTplRels);
                //审核人
                var lstApprovers = _approverRepository.Insert(approvers);
                //操作组
                var lstContractor = _contractorRepository.Insert(contractors);

                taskItems.ForEach(t =>
                {
                    string depName = codeSkDepRels.FirstOrDefault(x => x.CodeSk == t.BigDataId)?.DepName;
                    var userPositons = netUserPositons.Where(t => t.IsActived && t.DepName == depName && t.UserPosition == EnumUserPosition.NetChargePerson);
                    string contractorId = contractors.FirstOrDefault(t => t.DisplayName == t.DisplayName)?.ID;

                    if (userPositons.Count() > 0)
                    {
                        foreach (var user in userPositons)
                        {
                            relContractorUsers.Add(new RelContractorUser() { ContractorID = contractorId, UserEmail = user.UserEmail, UserName = user.NickName });
                        }
                    }

                    relTaskTplContractors.Where(x => x.TaskItemID == t.ID).ToList().ForEach(p =>
                    {
                        p.ContractorID = contractorId;
                    });
                });
                //操作组人员
                _relContractorUserRepository.Insert(relContractorUsers);
                //任务，模板，操作组关系
                _relContractorTaskItemTplRepository.Insert(relTaskTplContractors);
            }
        }
        [Test]
        public void Do()
        {

            DateTime time = DateTime.Now;
            DateTime startTime = time.AddDays(1 - time.Day).AddMonths(-1).Date;
            DateTime endTime = time.AddDays(1 - time.Day).AddDays(-1).Date.AddHours(23).AddMinutes(59).AddSeconds(59);


            var _cycleService = _serviceProvider.GetService<TaskCycleService>();
            var _regionRepository = _serviceProvider.GetService<RegionRepository>();
            var _projRepo = _serviceProvider.GetService<ProjectRepository>();
            var _tplRepo = _serviceProvider.GetService<TaskTplRepository>();
            var _relTaskTpl = _serviceProvider.GetService<RelTaskItemAndTplRepository>();
            var _itemRepository = _serviceProvider.GetService<TaskItemRepository>();

            var _approverRepository = _serviceProvider.GetService<ApproverRepository>();
            var _contractorRepository = _serviceProvider.GetService<ContractorRepository>();
            var _relContractorUserRepository = _serviceProvider.GetService<RelContractorUserRepository>();
            var _relContractorTaskItemTplRepository = _serviceProvider.GetService<RelContractorTaskItemTplRepository>();
            var _netproviderRepository = _serviceProvider.GetService<NetproviderRepository>();
            var _netUserPositionRepository = _serviceProvider.GetService<NetUserPositionRepository>();

            var _appSolutionRepository = _serviceProvider.GetService<AppSolutionRepository>();
            var _relAppSolutionAndTplRepository = _serviceProvider.GetService<RelAppSolutionAndTplRepository>();

            var _cycleTaskRecordRepo = _serviceProvider.GetService<CycleTaskRecordRepository>();

            var _statusService = _serviceProvider.GetService<TaskItemStatusService>();

            var _qaWorkOrderRepository = _serviceProvider.GetService<QaWorkOrderRepository>();
            //var _thirdSystemApiService = _serviceProvider.GetService<ThirdSystemApiService>();

            //DateTime time = DateTime.Now;
            //DateTime startTime = time.AddDays(1 - time.Day).AddMonths(-1).Date;
            //DateTime endTime = time.AddDays(1 - time.Day).AddDays(-1).Date.AddHours(23).AddMinutes(59).AddSeconds(59);


            Dictionary<string, string> dicLevelApprover = new Dictionary<string, string>();

            List<Approver> approvers = new List<Approver>();
            List<Contractor> contractors = new List<Contractor>();
            List<RelContractorUser> relContractorUsers = new List<RelContractorUser>();
            List<RelContractorTaskItemTpl> relTaskTplContractors = new List<RelContractorTaskItemTpl>();
            List<RelTaskItemAndTpl> taskTplRels = new List<RelTaskItemAndTpl>();
            List<TaskItem> exceptTaskItem = new List<TaskItem>();
            List<QaWorkOrder> qaWorkOrders = new List<QaWorkOrder>();

            // List<TaskItem> lstTaskItem = _itemRepository.Query(t=>t.IsActived&&t.ProjectID== "93470067f8514dc7907b3f0f821fe814").ToList();

            Project project = _projRepo.Query(t => t.IsActived && t.ProjectType == ProjectType.ForQA).FirstOrDefault();

            //app(获取qa的app)
            var appSolutions = _appSolutionRepository.Query(t => t.IsActived && t.ProjectType == ProjectType.ForQA).Select(t => new { t.ID, t.FirstNode, t.SecondNode, t.ThirdNode }).ToList();
            //app-tpl
            var appSolutionIds = appSolutions.Select(t => t.ID);
            var relAppAndTpls = _relAppSolutionAndTplRepository.Query(t => t.IsActived && appSolutionIds.Contains(t.AppSolutionID)).ToList();
            //网络表(codesk与部门)
            var netproviders = _netproviderRepository.Query(t => t.IsActived).Select(t => new {t.NetProperties, t.CodeSk, t.DepName, t.ChargePersonName, t.ChargePersonEmail }).ToList();
            //部门人员岗位
            var netUserPositons = _netUserPositionRepository.Query(t => t.IsActived).ToList();
            //模板
            var tpls = _tplRepo.Query(t => t.IsActived && t.Published && t.ServiceLineID == project.ServiceLineID && t.ProjectID == project.ID).ToList();
            //网络
            var lstTaskItem = _itemRepository.Query(t => t.IsActived && t.ServiceLineID == project.ServiceLineID && t.ProjectID == project.ID && t.ID == "25ce20c9f08b437c81e50c952220f004").ToList();


            #region 2.写入周期性网络记录信息cycletaskrecords(筛选非周期性内的网络，生成qa工单均需用到cycletaskrecords)
            SetCycleTaskRecord(project.ID).GetAwaiter().GetResult();
            #endregion

            #region 4.写入qa工单,根据周期任务表与网络来生成qa的工单
            //1.获取网络与周期性表
            //2.根据周期性表来生成工单，
            //例如站点A的其中一个模板1，其为周期性为月的任务，执行12次即1年，周期性表中该站点的模板周期数据有12条，则也同样对应生成12条工单
            //3.注意去重
            //4.注意性能测试
            var dicNetAttrMapCodeSk = netproviders.ToDictionary(t => t.CodeSk, t => t.NetProperties);
            var taskItemIds = lstTaskItem.Select(t => t.ID);
            var cycleTaskRecords = _cycleTaskRecordRepo.Query(t => t.IsActived && taskItemIds.Contains(t.TaskItemID)).Select(t => new { t.ID, t.TplID, t.TaskItemID, t.TriggerDay }).OrderBy(t => t.TplID).ToList();
            if (cycleTaskRecords.Count() > 0)
            {
                lstTaskItem.ForEach(t =>
                {
                    var cycleRecords = cycleTaskRecords.Where(p => p.TaskItemID == t.ID).ToList();
                    if (cycleTaskRecords.Count > 0)
                    {
                        cycleRecords.ForEach(p =>
                        {
                            qaWorkOrders.Add(new QaWorkOrder()
                            {
                                ServiceLineID = t.ServiceLineID,
                                ProjectID = t.ProjectID,
                                OrderNo = DateTime.Now.ToString("yyyyMMddHHmmssSSS") + UtilHelper.GetRandomCode("", 6, 6),
                                Code = t.Code,
                                NetName = t.DisplayName,
                                Province = t.Addr1,
                                City = t.Addr2,
                                AppName = t.Addr3,
                                AppCode = t.Addr4,
                                Source = "QA_系统生成",
                                NetType = t.Region,
                                Operator = t.SubRegion,
                                TaskItemId = t.ID,
                                TplId = p.TplID,
                                TriggerDay = p.TriggerDay,
                                BigDataId = t.BigDataId,
                                InspectTime = t.InspectTime,
                                CreatedUser = "sys",
                                NetAttributes = dicNetAttrMapCodeSk.ContainsKey(t.BigDataId) ? dicNetAttrMapCodeSk[t.BigDataId] : ""
                            }) ;
                        });
                    }
                });
            }

            _qaWorkOrderRepository.Insert(qaWorkOrders);
            #endregion

          


           

            #region 3.任务模板关系，操作组，操作组人员，审批人等相关处理
            if (lstTaskItem.Count() > 0)
            {
                foreach (var item in lstTaskItem)
                {
                    bool include = false;
                    //3.1 V2 版本：根据{projectType:2,FirstNode:专业,ThirdNode:appCode}获取app模板方案id集，根据id集合找RelAppSolutionAndTplRepository中的模板id集,在根据模板id集找到对应模板
                    var appSoluIds = appSolutions.Where(t => t.FirstNode.ToLower().Equals(item.Region.ToLower()) && t.ThirdNode.ToLower().Equals(item.Addr4.ToLower())).Select(t => t.ID);
                    var tplIds = relAppAndTpls.Where(t => appSoluIds.Contains(t.AppSolutionID)).Select(t => t.TplID);
                    var taskTpls = tpls.Where(t => tplIds.Contains(t.ID));
                    if (taskTpls.Count() > 0)
                    {
                        foreach (var tpl in taskTpls)
                        {
                            #region MyRegion
                            //if (tpl.CycleType != CycleType.Once)
                            //{
                            //    List<CycleRecord> cycles = _cycleService.CalCycleRecords(tpl.ModifiedDateTime.Value, timeOffset, tpl.CycleType, tpl.TriggerDay, tpl.TriggerTimes, true, null);
                            //    string inspectTime = item.InspectTime.Value.Date.ToString();
                            //    //网络巡检时间是否包含的周期性任务的周期时间范围内
                            //    bool isInculude = cycles.Any(t => t.Date.Contains(inspectTime));
                            //    if (isInculude)
                            //    {
                            //        taskTplRels.Add(new RelTaskItemAndTpl() { TplID = tpl.ID, TaskItemID = item.ID });
                            //        relTaskTplContractors.Add(new RelContractorTaskItemTpl() { TaskItemID = item.ID, TplID = tpl.ID });
                            //        include = true;
                            //    }
                            //}
                            //else
                            //{
                            //    taskTplRels.Add(new RelTaskItemAndTpl() { TplID = tpl.ID, TaskItemID = item.ID });
                            //    relTaskTplContractors.Add(new RelContractorTaskItemTpl() { TaskItemID = item.ID, TplID = tpl.ID });
                            //    include = true;
                            //} 
                            #endregion

                            taskTplRels.Add(new RelTaskItemAndTpl() { TplID = tpl.ID, TaskItemID = item.ID });
                            relTaskTplContractors.Add(new RelContractorTaskItemTpl() { TaskItemID = item.ID, TplID = tpl.ID });
                            include = true;

                        }
                        if (!include)
                        {
                            //记录不符合周期模板时间范围内的站点(过滤:待处理zh0327)
                            exceptTaskItem.Add(item);
                        }


                        //构建操作组
                        contractors.Add(new Contractor() { Code = item.BigDataId, DisplayName = item.DisplayName, ProjectID = project.ID });

                        #region 审核人构造 审核人(维护主管)
                        string depName = netproviders.FirstOrDefault(t => t.CodeSk == item.BigDataId)?.DepName;
                        var userPositons = netUserPositons.Where(t => t.IsActived && t.DepName == depName && t.UserPosition == EnumUserPosition.MainSupervisor);
                        if (userPositons.Count() > 0)
                        {
                            //站点模板关系表中：一，二级别审批人为维护主管，多个维护主管取第一个
                            var leverApprover = userPositons.FirstOrDefault();
                            dicLevelApprover.Add(item.ID, leverApprover.UserEmail);

                            foreach (var user in userPositons)
                            {
                                approvers.Add(new Approver() { UserEmail = user.UserEmail, UserName = user.NickName, AllowLevel = 1, ProjectID = project.ID });
                            }
                        }
                        #endregion
                    }
                    else
                    {
                        //   _logger.LogInformation($"the site :[{item.ID}:{item.DisplayName}] can not find the template");
                    }
                }

                var lstContractor = _contractorRepository.Insert(contractors);
                var lstApprovers = _approverRepository.Insert(approvers);

                //站点模板关系表：赋值一，二等级审批人(一二级设置为相同的审批人)
                taskTplRels.ForEach(t =>
                {
                    string approverEmail = dicLevelApprover[t.TaskItemID];
                    string approverId = approvers.FirstOrDefault(t => t.UserEmail == approverEmail)?.ID;
                    t.Level1ApproverID = approverId;
                    t.Level2ApproverID = approverId;
                });
                var relTaskTpls = _relTaskTpl.Insert(taskTplRels);

                lstTaskItem.ForEach(async t =>
                {
                    string contractorId = contractors.FirstOrDefault(x => x.Code == t.BigDataId && x.ProjectID == t.ProjectID)?.ID;

                    #region 操作组人员构造
                    //3.1操作组人员：网络负责人以及拥有网络权限的人员（调用sunyibing接口获取，requestparams:{"codesk":"23432423432"}）
                    var ChargePerson = netproviders.FirstOrDefault(x => x.CodeSk == t.BigDataId);
                    if (ChargePerson != null)
                    {
                        relContractorUsers.Add(new RelContractorUser() { ContractorID = contractorId, UserEmail = ChargePerson.ChargePersonEmail, UserName = ChargePerson.ChargePersonName, UserType = EnumContractorUserType.Operator });
                    }
                    //具有网络权限的操作组人员从sunyibing的接口获取(注意超时)
                    //var providerUserList = await _thirdSystemApiService.GetNetProviderUserList(t.BigDataId);
                    //if (providerUserList.Data.Count() > 0)
                    //{
                    //    providerUserList.Data.ForEach(p =>
                    //    {
                    //        relContractorUsers.Add(new RelContractorUser() { ContractorID = contractorId, UserEmail = p.Email, UserName = p.NickName, UserType = EnumContractorUserType.Viewer });
                    //    });
                    //}
                    #endregion

                    #region 网络/模板和供应商的关系表
                    relTaskTplContractors.Where(x => x.TaskItemID == t.ID).ToList().ForEach(p =>
                    {
                        p.ContractorID = contractorId;
                    });
                    #endregion
                });

                //注意去重
                relContractorUsers = relContractorUsers.GroupBy(t => new { t.UserEmail, t.UserName }).Select(t => t.FirstOrDefault()).ToList();
                _relContractorUserRepository.Insert(relContractorUsers);

                _relContractorTaskItemTplRepository.Insert(relTaskTplContractors);

                //3.2.删除非周期性范围内的站点(判断是否存在关系)--delete exceptTaskItem
            }
            #endregion

            #region 4.写入qa工单,根据周期任务表与网络来生成qa的工单
            ////1.获取网络与周期性表
            ////2.根据周期性表来生成工单，
            ////例如站点A的其中一个模板1，其为周期性为月的任务，执行12次即1年，周期性表中该站点的模板周期数据有12条，则也同样对应生成12条工单
            ////3.注意去重
            ////4.注意性能测试
            //var taskItemIds = lstTaskItem.Select(t => t.ID);
            //List<CycleTaskRecord> cycleTaskRecords = _cycleTaskRecordRepo.Query(t => t.IsActived && taskItemIds.Contains(t.TaskItemID)).OrderBy(t => t.TplID).ToList();
            //if (cycleTaskRecords.Count() > 0)
            //{
            //    lstTaskItem.ForEach(t =>
            //    {
            //        var cycleRecords = cycleTaskRecords.Where(p => p.TaskItemID == t.ID).ToList();
            //        if (cycleTaskRecords.Count > 0)
            //        {
            //            cycleRecords.ForEach(p =>
            //            {
            //                qaWorkOrders.Add(new QaWorkOrder()
            //                {
            //                    ServiceLineID = t.ServiceLineID,
            //                    ProjectID = t.ProjectID,
            //                    OrderNo = "",
            //                    Code = t.Code,
            //                    NetName = t.DisplayName,
            //                    Province = t.Addr1,
            //                    City = t.Addr2,
            //                    AppName = t.Addr3,
            //                    AppCode = t.Addr4,
            //                    Source = "SYNC_QA_Init",
            //                    NetType = t.Region,
            //                    Operator = t.SubRegion,
            //                    TaskItemId = t.ID,
            //                    TplId = p.TplID,
            //                    TriggerDay = p.TriggerDay,
            //                    BigDataId = t.BigDataId,
            //                    InspectTime = t.InspectTime,
            //                });
            //            });
            //        }
            //    });
            //}
            //_qaWorkOrderRepository.Insert(qaWorkOrders);
            #endregion

        }

        public async Task SetCycleTaskRecord(string projectID)
        {
            //string projectID = "93470067f8514dc7907b3f0f821fe814";
            var _cycleService = _serviceProvider.GetService<TaskCycleService>();
            var _regionRepository = _serviceProvider.GetService<RegionRepository>();
            var _projRepo = _serviceProvider.GetService<ProjectRepository>();
            var _tplRepo = _serviceProvider.GetService<TaskTplRepository>();
            var _relTaskTpl = _serviceProvider.GetService<RelTaskItemAndTplRepository>();
            var _itemRepository = _serviceProvider.GetService<TaskItemRepository>();

            var _approverRepository = _serviceProvider.GetService<ApproverRepository>();
            var _contractorRepository = _serviceProvider.GetService<ContractorRepository>();
            var _relContractorUserRepository = _serviceProvider.GetService<RelContractorUserRepository>();
            var _relContractorTaskItemTplRepository = _serviceProvider.GetService<RelContractorTaskItemTplRepository>();
            var _netproviderRepository = _serviceProvider.GetService<NetproviderRepository>();
            var _netUserPositionRepository = _serviceProvider.GetService<NetUserPositionRepository>();

            var _appSolutionRepository = _serviceProvider.GetService<AppSolutionRepository>();
            var _relAppSolutionAndTplRepository = _serviceProvider.GetService<RelAppSolutionAndTplRepository>();

            var _cycleTaskRecordRepo = _serviceProvider.GetService<CycleTaskRecordRepository>();

            var _statusService = _serviceProvider.GetService<TaskItemStatusService>();

            var utcHour = DateTime.UtcNow.Hour;
            var offset = 0;
            if (utcHour > 12)
            {
                offset = 24 - utcHour;
            }
            else
            {
                offset = -utcHour;
            }
#if DEBUG
            offset = 8;
#endif


            System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();

            sw.Start();
            var flag = true;
            var msg = string.Empty;
            try
            {
                var proj = _projRepo.Find(projectID);

                if (proj != null && proj.TaskType == TaskType.Cycle)
                {
                    var timeOffset = _regionRepository.GetRegionTimeZoneOffset(proj.RegionCode);

                    var taskitemIDs = _itemRepository.Query(x => x.IsActived && x.ProjectID == proj.ID).Select(x => x.ID).ToList();
                    var tpls = _tplRepo.Query(x => x.ProjectID == projectID && x.Published && x.IsActived && x.CycleType != CycleType.Once).ToList();
                    var tplIDs = tpls.Select(x => x.ID);
                    var rels = _relTaskTpl.Query(x => taskitemIDs.Contains(x.TaskItemID) && tplIDs.Contains(x.TplID)).Select(x => new { x.TplID, x.TaskItemID, x.CreatedDateTime }).ToList();
                    foreach (var tpl in tpls)
                    {
                        foreach (var taskitemID in taskitemIDs)
                        {
                            var rel = rels.FirstOrDefault(x => x.TaskItemID == taskitemID && x.TplID == tpl.ID);
                            if (rel != null)
                            {
                                var boundDate = rel.CreatedDateTime.Value;
                                var dates = _cycleService.CalCycleRecords(boundDate, timeOffset, tpl.CycleType, tpl.TriggerDay, tpl.TriggerTimes,true).Select(x => x.Date).ToList();
                                if (dates.Count > 0)
                                {
                                    var cycleExistDates = _cycleTaskRecordRepo.Query(x => x.TaskItemID == taskitemID && x.TplID == tpl.ID).Select(x => x.TriggerDay).ToList();
                                    dates = dates.Where(x => !cycleExistDates.Contains(x)).ToList();
                                    if (dates.Count > 0)
                                    {
                                        var cycles = new List<CycleTaskRecord>();
                                        dates.ForEach(x =>
                                        {
                                            cycles.Add(new CycleTaskRecord
                                            {
                                                TplID = tpl.ID,
                                                TaskItemID = taskitemID,
                                                TriggerDay = x
                                            });
                                        });
                                        _cycleTaskRecordRepo.Insert(cycles);
                                    }
                                }
                            }
                        }
                        _statusService.UpdateTplTotalCount(tpl.ID);
                    }

                }
            }
            catch (Exception ex)
            {
                flag = false;
                msg = ex.Message;
            }
            sw.Stop();
            // _logger.LogInformation($"CycleRecordJob，项目名称:[{projectID}] 耗时{sw.Elapsed.TotalSeconds}秒，结果:{flag}，原因:{msg}");

            await Task.CompletedTask;
        }
        //[Test]
        //public void TestCalculateStatus()
        //{
        //    var _taskItemRepository = _serviceProvider.GetService<TaskItemRepository>();
        //    var taskItem = _taskItemRepository.Find("a0e8fae6f7ef4f6a8d89a8e59930f9e4");
        //    var tplID = "9cf4b2e198e64764988b581fbd36106b";
        //    var service = _serviceProvider.GetService<TaskItemStatusService>();
        //    service.UpdateRelWholeStatus(taskItem.ID, tplID);
        //}

        //[Test]
        //public void TestServiceLine()
        //{
        //    ServiceLineService service = _serviceProvider.GetService<ServiceLineService>();
        //    service.DeleteAll();
        //    var all = service.FindAll();
        //    var entities = service.Transfer<ServiceLineDto, ServiceLine>(all);
        //    service.Delete(entities, true);
        //    var serviceLine = service.Create();
        //    serviceLine.Code = "test";
        //    service.Insert(serviceLine);
        //    all = service.FindAll();
        //    Assert.AreEqual(all.Count, 1);
        //    Assert.AreEqual(all[0].Code, "test");
        //}

        //[Test]
        //public void TestQueryProjects()
        //{
        //    ProjectService projectService = _serviceProvider.GetService<ProjectService>();
        //    //var list1 = projectService.QueryByServiceLineAndRegion(null, "101012");
        //    //var list2 = projectService.QueryByServiceLineAndRegion(null, "101011");
        //}

        //[Test]
        //public void TestServiceLineDeleteNotIndeed()
        //{
        //    ServiceLineService service = _serviceProvider.GetService<ServiceLineService>();
        //    service.DeleteAll();
        //    var all = service.FindAll();
        //    var entities = service.Transfer<ServiceLineDto, ServiceLine>(all);
        //    service.Delete(entities, false);
        //    var serviceLine = service.Create();
        //    serviceLine.Code = "test";
        //    service.Insert(serviceLine);
        //    all = service.FindAll();
        //    Assert.AreEqual(all.Count, 1);
        //    Assert.AreEqual(all[0].Code, "test");

        //    service.Delete(all[0].ID, false);
        //}

        //[Test]
        //public void TestLang()
        //{
        //    ServiceLineService serviceLineService = _serviceProvider.GetService<ServiceLineService>();
        //    LangService lanService = _serviceProvider.GetService<LangService>();
        //    lanService.DeleteAll();
        //    serviceLineService.DeleteAll();

        //    var serviceLine = serviceLineService.Create();
        //    serviceLine.Code = "test";
        //    serviceLineService.Insert(serviceLine);
        //    var all = serviceLineService.FindAll();
        //    Assert.AreEqual(all.Count, 1);
        //    Assert.AreEqual(all[0].Code, "test");

        //    var item = serviceLineService.Find(all[0].ID);
        //    Assert.IsNotNull(item);
        //    var code = item.Code;
        //    lanService.SetKeyTitle(LangUsedForConsts.ServiceLine, code, "cn", "����");

        //    var list = serviceLineService.FindAll();
        //    list.ForEach(x =>
        //    {
        //        x.DisplayName = lanService.GetTitle(LangUsedForConsts.ServiceLine, x.Code, "cn") ?? x.Code;
        //    });

        //    Assert.AreEqual(list[0].DisplayName, "����");
        //}

        //[Test]
        //public void TestRegion()
        //{
        //    RegionService regionService = _serviceProvider.GetService<RegionService>();
        //    regionService.DeleteAll();
        //    LangService lanService = _serviceProvider.GetService<LangService>();
        //    lanService.DeleteAll();
        //    var region = regionService.Create();
        //    region.Code = "001";
        //    regionService.Insert(region);
        //    var region1 = regionService.Create();
        //    region1.Code = "001001";
        //    regionService.Insert(region1);
        //    var region2 = regionService.Create();
        //    region2.Code = "001002";
        //    regionService.Insert(region2);
        //    var region3 = regionService.Create();
        //    region3.Code = "002";
        //    regionService.Insert(region3);

        //    lanService.SetKeyTitle(LangUsedForConsts.Region, "001", "zh", "�й�");
        //    lanService.SetKeyTitle(LangUsedForConsts.Region, "002", "zh", "ӡ��");
        //    lanService.SetKeyTitle(LangUsedForConsts.Region, "001001", "zh", "����");
        //    lanService.SetKeyTitle(LangUsedForConsts.Region, "001002", "zh", "����");

        //    var all = regionService.GetChildrenRegions(recur: true);

        //    region.DisplayName = lanService.GetTitle(LangUsedForConsts.Region, region.Code, "cn") ?? region.Code;
        //    region1.DisplayName = lanService.GetTitle(LangUsedForConsts.Region, region1.Code, "cn") ?? region.Code;
        //    region2.DisplayName = lanService.GetTitle(LangUsedForConsts.Region, region2.Code, "cn") ?? region.Code;
        //    region3.DisplayName = lanService.GetTitle(LangUsedForConsts.Region, region3.Code, "cn") ?? region.Code;
        //    Assert.AreEqual(region.DisplayName, "�й�");
        //    Assert.AreEqual(region1.DisplayName, "����");
        //    Assert.AreEqual(region2.DisplayName, "����");
        //    Assert.AreEqual(region2.DisplayName, "ӡ��");
        //}

        //[Test]
        //public void TestProject()
        //{
        //    ServiceLineService serviceLineService = _serviceProvider.GetService<ServiceLineService>();
        //    RegionService regionService = _serviceProvider.GetService<RegionService>();
        //    ProjectService projectService = _serviceProvider.GetService<ProjectService>();
        //    serviceLineService.DeleteAll();
        //    regionService.DeleteAll();
        //    projectService.DeleteAll();

        //    var serviceLine = serviceLineService.Create();
        //    serviceLine.Code = "test";
        //    serviceLine = serviceLineService.Insert(serviceLine);

        //    var region = regionService.Create();
        //    region.Code = "001";
        //    regionService.Insert(region);

        //    var project = projectService.Create();
        //    project.ServiceLineID = serviceLine.ID;
        //    project.RegionCode = region.Code;
        //    project.DisplayName = "������Ŀ";

        //    projectService.Insert(project);

        //    var all = projectService.FindAll();
        //    Assert.AreEqual(all.Count, 1);
        //    Assert.AreEqual(all[0].DisplayName, "������Ŀ");
        //}

        //[Test]
        //public void TestTaskItem()
        //{
        //    ServiceLineService serviceLineService = _serviceProvider.GetService<ServiceLineService>();
        //    RegionService regionService = _serviceProvider.GetService<RegionService>();
        //    ProjectService projectService = _serviceProvider.GetService<ProjectService>();
        //    TaskItemService taskItemService = _serviceProvider.GetService<TaskItemService>();

        //    serviceLineService.DeleteAll();
        //    regionService.DeleteAll();
        //    projectService.DeleteAll();
        //    taskItemService.DeleteAll();

        //    var serviceLine = serviceLineService.Create();
        //    serviceLine.Code = "test";
        //    serviceLine = serviceLineService.Insert(serviceLine);

        //    var region = regionService.Create();
        //    region.Code = "001";
        //    regionService.Insert(region);

        //    var project = projectService.Create();
        //    project.ServiceLineID = serviceLine.ID;
        //    project.RegionCode = region.Code;
        //    project.DisplayName = "������Ŀ";

        //    projectService.Insert(project);

        //    var all = projectService.FindAll();
        //    Assert.AreEqual(all.Count, 1);
        //    Assert.AreEqual(all[0].DisplayName, "������Ŀ");

        //    var taskItem = taskItemService.Create();
        //    taskItem.Code = "TEST1";
        //    taskItem.DisplayName = "�人������������޹�˾";
        //    taskItem.ProjectID = all[0].ID;
        //    taskItem.ServiceLineID = serviceLine.ID;
        //    taskItemService.Insert(taskItem);

        //    var pageData = taskItemService.QueryTaskItem(null, null, null, DateTime.UtcNow.AddHours(1), new PageCriteria { PageNumber = 1, PageSize = 10 }, out int totalCount);
        //    Assert.AreEqual(pageData.Count, 0);
        //    Assert.AreEqual(totalCount, 0);

        //    pageData = taskItemService.QueryTaskItem(null, null, null, DateTime.UtcNow.AddHours(-1), new PageCriteria { PageNumber = 1, PageSize = 10 }, out totalCount);
        //    Assert.AreEqual(pageData.Count, 1);
        //    Assert.AreEqual(totalCount, 1);
        //}

        //[Test]
        //public void TestProjectDuplicate()
        //{
        //    ServiceLineService serviceLineService = _serviceProvider.GetService<ServiceLineService>();
        //    RegionService regionService = _serviceProvider.GetService<RegionService>();
        //    ProjectService projectService = _serviceProvider.GetService<ProjectService>();
        //    TaskItemService taskItemService = _serviceProvider.GetService<TaskItemService>();
        //    TaskTplService tplService = _serviceProvider.GetService<TaskTplService>();

        //    serviceLineService.DeleteAll();
        //    regionService.DeleteAll();
        //    projectService.DeleteAll();
        //    taskItemService.DeleteAll();
        //    tplService.DeleteAll();

        //    var serviceLine = serviceLineService.Create();
        //    serviceLine.Code = "test";
        //    serviceLine = serviceLineService.Insert(serviceLine);

        //    var region = regionService.Create();
        //    region.Code = "001";
        //    regionService.Insert(region);

        //    try
        //    {
        //        var project1 = projectService.Create();
        //        project1.ServiceLineID = serviceLine.ID;
        //        project1.RegionCode = region.Code;
        //        project1.DisplayName = "������Ŀ";
        //        project1.Code = "������ĿCode";
        //        projectService.Insert(project1);

        //        var project2 = projectService.Create();
        //        project2.ServiceLineID = serviceLine.ID;
        //        project2.RegionCode = region.Code;
        //        project2.DisplayName = "������Ŀ";
        //        project2.Code = "������ĿCode";
        //        projectService.Insert(project2);
        //    }
        //    catch (Exception ex)
        //    {
        //        if (ex.InnerException != null)
        //        {
        //            if (ex.InnerException is MySqlException)
        //            {
        //                var err = ex.InnerException as MySqlException;
        //                if (err.ErrorCode == MySqlErrorCode.DuplicateKeyEntry)
        //                {

        //                }
        //            }
        //        }
        //    }
        //}

        //[Test]
        //public void TestMD5()
        //{
        //    var filepath = @"C:\Users\<USER>\Desktop\02f2c38a89c7baf36ee66843e0277faa_mark.jpeg";
        //    var md5 = FileHelper.GetFileMD5Hash(filepath);
        //}

        //[Test]
        //public void TestTpl()
        //{
        //    ServiceLineService serviceLineService = _serviceProvider.GetService<ServiceLineService>();
        //    RegionService regionService = _serviceProvider.GetService<RegionService>();
        //    ProjectService projectService = _serviceProvider.GetService<ProjectService>();
        //    TaskItemService taskItemService = _serviceProvider.GetService<TaskItemService>();
        //    TaskTplService tplService = _serviceProvider.GetService<TaskTplService>();

        //    serviceLineService.DeleteAll();
        //    regionService.DeleteAll();
        //    projectService.DeleteAll();
        //    taskItemService.DeleteAll();
        //    tplService.DeleteAll();

        //    var serviceLine = serviceLineService.Create();
        //    serviceLine.Code = "test";
        //    serviceLine = serviceLineService.Insert(serviceLine);

        //    var region = regionService.Create();
        //    region.Code = "001";
        //    regionService.Insert(region);

        //    var project = projectService.Create();
        //    project.ServiceLineID = serviceLine.ID;
        //    project.RegionCode = region.Code;
        //    project.DisplayName = "������Ŀ";
        //    project.Code = "������ĿCode";
        //    projectService.Insert(project);

        //    var taskItem = taskItemService.Create();
        //    taskItem.Code = "TEST1";
        //    taskItem.DisplayName = "�人������������޹�˾";
        //    taskItem.ProjectID = project.ID;
        //    taskItem.ServiceLineID = serviceLine.ID;
        //    taskItemService.Insert(taskItem);

        //    var tpl = tplService.Create();

        //    tpl.DisplayName = "����ģ��";
        //    tpl.ProjectID = project.ID;
        //    tpl.ServiceLineID = serviceLine.ID;
        //    tpl.RegionCode = region.Code;

        //    tpl = tplService.Insert(tpl);

        //    tpl = tplService.Create();

        //    tpl.DisplayName = "����ģ��";
        //    tpl.ProjectID = project.ID;
        //    tpl.ServiceLineID = serviceLine.ID;
        //    tpl.RegionCode = region.Code;

        //    try
        //    {
        //        tpl = tplService.Insert(tpl);
        //    }
        //    catch (Exception ex)
        //    {

        //    }

        //    Assert.IsNotNull(tpl.ID);

        //    tplService.AddOrUpdateTpl(tpl);

        //    var all = tplService.FindAll();
        //    Assert.AreEqual(all.Count, 1);

        //    tpl = all[0];

        //    var group1 = tplService.AddOrUpdateTplGroup(new TaskGroupTplDto
        //    {
        //        DisplayName = "����1",
        //        Order = 1,
        //        TplID = tpl.ID
        //    });


        //    var group2 = tplService.AddOrUpdateTplGroup(new TaskGroupTplDto
        //    {
        //        DisplayName = "����2",
        //        Order = 2,
        //        TplID = tpl.ID
        //    });

        //    for (int i = 1; i <= 20; i++)
        //    {
        //        tplService.AddOrUpdateTplStep(new TaskStepTplDto
        //        {
        //            TplID = tpl.ID,
        //            AllowMany = true,
        //            CountMaxLimit = 5,
        //            IsRequired = i % 2 == 0,
        //            Order = i,
        //            SupportFileTypes = StepFileType.NoLimit,
        //            DataType = StepDataType.File,
        //            AllowSelect = true,
        //            DisplayName = "����" + i,
        //            GroupID = group1.ID
        //        });
        //    }

        //    for (int i = 1; i <= 10; i++)
        //    {
        //        tplService.AddOrUpdateTplStep(new TaskStepTplDto
        //        {
        //            TplID = tpl.ID,
        //            AllowMany = true,
        //            CountMaxLimit = 3,
        //            IsRequired = i % 2 == 0,
        //            Order = i,
        //            SupportFileTypes = StepFileType.Image,
        //            DataType = StepDataType.File,
        //            AllowSelect = true,
        //            DisplayName = "����" + i,
        //            GroupID = group2.ID
        //        });
        //    }

        //    var steps = tplService.GetStepsByGroupID(group1.ID, false);

        //    Assert.AreEqual(steps.Count, 20);

        //    var step = steps[0];
        //    step.AllowMany = false;
        //    tplService.AddOrUpdateTplStep(step);

        //    tplService.PublishGroup(tpl.ID);
        //    tplService.PublishStep(tpl.ID);
        //    tplService.PublishTpl(tpl.ID);

        //    bool b = tplService.IsTplPublished(tpl.ID);
        //    Assert.IsTrue(b);
        //}

        //[Test]
        //public void TestCopyTpl()
        //{
        //    ServiceLineService serviceLineService = _serviceProvider.GetService<ServiceLineService>();
        //    RegionService regionService = _serviceProvider.GetService<RegionService>();
        //    ProjectService projectService = _serviceProvider.GetService<ProjectService>();
        //    TaskItemService taskItemService = _serviceProvider.GetService<TaskItemService>();
        //    TaskTplService tplService = _serviceProvider.GetService<TaskTplService>();

        //    serviceLineService.DeleteAll();
        //    regionService.DeleteAll();
        //    projectService.DeleteAll();
        //    taskItemService.DeleteAll();
        //    tplService.DeleteAll();

        //    var serviceLine = serviceLineService.Create();
        //    serviceLine.Code = "test";
        //    serviceLine = serviceLineService.Insert(serviceLine);

        //    var region = regionService.Create();
        //    region.Code = "001";
        //    regionService.Insert(region);

        //    var project = projectService.Create();
        //    project.ServiceLineID = serviceLine.ID;
        //    project.RegionCode = region.Code;
        //    project.DisplayName = "������Ŀ";

        //    projectService.Insert(project);

        //    var taskItem = taskItemService.Create();
        //    taskItem.Code = "TEST1";
        //    taskItem.DisplayName = "�人������������޹�˾";
        //    taskItem.ProjectID = project.ID;
        //    taskItem.ServiceLineID = serviceLine.ID;
        //    taskItemService.Insert(taskItem);

        //    var tpl = tplService.Create();

        //    tpl.DisplayName = "����ģ��";
        //    tpl.ProjectID = project.ID;
        //    tpl.ServiceLineID = serviceLine.ID;
        //    tpl.RegionCode = region.Code;

        //    tpl = tplService.Insert(tpl);

        //    Assert.IsNotNull(tpl.ID);

        //    tplService.AddOrUpdateTpl(tpl);

        //    var all = tplService.FindAll();
        //    Assert.AreEqual(all.Count, 1);

        //    tpl = all[0];

        //    var group1 = tplService.AddOrUpdateTplGroup(new TaskGroupTplDto
        //    {
        //        DisplayName = "����1",
        //        Order = 1,
        //        TplID = tpl.ID
        //    });

        //    var group2 = tplService.AddOrUpdateTplGroup(new TaskGroupTplDto
        //    {
        //        DisplayName = "����2",
        //        Order = 2,
        //        TplID = tpl.ID
        //    });

        //    for (int i = 1; i <= 20; i++)
        //    {
        //        tplService.AddOrUpdateTplStep(new TaskStepTplDto
        //        {
        //            TplID = tpl.ID,
        //            AllowMany = true,
        //            CountMaxLimit = 5,
        //            IsRequired = i % 2 == 0,
        //            Order = i,
        //            SupportFileTypes = StepFileType.NoLimit,
        //            DataType = StepDataType.File,
        //            AllowSelect = true,
        //            DisplayName = "����" + i,
        //            GroupID = group1.ID
        //        });
        //    }

        //    for (int i = 1; i <= 10; i++)
        //    {
        //        tplService.AddOrUpdateTplStep(new TaskStepTplDto
        //        {
        //            TplID = tpl.ID,
        //            AllowMany = true,
        //            CountMaxLimit = 3,
        //            IsRequired = i % 2 == 0,
        //            Order = i,
        //            SupportFileTypes = StepFileType.Image,
        //            DataType = StepDataType.File,
        //            AllowSelect = true,
        //            DisplayName = "����" + i,
        //            GroupID = group2.ID
        //        });
        //    }

        //    var steps = tplService.GetStepsByGroupID(group1.ID, false);

        //    Assert.AreEqual(steps.Count, 20);

        //    var step = steps[0];
        //    step.AllowMany = false;
        //    tplService.AddOrUpdateTplStep(step);

        //    tplService.PublishGroup(tpl.ID);
        //    tplService.PublishStep(tpl.ID);
        //    tplService.PublishTpl(tpl.ID);

        //    bool b = tplService.IsTplPublished(tpl.ID);
        //    Assert.IsTrue(b);

        //    var newTplName = tplService.CopyTpl(tpl.ID, "������ģ��");
        //    var groups = tplService.GetTplGroupsByTplDisplayName(project.ID, newTplName, false);
        //    Assert.AreEqual(groups.Count, 2);
        //}

        //[Test]
        //public void TestTplGroups()
        //{
        //    ServiceLineService serviceLineService = _serviceProvider.GetService<ServiceLineService>();
        //    RegionService regionService = _serviceProvider.GetService<RegionService>();
        //    ProjectService projectService = _serviceProvider.GetService<ProjectService>();
        //    TaskItemService taskItemService = _serviceProvider.GetService<TaskItemService>();
        //    TaskTplService tplService = _serviceProvider.GetService<TaskTplService>();

        //    serviceLineService.DeleteAll();
        //    regionService.DeleteAll();
        //    projectService.DeleteAll();
        //    taskItemService.DeleteAll();
        //    tplService.DeleteAll();

        //    var serviceLine = serviceLineService.Create();
        //    serviceLine.Code = "test";
        //    serviceLine = serviceLineService.Insert(serviceLine);

        //    var region = regionService.Create();
        //    region.Code = "001";
        //    regionService.Insert(region);

        //    var project = projectService.Create();
        //    project.ServiceLineID = serviceLine.ID;
        //    project.RegionCode = region.Code;
        //    project.DisplayName = "������Ŀ";

        //    projectService.Insert(project);

        //    var taskItem = taskItemService.Create();
        //    taskItem.Code = "TEST1";
        //    taskItem.DisplayName = "�人������������޹�˾";
        //    taskItem.ProjectID = project.ID;
        //    taskItem.ServiceLineID = serviceLine.ID;
        //    taskItemService.Insert(taskItem);

        //    var tpl = tplService.Create();

        //    tpl.DisplayName = "����ģ��";
        //    tpl.ProjectID = project.ID;
        //    tpl.ServiceLineID = serviceLine.ID;
        //    tpl.RegionCode = region.Code;

        //    tpl = tplService.Insert(tpl);

        //    Assert.IsNotNull(tpl.ID);

        //    tplService.AddOrUpdateTpl(tpl);

        //    var all = tplService.FindAll();
        //    Assert.AreEqual(all.Count, 1);

        //    tpl = all[0];

        //    var group1 = tplService.AddOrUpdateTplGroup(new TaskGroupTplDto
        //    {
        //        DisplayName = "����2",
        //        Order = 1,
        //        TplID = tpl.ID
        //    });

        //    var group2 = tplService.AddOrUpdateTplGroup(new TaskGroupTplDto
        //    {
        //        DisplayName = "����3",
        //        Order = 2,
        //        TplID = tpl.ID
        //    });

        //    var group3 = tplService.AddOrUpdateTplGroup(new TaskGroupTplDto
        //    {
        //        DisplayName = "����1",
        //        Order = 1,
        //        TplID = tpl.ID
        //    });

        //    var groups = tplService.GetTplGroups(tpl.ID, false).OrderBy(x => x.Order).ToList();
        //    Assert.AreEqual(groups.Count, 3);
        //    Assert.AreEqual(groups[2].Order, 3);
        //}

        //[Test]
        //public async Task TestTplSample()
        //{
        //    ServiceLineService serviceLineService = _serviceProvider.GetService<ServiceLineService>();
        //    RegionService regionService = _serviceProvider.GetService<RegionService>();
        //    ProjectService projectService = _serviceProvider.GetService<ProjectService>();
        //    TaskItemService taskItemService = _serviceProvider.GetService<TaskItemService>();
        //    TaskTplService tplService = _serviceProvider.GetService<TaskTplService>();
        //    TaskStepValueSampleService sampleService = _serviceProvider.GetService<TaskStepValueSampleService>();
        //    serviceLineService.DeleteAll();
        //    regionService.DeleteAll();
        //    projectService.DeleteAll();
        //    taskItemService.DeleteAll();
        //    tplService.DeleteAll();
        //    sampleService.DeleteAll();

        //    var serviceLine = serviceLineService.Create();
        //    serviceLine.Code = "test";
        //    serviceLine = serviceLineService.Insert(serviceLine);

        //    var region = regionService.Create();
        //    region.Code = "001";
        //    regionService.Insert(region);

        //    var project = projectService.Create();
        //    project.ServiceLineID = serviceLine.ID;
        //    project.RegionCode = region.Code;
        //    project.DisplayName = "������Ŀ";

        //    projectService.Insert(project);

        //    var taskItem = taskItemService.Create();
        //    taskItem.Code = "TEST1";
        //    taskItem.DisplayName = "�人������������޹�˾";
        //    taskItem.ProjectID = project.ID;
        //    taskItem.ServiceLineID = serviceLine.ID;
        //    taskItemService.Insert(taskItem);

        //    var tpl = tplService.Create();

        //    tpl.DisplayName = "����ģ��";
        //    tpl.ProjectID = project.ID;
        //    tpl.ServiceLineID = serviceLine.ID;
        //    tpl.RegionCode = region.Code;

        //    tpl = tplService.Insert(tpl);

        //    Assert.IsNotNull(tpl.ID);

        //    tplService.AddOrUpdateTpl(tpl);

        //    var all = tplService.FindAll();
        //    Assert.AreEqual(all.Count, 1);

        //    tpl = all[0];

        //    var group1 = tplService.AddOrUpdateTplGroup(new TaskGroupTplDto
        //    {
        //        DisplayName = "����1",
        //        Order = 1,
        //        TplID = tpl.ID
        //    });

        //    var step = tplService.AddOrUpdateTplStep(new TaskStepTplDto
        //    {
        //        TplID = tpl.ID,
        //        AllowMany = true,
        //        CountMaxLimit = 5,
        //        IsRequired = true,
        //        Order = 1,
        //        SupportFileTypes = StepFileType.NoLimit,
        //        DataType = StepDataType.File,
        //        AllowSelect = true,
        //        DisplayName = "����1",
        //        GroupID = group1.ID
        //    });

        //    tplService.PublishGroup(tpl.ID);
        //    tplService.PublishStep(tpl.ID);
        //    tplService.PublishTpl(tpl.ID);


        //    var file = new FileInfo(@"C:\Users\<USER>\Desktop\elasticsearch_head_demo.png");

        //    for (var i = 0; i < 5; i++)
        //    {
        //        await sampleService.AddStepValueSample(new StepValueInputDto()
        //        {
        //            StepID = step.ID,
        //            TaskItemID = taskItem.ID,
        //            Order = i,
        //            File = new FormFile(file.OpenRead(), 0, file.Length, "elasticsearch_head_demo.png", "elasticsearch_head_demo.png")
        //        });
        //    }

        //    var samples1 = sampleService.GetStepValueSamples(step.ID);
        //    Assert.AreEqual(samples1.Count, 5);

        //    var samples2 = sampleService.GetTplSampleValues(tpl.ID);
        //    Assert.AreEqual(samples2.Count, 5);
        //}

        //[Test]
        //public async Task TestTplValue()
        //{
        //    ServiceLineService serviceLineService = _serviceProvider.GetService<ServiceLineService>();
        //    RegionService regionService = _serviceProvider.GetService<RegionService>();
        //    ProjectService projectService = _serviceProvider.GetService<ProjectService>();
        //    TaskItemService taskItemService = _serviceProvider.GetService<TaskItemService>();
        //    TaskTplService tplService = _serviceProvider.GetService<TaskTplService>();
        //    TaskStepValueService valueService = _serviceProvider.GetService<TaskStepValueService>();
        //    serviceLineService.DeleteAll();
        //    regionService.DeleteAll();
        //    projectService.DeleteAll();
        //    taskItemService.DeleteAll();
        //    tplService.DeleteAll();
        //    valueService.DeleteAll();

        //    var serviceLine = serviceLineService.Create();
        //    serviceLine.Code = "test";
        //    serviceLine = serviceLineService.Insert(serviceLine);

        //    var region = regionService.Create();
        //    region.Code = "001";
        //    regionService.Insert(region);

        //    var project = projectService.Create();
        //    project.ServiceLineID = serviceLine.ID;
        //    project.RegionCode = region.Code;
        //    project.DisplayName = "������Ŀ";

        //    projectService.Insert(project);

        //    var taskItem = taskItemService.Create();
        //    taskItem.Code = "TEST1";
        //    taskItem.DisplayName = "�人������������޹�˾";
        //    taskItem.ProjectID = project.ID;
        //    taskItem.ServiceLineID = serviceLine.ID;
        //    taskItemService.Insert(taskItem);

        //    var tpl = tplService.Create();

        //    tpl.DisplayName = "����ģ��";
        //    tpl.ProjectID = project.ID;
        //    tpl.ServiceLineID = serviceLine.ID;
        //    tpl.RegionCode = region.Code;

        //    tpl = tplService.Insert(tpl);

        //    Assert.IsNotNull(tpl.ID);

        //    tplService.AddOrUpdateTpl(tpl);

        //    var all = tplService.FindAll();
        //    Assert.AreEqual(all.Count, 1);

        //    tpl = all[0];

        //    var group1 = tplService.AddOrUpdateTplGroup(new TaskGroupTplDto
        //    {
        //        DisplayName = "����1",
        //        Order = 1,
        //        TplID = tpl.ID
        //    });

        //    var step = tplService.AddOrUpdateTplStep(new TaskStepTplDto
        //    {
        //        TplID = tpl.ID,
        //        AllowMany = true,
        //        CountMaxLimit = 5,
        //        IsRequired = true,
        //        Order = 1,
        //        SupportFileTypes = StepFileType.NoLimit,
        //        DataType = StepDataType.File,
        //        AllowSelect = true,
        //        DisplayName = "����1",
        //        GroupID = group1.ID
        //    });

        //    tplService.PublishGroup(tpl.ID);
        //    tplService.PublishStep(tpl.ID);
        //    tplService.PublishTpl(tpl.ID);


        //    var file = new FileInfo(@"C:\Users\<USER>\Desktop\elasticsearch_head_demo.png");

        //    for (var i = 0; i < 5; i++)
        //    {
        //        await valueService.AddStepValue(new StepValueInputDto()
        //        {
        //            StepID = step.ID,
        //            TaskItemID = taskItem.ID,
        //            Order = i,
        //            File = new FormFile(file.OpenRead(), 0, file.Length, "elasticsearch_head_demo.png", "elasticsearch_head_demo.png")
        //        });
        //    }

        //    var values1 = valueService.GetStepValues(taskItem.ID, step.ID, null);
        //    Assert.AreEqual(values1.Count, 5);

        //    var values2 = valueService.GetTplValues(taskItem.ID, tpl.ID, null);
        //    Assert.AreEqual(values2.Count, 5);
        //}

        //[Test]
        //public async Task TestQueryApprovalStatus()
        //{
        //    ServiceLineService serviceLineService = _serviceProvider.GetService<ServiceLineService>();
        //    RegionService regionService = _serviceProvider.GetService<RegionService>();
        //    ProjectService projectService = _serviceProvider.GetService<ProjectService>();
        //    TaskItemService taskItemService = _serviceProvider.GetService<TaskItemService>();
        //    TaskTplService tplService = _serviceProvider.GetService<TaskTplService>();
        //    TaskStepValueService valueService = _serviceProvider.GetService<TaskStepValueService>();
        //    ApprovalService approvalService = _serviceProvider.GetService<ApprovalService>();
        //    RelTaskItemAndTplService relService = _serviceProvider.GetService<RelTaskItemAndTplService>();
        //    serviceLineService.DeleteAll();
        //    regionService.DeleteAll();
        //    projectService.DeleteAll();
        //    taskItemService.DeleteAll();
        //    tplService.DeleteAll();
        //    valueService.DeleteAll();
        //    approvalService.DeleteAll();
        //    relService.DeleteAll();

        //    var serviceLine = serviceLineService.Create();
        //    serviceLine.Code = "test";
        //    serviceLine = serviceLineService.Insert(serviceLine);

        //    var region = regionService.Create();
        //    region.Code = "001";
        //    regionService.Insert(region);

        //    var project = projectService.Create();
        //    project.ServiceLineID = serviceLine.ID;
        //    project.RegionCode = region.Code;
        //    project.DisplayName = "������Ŀ";

        //    projectService.Insert(project);

        //    var taskItem = taskItemService.Create();
        //    taskItem.Code = "TEST1";
        //    taskItem.DisplayName = "�人������������޹�˾";
        //    taskItem.ProjectID = project.ID;
        //    taskItem.ServiceLineID = serviceLine.ID;
        //    taskItemService.Insert(taskItem);

        //    var tpl = tplService.Create();

        //    tpl.DisplayName = "����ģ��";
        //    tpl.ProjectID = project.ID;
        //    tpl.ServiceLineID = serviceLine.ID;
        //    tpl.RegionCode = region.Code;

        //    tpl = tplService.Insert(tpl);

        //    Assert.IsNotNull(tpl.ID);

        //    tplService.AddOrUpdateTpl(tpl);

        //    var all = tplService.FindAll();
        //    Assert.AreEqual(all.Count, 1);

        //    tpl = all[0];
        //    taskItemService.AssignTaskItemTpls(new List<string> { taskItem.ID }, new List<string>() { tpl.ID });
        //    var group1 = tplService.AddOrUpdateTplGroup(new TaskGroupTplDto
        //    {
        //        DisplayName = "����1",
        //        Order = 1,
        //        TplID = tpl.ID
        //    });

        //    var step1 = tplService.AddOrUpdateTplStep(new TaskStepTplDto
        //    {
        //        TplID = tpl.ID,
        //        AllowMany = true,
        //        CountMaxLimit = 5,
        //        IsRequired = true,
        //        Order = 1,
        //        SupportFileTypes = StepFileType.NoLimit,
        //        DataType = StepDataType.File,
        //        AllowSelect = true,
        //        DisplayName = "����1",
        //        GroupID = group1.ID
        //    });

        //    var step2 = tplService.AddOrUpdateTplStep(new TaskStepTplDto
        //    {
        //        TplID = tpl.ID,
        //        AllowMany = true,
        //        CountMaxLimit = 5,
        //        IsRequired = true,
        //        Order = 1,
        //        SupportFileTypes = StepFileType.NoLimit,
        //        DataType = StepDataType.File,
        //        AllowSelect = true,
        //        DisplayName = "����1",
        //        GroupID = group1.ID
        //    });

        //    tplService.PublishGroup(tpl.ID);
        //    tplService.PublishStep(tpl.ID);
        //    tplService.PublishTpl(tpl.ID);

        //    var file = new FileInfo(@"C:\Users\<USER>\Desktop\444.jpg");

        //    for (var i = 0; i < 5; i++)
        //    {
        //        await valueService.AddStepValue(new StepValueInputDto()
        //        {
        //            StepID = step1.ID,
        //            TaskItemID = taskItem.ID,
        //            Order = i,
        //            File = new FormFile(file.OpenRead(), 0, file.Length, "elasticsearch_head_demo.png", "elasticsearch_head_demo.png")
        //        });
        //    }

        //    approvalService.AddOrUpdateStepApprovalStatus(taskItem.ID, step1.ID, ApprovalStatus.Approved, false, "����", 1);
        //    approvalService.AddOrUpdateStepApprovalStatus(taskItem.ID, step2.ID, ApprovalStatus.Disapproved, false, "����", 1);
        //    var values = valueService.GetStepValues(taskItem.ID, step1.ID, null);
        //    approvalService.AddOrUpdateFileApprovalStatus(values[0].ID, ApprovalStatus.Approved, false, "����", 1);
        //    approvalService.AddOrUpdateFileApprovalStatus(values[1].ID, ApprovalStatus.Approved, false, "����", 1);
        //    var list1 = approvalService.QueryFileApprovalStatusByStepID(taskItem.ID, step1.ID);
        //    Assert.AreEqual(list1.Count, 5);
        //    var merged = approvalService.QueryTaskItemApprovalStatus(taskItem.ID, tpl.ID);
        //    Assert.AreEqual(merged.FileApprovalStatus.Count, 5);
        //    Assert.AreEqual(merged.StepApprovalStatus.Count, 2);
        //}

        //[Test]
        //public async Task TestExportTaskItem()
        //{
        //    ServiceLineService serviceLineService = _serviceProvider.GetService<ServiceLineService>();
        //    RegionService regionService = _serviceProvider.GetService<RegionService>();
        //    ProjectService projectService = _serviceProvider.GetService<ProjectService>();
        //    TaskItemService taskItemService = _serviceProvider.GetService<TaskItemService>();
        //    TaskTplService tplService = _serviceProvider.GetService<TaskTplService>();
        //    TaskStepValueService valueService = _serviceProvider.GetService<TaskStepValueService>();
        //    ApprovalService approvalService = _serviceProvider.GetService<ApprovalService>();
        //    RelTaskItemAndTplService relService = _serviceProvider.GetService<RelTaskItemAndTplService>();
        //    ExportDataService exportService = _serviceProvider.GetService<ExportDataService>();
        //    serviceLineService.DeleteAll();
        //    regionService.DeleteAll();
        //    projectService.DeleteAll();
        //    taskItemService.DeleteAll();
        //    tplService.DeleteAll();
        //    valueService.DeleteAll();
        //    approvalService.DeleteAll();
        //    relService.DeleteAll();

        //    var serviceLine = serviceLineService.Create();
        //    serviceLine.Code = "test";
        //    serviceLine = serviceLineService.Insert(serviceLine);

        //    var region = regionService.Create();
        //    region.Code = "001";
        //    regionService.Insert(region);

        //    var project = projectService.Create();
        //    project.ServiceLineID = serviceLine.ID;
        //    project.RegionCode = region.Code;
        //    project.DisplayName = "������Ŀ";

        //    projectService.Insert(project);

        //    var taskItem = taskItemService.Create();
        //    taskItem.Code = "TEST1";
        //    taskItem.DisplayName = "�人������������޹�˾";
        //    taskItem.ProjectID = project.ID;
        //    taskItem.ServiceLineID = serviceLine.ID;
        //    taskItemService.Insert(taskItem);

        //    var tpl = tplService.Create();

        //    tpl.DisplayName = "����ģ��";
        //    tpl.ProjectID = project.ID;
        //    tpl.ServiceLineID = serviceLine.ID;
        //    tpl.RegionCode = region.Code;

        //    tpl = tplService.Insert(tpl);

        //    Assert.IsNotNull(tpl.ID);

        //    tplService.AddOrUpdateTpl(tpl);

        //    var all = tplService.FindAll();
        //    Assert.AreEqual(all.Count, 1);

        //    tpl = all[0];
        //    taskItemService.AssignTaskItemTpls(new List<string> { taskItem.ID }, new List<string>() { tpl.ID });
        //    var group1 = tplService.AddOrUpdateTplGroup(new TaskGroupTplDto
        //    {
        //        DisplayName = "����1",
        //        Order = 1,
        //        TplID = tpl.ID
        //    });
        //    var group2 = tplService.AddOrUpdateTplGroup(new TaskGroupTplDto
        //    {
        //        DisplayName = "����2",
        //        Order = 2,
        //        TplID = tpl.ID
        //    });

        //    var step1 = tplService.AddOrUpdateTplStep(new TaskStepTplDto
        //    {
        //        TplID = tpl.ID,
        //        AllowMany = true,
        //        CountMaxLimit = 5,
        //        IsRequired = true,
        //        Order = 1,
        //        SupportFileTypes = StepFileType.NoLimit,
        //        DataType = StepDataType.File,
        //        AllowSelect = true,
        //        DisplayName = "����1",
        //        GroupID = group1.ID
        //    });

        //    var step2 = tplService.AddOrUpdateTplStep(new TaskStepTplDto
        //    {
        //        TplID = tpl.ID,
        //        AllowMany = true,
        //        CountMaxLimit = 5,
        //        IsRequired = true,
        //        Order = 1,
        //        SupportFileTypes = StepFileType.NoLimit,
        //        DataType = StepDataType.File,
        //        AllowSelect = true,
        //        DisplayName = "����2",
        //        GroupID = group2.ID
        //    });


        //    var step3 = tplService.AddOrUpdateTplStep(new TaskStepTplDto
        //    {
        //        TplID = tpl.ID,
        //        AllowMany = true,
        //        CountMaxLimit = 5,
        //        IsRequired = true,
        //        Order = 1,
        //        SupportFileTypes = StepFileType.NoLimit,
        //        DataType = StepDataType.File,
        //        AllowSelect = true,
        //        DisplayName = "����3",
        //        GroupID = group2.ID
        //    });

        //    tplService.PublishGroup(tpl.ID);
        //    tplService.PublishStep(tpl.ID);
        //    tplService.PublishTpl(tpl.ID);

        //    var file = new FileInfo(@"C:\Users\<USER>\Desktop\logo.png");

        //    for (var i = 0; i < 5; i++)
        //    {
        //        await valueService.AddStepValue(new StepValueInputDto()
        //        {
        //            StepID = step1.ID,
        //            TaskItemID = taskItem.ID,
        //            Order = i,
        //            File = new FormFile(file.OpenRead(), 0, file.Length, "logo.png", "logo.png")
        //        });
        //    }

        //    var values = valueService.GetTplValues(taskItem.ID, tpl.ID, null);

        //    file = new FileInfo(@"C:\Users\<USER>\Desktop\����.png");

        //    await valueService.UpdateStepValue(new StepValueInputDto()
        //    {
        //        ValueID = values[0].ID,
        //        TaskItemID = taskItem.ID,
        //        Order = 1,
        //        File = new FormFile(file.OpenRead(), 0, file.Length, "����.png", "����.png")
        //    });

        //}

        //[Test]
        //public void TestWatermark()
        //{
        //    ImageThumbHelper.Thumb(@"C:\Users\<USER>\Desktop\IMG_4054.jpg", @"C:\Users\<USER>\Desktop\IMG_4054_1.jpg");
        //    //WatermarkHelper.AddWatermark(new System.Collections.Generic.List<string> { "����Line1", "����Line2", "����Line3", "����Line4" }, @"C:\Users\<USER>\Desktop\logo.png", @"C:\Users\<USER>\Desktop\logo_mark.png");
        //    //WatermarkHelper.AddWatermarkAndThumb(new System.Collections.Generic.List<string> { "����Line1 �������� ����Line1 �������� ����Line1 �������� ����Line1 ��������", "����Line2 �人���", "����Line3 QMD����", "����Line4" }, @"C:\Users\<USER>\Desktop\logo.png", @"C:\Users\<USER>\Desktop\logo_thumb.png");
        //}
    }
}