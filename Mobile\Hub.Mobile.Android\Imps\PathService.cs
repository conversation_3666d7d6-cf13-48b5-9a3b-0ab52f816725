﻿using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Views;
using Android.Widget;
using Hub.Mobile.Interface;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using Xamarin.Forms;

[assembly:Dependency(typeof(Hub.Mobile.Droid.Imps.PathService))]
namespace Hub.Mobile.Droid.Imps
{
    public class PathService:IPathService
    {
        public string GetAlbumPath()
        {
            return Android.OS.Environment.GetExternalStoragePublicDirectory(Android.OS.Environment.DirectoryPictures).AbsolutePath;
        }
        public string GetDirectoryDownloads()
        {
            //return Android.OS.Environment.GetExternalStoragePublicDirectory(Android.OS.Environment.DirectoryDownloads).AbsolutePath;
            return MainActivity.AppContext.GetExternalFilesDir(Android.OS.Environment.DirectoryDownloads).Path;
        }
    }
}