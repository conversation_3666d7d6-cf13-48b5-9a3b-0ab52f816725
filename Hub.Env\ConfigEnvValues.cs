﻿using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using System.IO;

namespace Hub.Env
{
    public static class ConfigEnvValues
    {
        private static IConfiguration _config;
        public static void Init(IConfiguration configure)
        {
            _config = configure;
        }

        public static string DomainURL { get { return _config["DomainURL"]; } }
        public static string CDNRootUrl { get { return _config["CDNRootURL"]; } }
        public static string CDNProjectName { get { return _config["CDNProjectName"]; } }
        public static string CDNPhysicalFolderPath { get { return _config["CDNPhysicalFolderPath"]; } }
        public static string OTAFolderName { get { return _config["OTAFolderName"]; } }
        public static string HybridAppFolderName { get { return _config["HybridAppFolderName"]; } }
        public static string OTAConfigFolderFullPath { get { return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, OTAFolderName); } }
        public static string OTAConfigFileFullPath { get { return Path.Combine(OTAConfigFolderFullPath, "version.xml"); } }
        public static string OTAFolderFullPath { get { return Path.Combine(CDNPhysicalFolderPath, OTAFolderName); } }
        public static string HybridAppFolderFullPath { get { return Path.Combine(CDNPhysicalFolderPath, HybridAppFolderName); } }
    }
}
