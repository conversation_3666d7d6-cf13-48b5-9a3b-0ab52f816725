(function () {
    function Authorize() {
    }
    //success token字符串
    //error    错误提示
    Authorize.prototype.getToken = function (successCallback, errorCallback) {
      if (typeof errorCallback != "function") {
          console.error("parameter [errorCallback] should be function");
          return;
        }
        if (typeof successCallback != "function") {
          console.error("parameter [successCallback] should be function");
          return;
        }
        hub_mobile_drive.exec(
        function (result) {
          successCallback(result);
        },
        function (error) {
          errorCallback(error);
        },
        "Authorize",
        "GetToken",
        null
      );
    };
  //success  
  //   {
  //     "Email":"xxxx",
  //     "NickName":"xxxx",
  //     "Position":"xxxx",
  //     "Areas":"xxxx"
  //  }
    //error    错误提示
    Authorize.prototype.getCurrentUser = function (successCallback, errorCallback) {
        if (typeof errorCallback != "function") {
            console.error("parameter [errorCallback] should be function");
            return;
          }
          if (typeof successCallback != "function") {
            console.error("parameter [successCallback] should be function");
            return;
          }
          hub_mobile_drive.exec(
          function (result) {
            successCallback(result);
          },
          function (error) {
            errorCallback(error);
          },
          "Authorize",
          "GetCurrentUser",
          null
        );
      };
    //添加插件
    hub_mobile_drive.addPlugin("authorize", new Authorize());
  })();
  