﻿using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Hub.DAL;
using Hub.Repository;

namespace Hub.Service
{
    public static class ServiceLocator
    {
        public static void RegisterServices(this IServiceCollection services)
        {
            services.AddScoped<AppService>();
            services.AddSingleton<ClientLogService>();
            services.AddSingleton<OTAService>();
        }
    }
}
