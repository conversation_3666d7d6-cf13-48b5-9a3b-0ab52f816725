﻿using Acr.UserDialogs;
using Hub.Mobile.Interface;
using Hub.Mobile.Model;
using Hub.Mobile.Resources;
using Hub.Mobile.Views;
using Rg.Plugins.Popup.Services;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Xamarin.Forms;

namespace Hub.Mobile.ViewModels
{
    public class SettingViewModel : BasePageViewModel
    {
        public Command SwitchLanguageCommand { get; }
        public Command PersonalInformationCommand { get; }
        public Command LogoutCommand { get; }
        public Command SynchronizeDataCommand { get; }
        public Command CheckUpgradeCommand { get; }
        public Command UploadLogCommand { get; }
        public Command ChangePasswordCommand { get; }
        public string CurrentVersion { get; set; }
        private bool isLogin;
        public bool IsLogin { get { return isLogin; } set { SetProperty(ref isLogin, value); } }
        bool initFinished = false;
        IAccountService accountService = DependencyService.Get<IAccountService>();
        public SettingViewModel()
        {
            SwitchLanguageCommand = new Command(OnSwitchLanguageTapped);
            PersonalInformationCommand = new Command(OnPersonalInformationTapped);
            LogoutCommand = new Command(OnLogoutTapped);
            SynchronizeDataCommand = new Command(OnSynchronizeDataTapped);
            CheckUpgradeCommand = new Command(OnCheckUpgradeTapped);
            CurrentVersion = Upgrade.GetCurrentVersion();
            UploadLogCommand = new Command(OnUploadLogTapped);
            ChangePasswordCommand = new Command(async () => {
                if (!IsBusy)
                {
                    IsBusy = true;
                    if (await accountService.ValidateTokenAsync())
                    {
                        await Navigation.PushAsync(new ChangePasswordPage());
                    }
                    else
                    {
                        await PopupNavigation.Instance.PushAsync(new LoginPage(ClosedCallBack));
                    }
                    IsBusy = false;
                }
            });
            InitData();
        }
        private async void InitData()
        {
            initFinished = false;
            IsLogin =await accountService.IsLogin();
            initFinished = true;
        }
        private async void OnUploadLogTapped(object obj)
        {
            if (!IsBusy)
            {
                IsBusy = true;
                if (await UserDialogs.Instance.ConfirmAsync(UIResources.LogUploadConfirmMsg, okText: UIResources.Ok, cancelText: UIResources.Cancel))
                {
#if RELEASE
                    if (DependencyService.Get<INetWork>().IsNetworkAvailable())
                    {
                        await PopupNavigation.Instance.PushAsync(new UploadLogPage());
                    }
                    else
                    {
                         await UserDialogs.Instance.AlertAsync(UIResources.NetworkUnavailable, okText: UIResources.Ok);
                    }
#endif
#if DEBUG
                    await PopupNavigation.Instance.PushAsync(new UploadLogPage());
#endif
                }
                IsBusy = false;
            }
        }

        private async void OnCheckUpgradeTapped(object obj)
        {
            if (!IsBusy)
            {
                IsBusy = true;
                var checkResult = await Upgrade.Check();
                if (checkResult.Success)
                {
                    if (checkResult.NeedUpgrade)
                    {
                        if (await UserDialogs.Instance.ConfirmAsync(UIResources.UpgradeConfirmMsg, okText: UIResources.Ok, cancelText: UIResources.Cancel))
                        {
                            await PopupNavigation.Instance.PushAsync(new UpgradePage(checkResult.FileName));
                        }
                    }
                    else
                    {
                        await UserDialogs.Instance.AlertAsync(UIResources.AlreadyLatestVersion, okText: UIResources.Ok);
                    }
                }
                else
                {
                    await UserDialogs.Instance.AlertAsync(checkResult.Message, okText: UIResources.Ok);
                }
                IsBusy = false;
            }
        }

        private async void OnSynchronizeDataTapped(object obj)
        {
            if (!IsBusy)
            {
                IsBusy = true;
                if (await UserDialogs.Instance.ConfirmAsync(UIResources.SynchronizeConfirmMsg, okText: UIResources.Ok, cancelText: UIResources.Cancel))
                {
#if RELEASE
                    if (await accountService.SubmitCheckAsync())
                    {
                        await PopupNavigation.Instance.PushAsync(new SynchronizeBasicDataPopupPage());
                    }
#endif
#if DEBUG
                    await PopupNavigation.Instance.PushAsync(new SynchronizeBasicDataPopupPage());
#endif
                }
                IsBusy = false;
            }
        }

        private async void OnLogoutTapped(object obj)
        {
            if (!IsBusy)
            {
                IsBusy = true;
                if (initFinished)
                {
                    await accountService.LogoutAsync(loginDialogClosedCallBack: ClosedCallBack);
                }
                IsBusy = false;
            }
        }
        private void ClosedCallBack()
        {
            InitData();
        }
        private async void OnPersonalInformationTapped(object obj)
        {
            if (!IsBusy)
            {
                IsBusy = true;
                await Navigation.PushAsync(new PersonalInformationPage());
                IsBusy = false;
            }
        }

        private async void OnSwitchLanguageTapped(object obj)
        {
            if (!IsBusy)
            {
                IsBusy = true;
                await PopupNavigation.Instance.PushAsync(new SwitchLanguagePopupPage());
                IsBusy = false;
            }
        }
    }
}
