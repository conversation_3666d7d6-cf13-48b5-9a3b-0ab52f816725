﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace Hub.Mobile.Resources {
    using System;
    
    
    /// <summary>
    ///   一个强类型的资源类，用于查找本地化的字符串等。
    /// </summary>
    // 此类是由 StronglyTypedResourceBuilder
    // 类通过类似于 ResGen 或 Visual Studio 的工具自动生成的。
    // 若要添加或移除成员，请编辑 .ResX 文件，然后重新运行 ResGen
    // (以 /str 作为命令选项)，或重新生成 VS 项目。
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class UIResources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal UIResources() {
        }
        
        /// <summary>
        ///   返回此类使用的缓存的 ResourceManager 实例。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Hub.Mobile.Resources.UIResources", typeof(UIResources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   重写当前线程的 CurrentUICulture 属性，对
        ///   使用此强类型资源类的所有资源查找执行重写。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   查找类似 Acquire 的本地化字符串。
        /// </summary>
        internal static string AcquireVerificationCode {
            get {
                return ResourceManager.GetString("AcquireVerificationCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Acquire Failed 的本地化字符串。
        /// </summary>
        internal static string AcquireVerificationCodeFailed {
            get {
                return ResourceManager.GetString("AcquireVerificationCodeFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Add 的本地化字符串。
        /// </summary>
        internal static string Add {
            get {
                return ResourceManager.GetString("Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Address 的本地化字符串。
        /// </summary>
        internal static string Address {
            get {
                return ResourceManager.GetString("Address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Alert 的本地化字符串。
        /// </summary>
        internal static string Alert {
            get {
                return ResourceManager.GetString("Alert", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Current version is latest 的本地化字符串。
        /// </summary>
        internal static string AlreadyLatestVersion {
            get {
                return ResourceManager.GetString("AlreadyLatestVersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Approved 的本地化字符串。
        /// </summary>
        internal static string Approved {
            get {
                return ResourceManager.GetString("Approved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Area 的本地化字符串。
        /// </summary>
        internal static string Area {
            get {
                return ResourceManager.GetString("Area", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Cancel 的本地化字符串。
        /// </summary>
        internal static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Change Password 的本地化字符串。
        /// </summary>
        internal static string ChangePassword {
            get {
                return ResourceManager.GetString("ChangePassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Check For New Version 的本地化字符串。
        /// </summary>
        internal static string CheckVersion {
            get {
                return ResourceManager.GetString("CheckVersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 compressing 的本地化字符串。
        /// </summary>
        internal static string Compressing {
            get {
                return ResourceManager.GetString("Compressing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Confirm Password 的本地化字符串。
        /// </summary>
        internal static string ConfirmPassword {
            get {
                return ResourceManager.GetString("ConfirmPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Cover 的本地化字符串。
        /// </summary>
        internal static string Cover {
            get {
                return ResourceManager.GetString("Cover", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Create 的本地化字符串。
        /// </summary>
        internal static string Create {
            get {
                return ResourceManager.GetString("Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Date Created 的本地化字符串。
        /// </summary>
        internal static string DateCreated {
            get {
                return ResourceManager.GetString("DateCreated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 decompress completed 的本地化字符串。
        /// </summary>
        internal static string DecompressCompleted {
            get {
                return ResourceManager.GetString("DecompressCompleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 decompress failed 的本地化字符串。
        /// </summary>
        internal static string DecompressFailed {
            get {
                return ResourceManager.GetString("DecompressFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 decompressing 的本地化字符串。
        /// </summary>
        internal static string Decompressing {
            get {
                return ResourceManager.GetString("Decompressing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Delete 的本地化字符串。
        /// </summary>
        internal static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Are you sure to delete? 的本地化字符串。
        /// </summary>
        internal static string DeleteConfirmMsg {
            get {
                return ResourceManager.GetString("DeleteConfirmMsg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 download failed 的本地化字符串。
        /// </summary>
        internal static string DownloadFailed {
            get {
                return ResourceManager.GetString("DownloadFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 downloading 的本地化字符串。
        /// </summary>
        internal static string Downloading {
            get {
                return ResourceManager.GetString("Downloading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Edit 的本地化字符串。
        /// </summary>
        internal static string Edit {
            get {
                return ResourceManager.GetString("Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Email 的本地化字符串。
        /// </summary>
        internal static string Email {
            get {
                return ResourceManager.GetString("Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Email is incorrect 的本地化字符串。
        /// </summary>
        internal static string EmailIncorrect {
            get {
                return ResourceManager.GetString("EmailIncorrect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Email or UserName 的本地化字符串。
        /// </summary>
        internal static string EmailOrUserName {
            get {
                return ResourceManager.GetString("EmailOrUserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Email or UserName required 的本地化字符串。
        /// </summary>
        internal static string EmailOrUserNameRequired {
            get {
                return ResourceManager.GetString("EmailOrUserNameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Email required 的本地化字符串。
        /// </summary>
        internal static string EmailRequired {
            get {
                return ResourceManager.GetString("EmailRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Email Code 的本地化字符串。
        /// </summary>
        internal static string EmailVerificationCode {
            get {
                return ResourceManager.GetString("EmailVerificationCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Blank 的本地化字符串。
        /// </summary>
        internal static string Empty {
            get {
                return ResourceManager.GetString("Empty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Fail 的本地化字符串。
        /// </summary>
        internal static string Fail {
            get {
                return ResourceManager.GetString("Fail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Failed 的本地化字符串。
        /// </summary>
        internal static string Failed {
            get {
                return ResourceManager.GetString("Failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Finished 的本地化字符串。
        /// </summary>
        internal static string Finished {
            get {
                return ResourceManager.GetString("Finished", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Group 的本地化字符串。
        /// </summary>
        internal static string Group {
            get {
                return ResourceManager.GetString("Group", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Image 的本地化字符串。
        /// </summary>
        internal static string Image {
            get {
                return ResourceManager.GetString("Image", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Image Code 的本地化字符串。
        /// </summary>
        internal static string ImageVerificationCode {
            get {
                return ResourceManager.GetString("ImageVerificationCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Inconsistent Password 的本地化字符串。
        /// </summary>
        internal static string InconsistentPassword {
            get {
                return ResourceManager.GetString("InconsistentPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 install failed 的本地化字符串。
        /// </summary>
        internal static string InstallFailed {
            get {
                return ResourceManager.GetString("InstallFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Language Switch 的本地化字符串。
        /// </summary>
        internal static string LanguageSwitch {
            get {
                return ResourceManager.GetString("LanguageSwitch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Last Synchronization Time: 的本地化字符串。
        /// </summary>
        internal static string LastSynchronizationTime {
            get {
                return ResourceManager.GetString("LastSynchronizationTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Log In 的本地化字符串。
        /// </summary>
        internal static string Login {
            get {
                return ResourceManager.GetString("Login", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Login failed,please check network 的本地化字符串。
        /// </summary>
        internal static string LogInFailed {
            get {
                return ResourceManager.GetString("LogInFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Log Out 的本地化字符串。
        /// </summary>
        internal static string LogOut {
            get {
                return ResourceManager.GetString("LogOut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Are you sure to log out? 的本地化字符串。
        /// </summary>
        internal static string LogoutConfirmMsg {
            get {
                return ResourceManager.GetString("LogoutConfirmMsg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Are you sure to upload log? 的本地化字符串。
        /// </summary>
        internal static string LogUploadConfirmMsg {
            get {
                return ResourceManager.GetString("LogUploadConfirmMsg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Alert 的本地化字符串。
        /// </summary>
        internal static string MsgAlert {
            get {
                return ResourceManager.GetString("MsgAlert", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Plaese exit edit mode in the page of Recification Reports ! 的本地化字符串。
        /// </summary>
        internal static string MsgExitRectificationReportEditMode {
            get {
                return ResourceManager.GetString("MsgExitRectificationReportEditMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Failed Reason : {0} 的本地化字符串。
        /// </summary>
        internal static string MsgFailedReasonFormat {
            get {
                return ResourceManager.GetString("MsgFailedReasonFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Cannot get GPS, please retry ! 的本地化字符串。
        /// </summary>
        internal static string MsgGPSEmpty {
            get {
                return ResourceManager.GetString("MsgGPSEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 You must select a site ! 的本地化字符串。
        /// </summary>
        internal static string MsgMustSelectSite {
            get {
                return ResourceManager.GetString("MsgMustSelectSite", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Refresh local data... 的本地化字符串。
        /// </summary>
        internal static string MsgRefreshLocalData {
            get {
                return ResourceManager.GetString("MsgRefreshLocalData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Save Finished ! 的本地化字符串。
        /// </summary>
        internal static string MsgSaveFinished {
            get {
                return ResourceManager.GetString("MsgSaveFinished", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Site Id cannot be empty ! 的本地化字符串。
        /// </summary>
        internal static string MsgSiteIdEmpty {
            get {
                return ResourceManager.GetString("MsgSiteIdEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Your local storage already contains a data with this Site Id, the Site Id cannot be repeated ! 的本地化字符串。
        /// </summary>
        internal static string MsgSiteIdRepeated {
            get {
                return ResourceManager.GetString("MsgSiteIdRepeated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Always submit all selected groups event if the group is empty. 的本地化字符串。
        /// </summary>
        internal static string MsgSubmitEmptyGroupConfirm {
            get {
                return ResourceManager.GetString("MsgSubmitEmptyGroupConfirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Submit Finished ! 的本地化字符串。
        /// </summary>
        internal static string MsgSubmittedFinished {
            get {
                return ResourceManager.GetString("MsgSubmittedFinished", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 MyMessage 的本地化字符串。
        /// </summary>
        internal static string MyMessage {
            get {
                return ResourceManager.GetString("MyMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Name 的本地化字符串。
        /// </summary>
        internal static string Name {
            get {
                return ResourceManager.GetString("Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Network unavailable 的本地化字符串。
        /// </summary>
        internal static string NetworkUnavailable {
            get {
                return ResourceManager.GetString("NetworkUnavailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 New Password 的本地化字符串。
        /// </summary>
        internal static string NewPassword {
            get {
                return ResourceManager.GetString("NewPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 No data will be submitted ! 的本地化字符串。
        /// </summary>
        internal static string NoDataSubmbitted {
            get {
                return ResourceManager.GetString("NoDataSubmbitted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Ok 的本地化字符串。
        /// </summary>
        internal static string Ok {
            get {
                return ResourceManager.GetString("Ok", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Old Password 的本地化字符串。
        /// </summary>
        internal static string OldPassword {
            get {
                return ResourceManager.GetString("OldPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Please open GPS 的本地化字符串。
        /// </summary>
        internal static string OpenGPS {
            get {
                return ResourceManager.GetString("OpenGPS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Password 的本地化字符串。
        /// </summary>
        internal static string Password {
            get {
                return ResourceManager.GetString("Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Password is required 的本地化字符串。
        /// </summary>
        internal static string PasswordRequired {
            get {
                return ResourceManager.GetString("PasswordRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Personal Information 的本地化字符串。
        /// </summary>
        internal static string PersonalInformation {
            get {
                return ResourceManager.GetString("PersonalInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Please Input 的本地化字符串。
        /// </summary>
        internal static string PleaseInput {
            get {
                return ResourceManager.GetString("PleaseInput", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Please search 的本地化字符串。
        /// </summary>
        internal static string PleaseSearch {
            get {
                return ResourceManager.GetString("PleaseSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Position 的本地化字符串。
        /// </summary>
        internal static string Position {
            get {
                return ResourceManager.GetString("Position", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Project 的本地化字符串。
        /// </summary>
        internal static string Project {
            get {
                return ResourceManager.GetString("Project", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 After 的本地化字符串。
        /// </summary>
        internal static string QRRAfter {
            get {
                return ResourceManager.GetString("QRRAfter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Before 的本地化字符串。
        /// </summary>
        internal static string QRRBefore {
            get {
                return ResourceManager.GetString("QRRBefore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Comment 的本地化字符串。
        /// </summary>
        internal static string QRRComment {
            get {
                return ResourceManager.GetString("QRRComment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 No. 的本地化字符串。
        /// </summary>
        internal static string QRRNo {
            get {
                return ResourceManager.GetString("QRRNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Rectification Report 的本地化字符串。
        /// </summary>
        internal static string RectificationReport {
            get {
                return ResourceManager.GetString("RectificationReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {0} required ! 的本地化字符串。
        /// </summary>
        internal static string RequiredFormat {
            get {
                return ResourceManager.GetString("RequiredFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Please complete the required items for shooting 的本地化字符串。
        /// </summary>
        internal static string RequiredPrompt {
            get {
                return ResourceManager.GetString("RequiredPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Save 的本地化字符串。
        /// </summary>
        internal static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Search History 的本地化字符串。
        /// </summary>
        internal static string SearchHistory {
            get {
                return ResourceManager.GetString("SearchHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Please search 的本地化字符串。
        /// </summary>
        internal static string SearchPlaceholder {
            get {
                return ResourceManager.GetString("SearchPlaceholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Select 的本地化字符串。
        /// </summary>
        internal static string Select {
            get {
                return ResourceManager.GetString("Select", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Sending 的本地化字符串。
        /// </summary>
        internal static string Sending {
            get {
                return ResourceManager.GetString("Sending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Setting 的本地化字符串。
        /// </summary>
        internal static string Setting {
            get {
                return ResourceManager.GetString("Setting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 NAP LIST 的本地化字符串。
        /// </summary>
        internal static string SiteList {
            get {
                return ResourceManager.GetString("SiteList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Site Name 的本地化字符串。
        /// </summary>
        internal static string SiteName {
            get {
                return ResourceManager.GetString("SiteName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 NAP Selection 的本地化字符串。
        /// </summary>
        internal static string SiteSelection {
            get {
                return ResourceManager.GetString("SiteSelection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Submit 的本地化字符串。
        /// </summary>
        internal static string Submit {
            get {
                return ResourceManager.GetString("Submit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Submitted 的本地化字符串。
        /// </summary>
        internal static string Submitted {
            get {
                return ResourceManager.GetString("Submitted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {0} submitted by {1} 的本地化字符串。
        /// </summary>
        internal static string SubmittedByFormat {
            get {
                return ResourceManager.GetString("SubmittedByFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 submitted at {0} 的本地化字符串。
        /// </summary>
        internal static string SubmittedFormat {
            get {
                return ResourceManager.GetString("SubmittedFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Success 的本地化字符串。
        /// </summary>
        internal static string Success {
            get {
                return ResourceManager.GetString("Success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Sure 的本地化字符串。
        /// </summary>
        internal static string Sure {
            get {
                return ResourceManager.GetString("Sure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Sync 的本地化字符串。
        /// </summary>
        internal static string Synchronize {
            get {
                return ResourceManager.GetString("Synchronize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Synchronize Basic Data 的本地化字符串。
        /// </summary>
        internal static string SynchronizeBasicData {
            get {
                return ResourceManager.GetString("SynchronizeBasicData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Are you sure to synchronize? 的本地化字符串。
        /// </summary>
        internal static string SynchronizeConfirmMsg {
            get {
                return ResourceManager.GetString("SynchronizeConfirmMsg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 There is a new version, are you sure to upgrade? 的本地化字符串。
        /// </summary>
        internal static string UpgradeConfirmMsg {
            get {
                return ResourceManager.GetString("UpgradeConfirmMsg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Upload 的本地化字符串。
        /// </summary>
        internal static string Upload {
            get {
                return ResourceManager.GetString("Upload", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 uploading 的本地化字符串。
        /// </summary>
        internal static string Uploading {
            get {
                return ResourceManager.GetString("Uploading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Upload Log 的本地化字符串。
        /// </summary>
        internal static string UploadLog {
            get {
                return ResourceManager.GetString("UploadLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Verification Failed 的本地化字符串。
        /// </summary>
        internal static string ValidatorFailed {
            get {
                return ResourceManager.GetString("ValidatorFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 VerificationCode required 的本地化字符串。
        /// </summary>
        internal static string VerificationCodeRequired {
            get {
                return ResourceManager.GetString("VerificationCodeRequired", resourceCulture);
            }
        }
    }
}
