﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
<data name="AcquireVerificationCode" xml:space="preserve">
    <value>Obtener el</value>
  </data>
  <data name="AcquireVerificationCodeFailed" xml:space="preserve">
    <value>fallo en la verificacion</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>Nueva construcción</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="Alert" xml:space="preserve">
    <value>Advertencia</value>
  </data>
  <data name="AlreadyLatestVersion" xml:space="preserve">
    <value>La versión actual es la más reciente</value>
  </data>
  <data name="Approved" xml:space="preserve">
    <value>Approved</value>
  </data>
  <data name="Area" xml:space="preserve">
    <value>Región</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Anulación</value>
  </data>
  <data name="ChangePassword" xml:space="preserve">
    <value>Cambiar contraseña</value>
  </data>
  <data name="CheckVersion" xml:space="preserve">
    <value>Compruebe si hay actualizaciones</value>
  </data>
  <data name="Compressing" xml:space="preserve">
    <value>esta comprimiendo</value>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>Confirmar contraseña</value>
  </data>
  <data name="Cover" xml:space="preserve">
    <value>Cover</value>
  </data>
  <data name="Create" xml:space="preserve">
    <value>Create</value>
  </data>
  <data name="DateCreated" xml:space="preserve">
    <value>Date Created</value>
  </data>
  <data name="DecompressCompleted" xml:space="preserve">
    <value>Descompresión completa</value>
  </data>
  <data name="DecompressFailed" xml:space="preserve">
    <value>Fallo en la descompresión</value>
  </data>
  <data name="Decompressing" xml:space="preserve">
    <value>descomprimir</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Borrar</value>
  </data>
  <data name="DeleteConfirmMsg" xml:space="preserve">
    <value>Seguro que lo queres a borrar?</value>
  </data>
  <data name="DownloadFailed" xml:space="preserve">
    <value>Descarga fallida</value>
  </data>
  <data name="Downloading" xml:space="preserve">
    <value>Descargando</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Buzón</value>
  </data>
  <data name="EmailIncorrect" xml:space="preserve">
    <value>Formato de correo electrónico incorrecto</value>
  </data>
  <data name="EmailOrUserName" xml:space="preserve">
    <value>Correo electrónico o nombre de usuario</value>
  </data>
  <data name="EmailOrUserNameRequired" xml:space="preserve">
    <value>Correo electrónico o nombre de usuario requerido</value>
  </data>
  <data name="EmailRequired" xml:space="preserve">
    <value>Correo electrónico requerido</value>
  </data>
  <data name="EmailVerificationCode" xml:space="preserve">
    <value>Código de verificación del correo electrónico</value>
  </data>
  <data name="Empty" xml:space="preserve">
    <value>Blank</value>
  </data>
  <data name="Fail" xml:space="preserve">
    <value>Fail</value>
  </data>
  <data name="Failed" xml:space="preserve">
    <value>Fracaso</value>
  </data>
  <data name="Finished" xml:space="preserve">
    <value>Completa</value>
  </data>
  <data name="Group" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="Image" xml:space="preserve">
    <value>Fotos</value>
  </data>
  <data name="ImageVerificationCode" xml:space="preserve">
    <value>Código de verificación de la imagen</value>
  </data>
  <data name="InconsistentPassword" xml:space="preserve">
    <value>Contrase?as incoherentes</value>
  </data>
  <data name="InstallFailed" xml:space="preserve">
    <value>Fallo de instalación</value>
  </data>
  <data name="LanguageSwitch" xml:space="preserve">
    <value>Cambiar de idioma</value>
  </data>
  <data name="LastSynchronizationTime" xml:space="preserve">
    <value>última sincronización en.</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Inicio de sesión</value>
  </data>
  <data name="LogInFailed" xml:space="preserve">
    <value>Fallo en el inicio de sesión, por favor, compruebe la disponibilidad de la red</value>
  </data>
  <data name="LogOut" xml:space="preserve">
    <value>Salir</value>
  </data>
  <data name="LogoutConfirmMsg" xml:space="preserve">
    <value>Seguro que quieres salir de tu cuenta actual?</value>
  </data>
  <data name="LogUploadConfirmMsg" xml:space="preserve">
    <value>Confirmar registro de carga?</value>
  </data>
  <data name="MsgAlert" xml:space="preserve">
    <value>Alert</value>
  </data>
  <data name="MsgExitRectificationReportEditMode" xml:space="preserve">
    <value>Plaese exit edit mode in the page of Recification Reports !</value>
  </data>
  <data name="MsgFailedReasonFormat" xml:space="preserve">
    <value>Motivo del fallo : {0}</value>
  </data>
  <data name="MsgGPSEmpty" xml:space="preserve">
    <value>No se ha podido obtener la información del GPS, por favor, inténtelo de nuevo.</value>
  </data>
  <data name="MsgMustSelectSite" xml:space="preserve">
    <value>You must select a site !</value>
  </data>
  <data name="MsgRefreshLocalData" xml:space="preserve">
    <value>Actualizar los datos locales...</value>
  </data>
  <data name="MsgSaveFinished" xml:space="preserve">
    <value>Save Finished !</value>
  </data>
  <data name="MsgSiteIdEmpty" xml:space="preserve">
    <value>Site Id cannot be empty !</value>
  </data>
  <data name="MsgSiteIdRepeated" xml:space="preserve">
    <value>Your local storage already contains a data with this Site Id, the Site Id cannot be repeated !</value>
  </data>
  <data name="MsgSubmitEmptyGroupConfirm" xml:space="preserve">
    <value>Always submit all selected groups event if the group is empty.</value>
  </data>
  <data name="MsgSubmittedFinished" xml:space="preserve">
    <value>Submit Finished !</value>
  </data>
  <data name="MyMessage" xml:space="preserve">
    <value>Mi mensaje</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Nombre</value>
  </data>
  <data name="NetworkUnavailable" xml:space="preserve">
    <value>Red no disponible</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>Nueva contraseña</value>
  </data>
  <data name="NoDataSubmbitted" xml:space="preserve">
    <value>No data will be submitted !</value>
  </data>
  <data name="Ok" xml:space="preserve">
    <value>Por supuesto</value>
  </data>
  <data name="OldPassword" xml:space="preserve">
    <value>Contraseña original</value>
  </data>
  <data name="OpenGPS" xml:space="preserve">
    <value>Por favor, abra la información de localización</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Contraseña</value>
  </data>
  <data name="PasswordRequired" xml:space="preserve">
    <value>Se requiere contrase?a</value>
  </data>
  <data name="PersonalInformation" xml:space="preserve">
    <value>Información personal</value>
  </data>
  <data name="PleaseInput" xml:space="preserve">
    <value>Por favor, introduzca</value>
  </data>
  <data name="PleaseSearch" xml:space="preserve">
    <value>Por favor, introduzca su búsqueda</value>
  </data>
  <data name="Position" xml:space="preserve">
    <value>Posición</value>
  </data>
  <data name="Project" xml:space="preserve">
    <value>Project</value>
  </data>
  <data name="QRRAfter" xml:space="preserve">
    <value>After</value>
  </data>
  <data name="QRRBefore" xml:space="preserve">
    <value>Before</value>
  </data>
  <data name="QRRComment" xml:space="preserve">
    <value>Comment</value>
  </data>
  <data name="QRRNo" xml:space="preserve">
    <value>No.</value>
  </data>
  <data name="RectificationReport" xml:space="preserve">
    <value>Rectification Report</value>
  </data>
  <data name="RequiredFormat" xml:space="preserve">
    <value>{0} es necesario !</value>
  </data>
  <data name="RequiredPrompt" xml:space="preserve">
    <value>Please complete the required items for shooting</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="SearchHistory" xml:space="preserve">
    <value>Historial de búsqueda</value>
  </data>
  <data name="SearchPlaceholder" xml:space="preserve">
    <value>Please search</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>Select</value>
  </data>
  <data name="Sending" xml:space="preserve">
    <value>esta enviando</value>
  </data>
  <data name="Setting" xml:space="preserve">
    <value>Ajustes</value>
  </data>
  <data name="SiteList" xml:space="preserve">
    <value>NAP LIST</value>
  </data>
  <data name="SiteName" xml:space="preserve">
    <value>Site Name</value>
  </data>
  <data name="SiteSelection" xml:space="preserve">
    <value>NAP Selection</value>
  </data>
  <data name="Submit" xml:space="preserve">
    <value>Enviar</value>
  </data>
  <data name="Submitted" xml:space="preserve">
    <value>Submitted</value>
  </data>
  <data name="SubmittedByFormat" xml:space="preserve">
    <value>{0} submitted by {1}</value>
  </data>
  <data name="SubmittedFormat" xml:space="preserve">
    <value>submitted at {0}</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>éxito</value>
  </data>
  <data name="Sure" xml:space="preserve">
    <value>Sure</value>
  </data>
  <data name="Synchronize" xml:space="preserve">
    <value>Sincronización</value>
  </data>
  <data name="SynchronizeBasicData" xml:space="preserve">
    <value>Sincronización de datos básicos</value>
  </data>
  <data name="SynchronizeConfirmMsg" xml:space="preserve">
    <value>Seguro que quieres sincronizar los datos?</value>
  </data>
  <data name="UpgradeConfirmMsg" xml:space="preserve">
    <value>Hay una nueva version, queres actualizar?</value>
  </data>
  <data name="Upload" xml:space="preserve">
    <value>Subir a</value>
  </data>
  <data name="Uploading" xml:space="preserve">
    <value>Cargando</value>
  </data>
  <data name="UploadLog" xml:space="preserve">
    <value>Cargar el registro</value>
  </data>
  <data name="ValidatorFailed" xml:space="preserve">
    <value>Fallo en la verificacion</value>
  </data>
  <data name="VerificationCodeRequired" xml:space="preserve">
    <value>Captcha requerido</value>
  </data>
</root>