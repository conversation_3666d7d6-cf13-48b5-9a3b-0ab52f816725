﻿using Hub.Mobile.ApiClient.Login;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using WebApiClient;
using WebApiClient.Attributes;

namespace Hub.Mobile.ApiClient
{
    public interface ILoginApiClient : IHttpApi
    {
        //[HttpGet("Image/GetWXRegisterVerifyImage")]
        //Task<GetVerifyImageResponse> GetVerifyImage([PathQuery] GetVerifyImageRequest request);
        [HttpPost("Weixin/SendRegisterEmailCode")]
        Task<SendEmailCodeResponse> SendEmailCode([JsonContent] SendEmailCodeRequest request, [Header("sid")] string sid);
        [HttpPost("token")]
        Task<LoginResponse> Login([JsonContent] LoginRequest request, [Header("sid")] string sid);
        [HttpGet("Account/GetSimpleUserInfo")]
        Task<GetUserInfoResponse> GetUserInfo([Header("Authorization")] string authorization);
        [HttpGet("UserToken/VerifyTokenAuthorize")]
        Task<string> ValidateToken([Header("Authorization")] string authorization);
        [HttpPost("Personal/ChangePassword")]
        Task<ChangePasswordResponse> ChangePassword([JsonContent] ChangePasswordRequest request, [Header("Authorization")] string authorization);
    }
}
