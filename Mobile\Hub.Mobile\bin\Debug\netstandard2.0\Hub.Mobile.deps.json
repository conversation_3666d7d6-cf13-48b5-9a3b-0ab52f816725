{"runtimeTarget": {"name": ".NETStandard,Version=v2.0/", "signature": ""}, "compilationOptions": {}, "targets": {".NETStandard,Version=v2.0": {}, ".NETStandard,Version=v2.0/": {"Hub.Mobile/1.0.0": {"dependencies": {"Acr.UserDialogs": "7.2.0.564", "FluentValidation": "11.0.2", "Hub.Mobile.ApiClient": "1.0.0", "Hub.Mobile.CommonControl": "1.0.0", "Hub.Mobile.Const": "1.0.0", "Hub.Mobile.Converter": "1.0.0", "Hub.Mobile.DAL": "1.0.0", "Hub.Mobile.Interface": "1.0.0", "Hub.Mobile.Model": "1.0.0", "Hub.Mobile.Utility": "1.0.0", "NETStandard.Library": "2.0.3", "Rg.Plugins.Popup": "2.1.0", "Xamarin.Essentials": "1.6.1", "Xamarin.Forms": "5.0.0.2401", "dotMorten.Xamarin.Forms.AutoSuggestBox": "1.1.1"}, "runtime": {"Hub.Mobile.dll": {}}, "resources": {"id/Hub.Mobile.resources.dll": {"locale": "id"}, "zh/Hub.Mobile.resources.dll": {"locale": "zh"}, "es/Hub.Mobile.resources.dll": {"locale": "es"}}}, "Acr.UserDialogs/7.2.0.564": {"runtime": {"lib/netstandard2.0/Acr.UserDialogs.dll": {"assemblyVersion": "7.2.0.0", "fileVersion": "7.2.0.0"}}}, "AutoMapper/10.1.1": {"dependencies": {"Microsoft.CSharp": "4.7.0", "System.Reflection.Emit": "4.7.0"}, "runtime": {"lib/netstandard2.0/AutoMapper.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.1.1.0"}}}, "dotMorten.Xamarin.Forms.AutoSuggestBox/1.1.1": {"dependencies": {"Xamarin.Forms": "5.0.0.2401"}, "runtime": {"lib/netstandard2.0/dotMorten.Xamarin.Forms.AutoSuggestBox.dll": {"assemblyVersion": "1.1.1.0", "fileVersion": "1.1.1.0"}}}, "FluentValidation/11.0.2": {"runtime": {"lib/netstandard2.0/FluentValidation.dll": {"assemblyVersion": "11.0.0.0", "fileVersion": "11.0.2.0"}}}, "Microsoft.CSharp/4.7.0": {"runtime": {"lib/netstandard2.0/Microsoft.CSharp.dll": {"assemblyVersion": "4.0.5.0", "fileVersion": "4.700.19.56404"}}}, "Microsoft.Extensions.Caching.Abstractions/1.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "1.0.0", "System.Collections": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.20622"}}}, "Microsoft.Extensions.Caching.Memory/1.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "1.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "System.Linq": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.20622"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/1.0.0": {"dependencies": {"System.ComponentModel": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.1.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0"}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.20622"}}}, "Microsoft.Extensions.Logging.Abstractions/1.0.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.Concurrent": "4.0.12", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}, "runtime": {"lib/netstandard1.1/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.20622"}}}, "Microsoft.Extensions.Options/1.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.Primitives": "1.0.0", "System.ComponentModel": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.1.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.20622"}}}, "Microsoft.Extensions.Primitives/1.0.0": {"dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.20622"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "NETStandard.Library/2.0.3": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0"}}, "Newtonsoft.Json/9.0.1": {"dependencies": {"Microsoft.CSharp": "4.7.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Dynamic.Runtime": "4.0.11", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Serialization.Primitives": "4.1.1", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.0.11"}, "runtime": {"lib/netstandard1.0/Newtonsoft.Json.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.1.19813"}}}, "NLog/5.0.0": {"runtime": {"lib/netstandard2.0/NLog.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.1032"}}}, "Plugin.Permissions/6.0.1": {"dependencies": {"Xamarin.Essentials": "1.6.1"}, "runtime": {"lib/netstandard2.0/Plugin.Permissions.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Rg.Plugins.Popup/2.1.0": {"dependencies": {"Xamarin.Forms": "5.0.0.2401"}, "runtime": {"lib/netstandard2.0/Rg.Plugins.Popup.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "2.1.0.0"}}}, "sqlite-net-pcl/1.8.116": {"dependencies": {"SQLitePCLRaw.bundle_green": "2.0.4"}, "runtime": {"lib/netstandard2.0/SQLite-net.dll": {"assemblyVersion": "1.8.116.0", "fileVersion": "1.8.116.0"}}}, "SQLitePCLRaw.bundle_green/2.0.4": {"dependencies": {"SQLitePCLRaw.core": "2.0.4", "SQLitePCLRaw.lib.e_sqlite3": "2.0.4", "SQLitePCLRaw.provider.e_sqlite3": "2.0.4"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.0.4.976", "fileVersion": "2.0.4.976"}}}, "SQLitePCLRaw.core/2.0.4": {"dependencies": {"System.Memory": "4.5.3"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.0.4.976", "fileVersion": "2.0.4.976"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.0.4": {}, "SQLitePCLRaw.provider.e_sqlite3/2.0.4": {"dependencies": {"SQLitePCLRaw.core": "2.0.4"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.0.4.976", "fileVersion": "2.0.4.976"}}}, "System.Buffers/4.4.0": {"runtime": {"lib/netstandard2.0/System.Buffers.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.6.25519.3"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.0.12": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Collections.Concurrent.dll": {"assemblyVersion": "4.0.12.0", "fileVersion": "1.0.24212.1"}}}, "System.ComponentModel/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.ComponentModel.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.6.24705.1"}}}, "System.ComponentModel.Annotations/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.ComponentModel": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.4/System.ComponentModel.Annotations.dll": {"assemblyVersion": "4.1.1.0", "fileVersion": "4.6.24705.1"}}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.Tools/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.Tracing/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Dynamic.Runtime/4.0.11": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Dynamic.Runtime.dll": {"assemblyVersion": "4.0.11.0", "fileVersion": "1.0.24212.1"}}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.6.24705.1"}}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "runtime": {"lib/netstandard1.6/System.Linq.dll": {"assemblyVersion": "4.1.1.0", "fileVersion": "4.6.24705.1"}}}, "System.Linq.Expressions/4.1.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Emit.Lightweight": "4.0.1", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.6/System.Linq.Expressions.dll": {"assemblyVersion": "4.1.0.0", "fileVersion": "1.0.24212.1"}}}, "System.Memory/4.5.3": {"dependencies": {"System.Buffers": "4.4.0", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.2"}, "runtime": {"lib/netstandard2.0/System.Memory.dll": {"assemblyVersion": "4.0.1.1", "fileVersion": "4.6.27617.2"}}}, "System.Numerics.Vectors/4.5.0": {"runtime": {"lib/netstandard2.0/System.Numerics.Vectors.dll": {"assemblyVersion": "4.1.4.0", "fileVersion": "4.6.26515.6"}}}, "System.ObjectModel/4.0.12": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.ObjectModel.dll": {"assemblyVersion": "4.0.12.0", "fileVersion": "1.0.24212.1"}}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.7.0": {"dependencies": {"System.Reflection.Emit.ILGeneration": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Reflection.Emit.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.700.19.56404"}}}, "System.Reflection.Emit.ILGeneration/4.7.0": {"runtime": {"lib/netstandard2.0/System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.700.19.56404"}}}, "System.Reflection.Emit.Lightweight/4.0.1": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "4.0.1.0", "fileVersion": "1.0.24212.1"}}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.5/System.Reflection.TypeExtensions.dll": {"assemblyVersion": "4.1.1.0", "fileVersion": "4.6.24705.1"}}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "4.0.4.1", "fileVersion": "4.6.26919.2"}}}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.Serialization.Primitives/4.1.1": {"dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "4.1.1.0", "fileVersion": "1.0.24212.1"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.6/System.Text.RegularExpressions.dll": {"assemblyVersion": "4.1.1.0", "fileVersion": "4.6.24705.1"}}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Threading.dll": {"assemblyVersion": "4.0.12.0", "fileVersion": "4.6.24705.1"}}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "4.1.0.0", "fileVersion": "4.6.24705.1"}}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Xml.ReaderWriter.dll": {"assemblyVersion": "4.1.0.0", "fileVersion": "4.6.24705.1"}}}, "System.Xml.XDocument/4.0.11": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.0.1", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Xml.XDocument.dll": {"assemblyVersion": "4.0.11.0", "fileVersion": "1.0.24212.1"}}}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Xml.XmlDocument.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.6.24705.1"}}}, "System.Xml.XmlSerializer/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Xml.XmlSerializer.dll": {"assemblyVersion": "4.0.12.0", "fileVersion": "4.6.24705.1"}}}, "WebApiClient.JIT/1.1.4": {"dependencies": {"Microsoft.Extensions.Caching.Memory": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0", "Newtonsoft.Json": "9.0.1", "System.ComponentModel.Annotations": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Xml.XmlSerializer": "4.3.0"}, "runtime": {"lib/netstandard2.0/WebApiClient.JIT.dll": {"assemblyVersion": "1.1.4.0", "fileVersion": "1.1.4.0"}}}, "Xam.Plugin.Geolocator/4.5.0.6": {"runtime": {"lib/netstandard2.0/Plugin.Geolocator.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Xamarin.Essentials/1.6.1": {"dependencies": {"System.Numerics.Vectors": "4.5.0"}, "runtime": {"lib/netstandard2.0/Xamarin.Essentials.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Xamarin.Forms/5.0.0.2401": {"runtime": {"lib/netstandard2.0/Xamarin.Forms.Core.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "5.0.0.905"}, "lib/netstandard2.0/Xamarin.Forms.Platform.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "5.0.0.905"}, "lib/netstandard2.0/Xamarin.Forms.Xaml.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "5.0.0.905"}}}, "Hub.Mobile.ApiClient/1.0.0": {"dependencies": {"Hub.Mobile.Interface": "1.0.0", "WebApiClient.JIT": "1.1.4", "Xamarin.Forms": "5.0.0.2401"}, "runtime": {"Hub.Mobile.ApiClient.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}, "Hub.Mobile.CommonControl/1.0.0": {"dependencies": {"Xamarin.Forms": "5.0.0.2401"}, "runtime": {"Hub.Mobile.CommonControl.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}, "Hub.Mobile.Const/1.0.0": {"runtime": {"Hub.Mobile.Const.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}, "Hub.Mobile.Converter/1.0.0": {"dependencies": {"Xamarin.Forms": "5.0.0.2401"}, "runtime": {"Hub.Mobile.Converter.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}, "Hub.Mobile.DAL/1.0.0": {"dependencies": {"Hub.Mobile.Const": "1.0.0", "sqlite-net-pcl": "1.8.116"}, "runtime": {"Hub.Mobile.DAL.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}, "Hub.Mobile.Interface/1.0.0": {"dependencies": {"AutoMapper": "10.1.1", "Hub.Mobile.Const": "1.0.0", "Hub.Mobile.Model": "1.0.0", "Xamarin.Forms": "5.0.0.2401"}, "runtime": {"Hub.Mobile.Interface.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}, "Hub.Mobile.Model/1.0.0": {"dependencies": {"Xamarin.Forms": "5.0.0.2401"}, "runtime": {"Hub.Mobile.Model.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}, "Hub.Mobile.Utility/1.0.0": {"dependencies": {"NLog": "5.0.0", "Plugin.Permissions": "6.0.1", "Xam.Plugin.Geolocator": "4.5.0.6"}, "runtime": {"Hub.Mobile.Utility.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"Hub.Mobile/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Acr.UserDialogs/7.2.0.564": {"type": "package", "serviceable": true, "sha512": "sha512-RH+DT3EO/l12EyvOlhhilFizbVMOervE6bepyTccLlSWS+7m828huXefLBYgJ31PGCdiShteXIIkeIdUTRF4iQ==", "path": "acr.userdialogs/7.2.0.564", "hashPath": "acr.userdialogs.7.2.0.564.nupkg.sha512"}, "AutoMapper/10.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-uMgbqOdu9ZG5cIOty0C85hzzayBH2i9BthnS5FlMqKtMSHDv4ts81a2jS1VFaDBVhlBeIqJ/kQKjQY95BZde9w==", "path": "automapper/10.1.1", "hashPath": "automapper.10.1.1.nupkg.sha512"}, "dotMorten.Xamarin.Forms.AutoSuggestBox/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-VQEDmsVvw5ksEf1FrUdoeUuLZv6LDwDpP8kIo+XUGnlAbWTiSEIrBInEbH8+5fgxqLslKYPw9mVdppHb6j6rFw==", "path": "dotmorten.xamarin.forms.autosuggestbox/1.1.1", "hashPath": "dotmorten.xamarin.forms.autosuggestbox.1.1.1.nupkg.sha512"}, "FluentValidation/11.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-ik/xogKqBmZWIURMXNEbugJlKka6iV9a4qckmKDp7sHSwIfgkZ/ewzCuAeUTkFdVZujfDW6mCLMjrrR6GG4xLQ==", "path": "fluentvalidation/11.0.2", "hashPath": "fluentvalidation.11.0.2.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IxlFDVOchL6tdR05bk7EiJvMtvZrVkZXBhkbXqc3GxOHOrHFGcN+92WoWFPeBpdpy8ot/Px5ZdXzt7k+2n1Bdg==", "path": "microsoft.extensions.caching.abstractions/1.0.0", "hashPath": "microsoft.extensions.caching.abstractions.1.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6+7zTufCnZ+tfrUo7RbIRR3LB0BxwOwxfXuo0IbLyIvgoToGpWuz5wYEDfCYNOvpig9tY8FA0I1uRHYmITMXMQ==", "path": "microsoft.extensions.caching.memory/1.0.0", "hashPath": "microsoft.extensions.caching.memory.1.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+XwaNo3o9RhLQhUnnOBCaukeRi1X9yYc0Fzye9RlErSflKZdw0VgHtn6rvKo0FTionsW0x8QVULhKH+nkqVjQA==", "path": "microsoft.extensions.dependencyinjection.abstractions/1.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.1.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-wHT6oY50q36mAXBRKtFaB7u07WxKC5u2M8fi3PqHOOnHyUo9gD0u1TlCNR8UObHQxKMYwqlgI8TLcErpt29n8A==", "path": "microsoft.extensions.logging.abstractions/1.0.0", "hashPath": "microsoft.extensions.logging.abstractions.1.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SdP3yPKF++JTkoa91pBDiE70uQkR/gdXWzOnMPbSj+eOqY1vgY+b8RVl+gh7TrJ2wlCK2QqnQtvCQlPPZRK36w==", "path": "microsoft.extensions.options/1.0.0", "hashPath": "microsoft.extensions.options.1.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3q2vzfKEDjL6JFkRpk5SFA3zarYsO6+ZYgoucNImrUMzDn0mFbEOL5p9oPoWiypwypbJVVjWTf557bXZ0YFLig==", "path": "microsoft.extensions.primitives/1.0.0", "hashPath": "microsoft.extensions.primitives.1.0.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "NETStandard.Library/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-st47PosZSHrjECdjeIzZQbzivYBJFv6P2nv4cj2ypdI204DO+vZ7l5raGMiX4eXMJ53RfOIg+/s4DHVZ54Nu2A==", "path": "netstandard.library/2.0.3", "hashPath": "netstandard.library.2.0.3.nupkg.sha512"}, "Newtonsoft.Json/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-U82mHQSKaIk+lpSVCbWYKNavmNH1i5xrExDEquU1i6I5pV6UMOqRnJRSlKO3cMPfcpp0RgDY+8jUXHdQ4IfXvw==", "path": "newtonsoft.json/9.0.1", "hashPath": "newtonsoft.json.9.0.1.nupkg.sha512"}, "NLog/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OG9UQhhSnF31k1OImuZ3ogvJ+ktMI+P3QYhwA4cg3Fg5wznYS7PVQMzFglY2Bf4dHTYR/8CrNVgO7dFTfmvF2w==", "path": "nlog/5.0.0", "hashPath": "nlog.5.0.0.nupkg.sha512"}, "Plugin.Permissions/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-na/0FNhyKmSicANWZMLSM0y/FW2AvLoawX3lsvBpELwP15sdJRyuBhRKk1OQjRoNrmTIewcON1tJFyydjMGWcA==", "path": "plugin.permissions/6.0.1", "hashPath": "plugin.permissions.6.0.1.nupkg.sha512"}, "Rg.Plugins.Popup/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-ca<PERSON>jkvimzpUqk4pO/WSFn3uOX43uZNW5rPWCx7eXkOlGG9Br90MTwNVS1nxhtyypB69uUe/0GQPEC3rz+kscbA==", "path": "rg.plugins.popup/2.1.0", "hashPath": "rg.plugins.popup.2.1.0.nupkg.sha512"}, "sqlite-net-pcl/1.8.116": {"type": "package", "serviceable": true, "sha512": "sha512-W0NuwAOVVAR9LP4eZwNBIrim1p3EN7t8iNfSHXEhtzKAd4YyItekoQ8NyWYs4faVSrN2KZr/P5u4hycCjKKexg==", "path": "sqlite-net-pcl/1.8.116", "hashPath": "sqlite-net-pcl.1.8.116.nupkg.sha512"}, "SQLitePCLRaw.bundle_green/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-ubFgOHDmtTcq2LU7Ss10yHnFVMm2rFVr65Ggi+Mh514KjGx4pal98P2zSvZtWf3wbtJw6G1InBgeozBtnpEfKQ==", "path": "sqlitepclraw.bundle_green/2.0.4", "hashPath": "sqlitepclraw.bundle_green.2.0.4.nupkg.sha512"}, "SQLitePCLRaw.core/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-4XlDZpDAsboMD6qZQcz9AaKblKDUTVHF+8f3lvbP7QjoqSRr2Xc0Lm34IK2pjRIYnyFLhI3yOJ5YWfOiCid2yg==", "path": "sqlitepclraw.core/2.0.4", "hashPath": "sqlitepclraw.core.2.0.4.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-oetvmtDZOE4Nnrtxd8Trapl9geBiu0rDCUXff46qGYjnUwzaU1mZ3OHnfR402tl32rx8gBWg3n5OBRaPJRbsGw==", "path": "sqlitepclraw.lib.e_sqlite3/2.0.4", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.0.4.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-YiqCw+PfiPIuX2aZnEqt1ya5ldGm4ParIqzaRXtztDu8mdzZxOXVYOrKZazXg6nbwYbCP1H5mOYGmBFQ2W0M5g==", "path": "sqlitepclraw.provider.e_sqlite3/2.0.4", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.0.4.nupkg.sha512"}, "System.Buffers/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-AwarXzzoDwX6BgrhjoJsk6tUezZEozOT5Y9QKF94Gl4JK91I4PIIBkBco9068Y9/Dra8Dkbie99kXB8+1BaYKw==", "path": "system.buffers/4.4.0", "hashPath": "system.buffers.4.4.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-2gBcbb3drMLgxlI0fBfxMA31ec6AEyYCHygGse4vxceJan8mRIWeKJ24BFzN7+bi/NFTgdIgufzb94LWO5EERQ==", "path": "system.collections.concurrent/4.0.12", "hashPath": "system.collections.concurrent.4.0.12.nupkg.sha512"}, "System.ComponentModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "path": "system.componentmodel/4.3.0", "hashPath": "system.componentmodel.4.3.0.nupkg.sha512"}, "System.ComponentModel.Annotations/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SY2RLItHt43rd8J9D8M8e8NM4m+9WLN2uUd9G0n1I4hj/7w+v3pzK6ZBjexlG1/2xvLKQsqir3UGVSyBTXMLWA==", "path": "system.componentmodel.annotations/4.3.0", "hashPath": "system.componentmodel.annotations.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-xBfJ8pnd4C17dWaC9FM6aShzbJcRNMChUMD42I6772KGGrqaFdumwhn9OdM68erj1ueNo3xdQ1EwiFjK5k8p0g==", "path": "system.diagnostics.tools/4.0.1", "hashPath": "system.diagnostics.tools.4.0.1.nupkg.sha512"}, "System.Diagnostics.Tracing/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-vDN1PoMZCkkdNjvZLql592oYJZgS7URcJzJ7bxeBgGtx5UtR5leNm49VmfHGqIffX4FKacHbI3H6UyNSHQknBg==", "path": "system.diagnostics.tracing/4.1.0", "hashPath": "system.diagnostics.tracing.4.1.0.nupkg.sha512"}, "System.Dynamic.Runtime/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-db34f6LHYM0U0JpE+sOmjar27BnqTVkbLJhgfwMpTdgTigG/Hna3m2MYVwnFzGGKnEJk2UXFuoVTr8WUbU91/A==", "path": "system.dynamic.runtime/4.0.11", "hashPath": "system.dynamic.runtime.4.0.11.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-I+y02iqkgmCAyfbqOmSDOgqdZQ5tTj80Akm5BPSS8EeB0VGWdy6X1KCoYe8Pk6pwDoAKZUOdLVxnTJcExiv5zw==", "path": "system.linq.expressions/4.1.0", "hashPath": "system.linq.expressions.4.1.0.nupkg.sha512"}, "System.Memory/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "path": "system.memory/4.5.3", "hashPath": "system.memory.4.5.3.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.ObjectModel/4.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-tAgJM1xt3ytyMoW4qn4wIqgJYm7L7TShRZG4+Q4Qsi2PCcj96pXN7nRywS9KkB3p/xDUjc2HSwP9SROyPYDYKQ==", "path": "system.objectmodel/4.0.12", "hashPath": "system.objectmodel.4.0.12.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "path": "system.reflection.emit/4.7.0", "hashPath": "system.reflection.emit.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-AucBYo3DSI0IDxdUjKksBcQJXPHyoPyrCXYURW1WDsLI4M65Ar/goSHjdnHOAY9MiYDNKqDlIgaYm+zL2hA1KA==", "path": "system.reflection.emit.ilgeneration/4.7.0", "hashPath": "system.reflection.emit.ilgeneration.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-sSzHHXueZ5Uh0OLpUQprhr+ZYJrLPA2Cmr4gn0wj9+FftNKXx8RIMKvO9qnjk2ebPYUjZ+F2ulGdPOsvj+MEjA==", "path": "system.reflection.emit.lightweight/4.0.1", "hashPath": "system.reflection.emit.lightweight.4.0.1.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-wprSFgext8cwqymChhrBLu62LMg/1u92bU+VOwyfBimSPVFXtsNqEWC92Pf9ofzJFlk4IHmJA75EDJn1b2goAQ==", "path": "system.runtime.compilerservices.unsafe/4.5.2", "hashPath": "system.runtime.compilerservices.unsafe.4.5.2.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Primitives/4.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-HZ6Du5QrTG8MNJbf4e4qMO3JRAkIboGT5Fk804uZtg3Gq516S7hAqTm2UZKUHa7/6HUGdVy3AqMQKbns06G/cg==", "path": "system.runtime.serialization.primitives/4.1.1", "hashPath": "system.runtime.serialization.primitives.4.1.1.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-npvJkVKl5rKXrtl1Kkm6OhOUaYGEiF9wFbppFRWSMoApKzt2PiPHT2Bb8a5sAWxprvdOAtvaARS9QYMznEUtug==", "path": "system.threading.tasks.extensions/4.3.0", "hashPath": "system.threading.tasks.extensions.4.3.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-Mk2mKmPi0nWaoiYeotq1dgeNK1fqWh61+EK+w4Wu8SWuTYLzpUnschb59bJtGywaPq7SmTuPf44wrXRwbIrukg==", "path": "system.xml.xdocument/4.0.11", "hashPath": "system.xml.xdocument.4.0.11.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlSerializer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-MYoTCP7EZ98RrANESW05J5ZwskKDoN0AuZ06ZflnowE50LTpbR5yRg3tHckTVm5j/m47stuGgCrCHWePyHS70Q==", "path": "system.xml.xmlserializer/4.3.0", "hashPath": "system.xml.xmlserializer.4.3.0.nupkg.sha512"}, "WebApiClient.JIT/1.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-uTAXUdt3iUhURhB6Wlk6ccEEYHXmysYSz/VN/KO++VsJ9czTaeMEGOxjfvp10OnH+IMWbDIs9hvDdfdEXCnSkQ==", "path": "webapiclient.jit/1.1.4", "hashPath": "webapiclient.jit.1.1.4.nupkg.sha512"}, "Xam.Plugin.Geolocator/4.5.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Ro7peBDxsDXPc7J7yLx0ekAcXIfVTAXj/rXiBKiTgZ4Lm7KZJIU8PXOCcI4JztzT3MQrL8Rmka48il/ix0pwoA==", "path": "xam.plugin.geolocator/4.5.0.6", "hashPath": "xam.plugin.geolocator.4.5.0.6.nupkg.sha512"}, "Xamarin.Essentials/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-gG1Bp4j4bpJQ0dp9MeBJQJ9c8xULauIlhc7Cx5Qw4J2K1c+4Glq5n+gkb9Pm0UA/CevXVqLK5ZXmZkuYI52FgA==", "path": "xamarin.essentials/1.6.1", "hashPath": "xamarin.essentials.1.6.1.nupkg.sha512"}, "Xamarin.Forms/5.0.0.2401": {"type": "package", "serviceable": true, "sha512": "sha512-fk/V8QWjeyo8UXcsqsNRMd6o1H5PBfyXYm97nxvhMsJIdeqDgfRWyIsSurM8uLGLQGdQP23R+hHj7vcTSo2UjQ==", "path": "xamarin.forms/5.0.0.2401", "hashPath": "xamarin.forms.5.0.0.2401.nupkg.sha512"}, "Hub.Mobile.ApiClient/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Hub.Mobile.CommonControl/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Hub.Mobile.Const/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Hub.Mobile.Converter/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Hub.Mobile.DAL/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Hub.Mobile.Interface/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Hub.Mobile.Model/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Hub.Mobile.Utility/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}