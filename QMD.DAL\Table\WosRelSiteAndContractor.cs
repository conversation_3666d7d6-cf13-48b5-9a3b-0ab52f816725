﻿using Common.DAL;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace QMD.DAL.Table
{
    [Index(nameof(SubconCode), nameof(WbsCode), IsUnique = true)]
    public class WosRelSiteAndContractor: EntityBase
    {
        [MaxLength(16)]
        [Required]
        public string SubconCode { get; set; }
        [MaxLength(64)]
        [Required]
        public string WbsCode { get; set; }
        public DateTime? CreateTime { get; set; }
        public bool ImportedToQmd { get; set; }
    }
}
