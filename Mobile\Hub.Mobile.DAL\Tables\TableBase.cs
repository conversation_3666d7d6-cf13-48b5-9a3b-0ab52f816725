﻿using SQLite;
using System;
using System.Collections.Generic;
using System.Text;

namespace Hub.Mobile.DAL.Tables
{
    public class TableBase
    {
        public TableBase()
        {
            this.Id = Guid.NewGuid().ToString("N");
        }
        [PrimaryKey]
        public string Id { get; set; }
        public DateTime? CreatedTime { get; set; }
        public DateTime? ModifiedTime { get; set; }
    }
}
