﻿using Flurl;
using Microsoft.CSharp;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using NPOI.Util;
using NPOI.XSSF.Streaming.Values;
using OfficeOpenXml;
using Spire.Xls;
using System;
using System.CodeDom.Compiler;
using System.Collections;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net.Sockets;
using System.Reflection;
using System.Reflection.Emit;
using System.Text;
using System.Threading.Tasks;

namespace Common.Utility
{
    public static class UtilHelper
    {
        /// <summary>
        /// 判断集合是否为空
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static bool IsNullOrWhiteSpace(this IList value)
        {
            return value == null || value.Count == 0;
        }

        public static bool IsNullWithTrimSpace(this List<string> value)
        {
            if (value != null)
            {
                List<string> rmItems = new List<string>();
                value.ForEach(x => {
                    if (x==null || x.Trim() == "")
                    {
                        rmItems.Add(x);
                    }
                });
                rmItems.ForEach(x =>
                {
                    value.Remove(x);
                });
            }
            return value == null || value.Count == 0;
        }


        public static double ToDouble(this string value)
        {
            double result = 0;
            if (!double.TryParse(value,out result))
            {
                result = 0;
            }
            return result;
        }

        /// <summary>
        /// 转成百分数，带%符号,会四舍五入
        /// </summary>
        /// <param name="source"></param>
        /// <param name="total"></param>
        /// <param name="decimalPlace"></param>
        /// <returns></returns>
        public static string ToPercent(this decimal source, decimal total, int decimalPlace = 2)
        {
            decimal tempValue = 0;
            if (total == 0)
            {
                tempValue = 0;
            }
            else
            {
                tempValue = (source * 100) / total;
            }
            string format = "0";
            for (int i = 0; i < decimalPlace; i++)
            {
                if (i == 0)
                    format += ".0";
                else
                    format += "0";
            }
            return tempValue.ToString(format) + "%";
        }

        #region JSON处理

        public static List<T> JsonToList<T>(this string jsonStr)
        {
            List<T> result = new List<T>();
            try
            {
                if (!string.IsNullOrEmpty(jsonStr))
                {
                    result = Newtonsoft.Json.JsonConvert.DeserializeObject<List<T>>(jsonStr);
                }
            }
            catch (Exception ex)
            {
                result = null;
            }
            return result;
        }
        //对象转换成json字符串
        public static string ToJson<T>(this T obj)
        {
            string result = string.Empty;
            try
            {
                result = Newtonsoft.Json.JsonConvert.SerializeObject(obj);
            }
            catch (Exception ex)
            {
                result = string.Empty;
            }
            return result;
        }
        #endregion

        #region 生成随机数
        private static List<char> BaseIntCollection = new List<char>() { '1', '2', '3', '4', '5', '6', '7', '8', '9' };
        private static List<char> BaseLowerCharCollection = new List<char>() { 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'j', 'k', 'm', 'n', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z' };
        private static List<char> BaseUpperCharCollection = new List<char>() { 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z' };
        public static object randomloker = new object();
        /// <summary>
        /// 取随机数
        /// </summary>
        /// <param name="profix">前缀</param>
        /// <param name="length">随机数长度</param>
        /// <param name="type">随机数类型（0数字大小写字母混合，1纯数字，2纯小写字母，3纯大写字母，4大小写字母混合，5数字小写字母混合，6数字大写字母混合）</param>
        /// <returns></returns>
        public static string GetRandomCode(string profix, int length, int type, bool isRepeat = true)
        {
            lock (randomloker)
            {
                StringBuilder sb = new StringBuilder();
                int RLenth = length;
                if (!string.IsNullOrEmpty(profix))
                {
                    RLenth = length - profix.Length;
                    sb.Append(profix);
                }
                List<char> allChar = BaseIntCollection.Concat(BaseLowerCharCollection).Concat(BaseUpperCharCollection).ToList();
                try
                {
                    List<int> hasPosList = new List<int>();
                    for (int i = 0; i < RLenth; i++)
                    {
                        Random randomNum = new Random(Guid.NewGuid().GetHashCode());
                        switch (type)
                        {
                            case 1:
                                allChar = BaseIntCollection;
                                break;
                            case 2:
                                allChar = BaseLowerCharCollection;
                                break;
                            case 3:
                                allChar = BaseUpperCharCollection;
                                break;
                            case 4:
                                allChar = BaseLowerCharCollection.Concat(BaseUpperCharCollection).ToList();
                                break;
                            case 5:
                                allChar = BaseIntCollection.Concat(BaseLowerCharCollection).ToList();
                                break;
                            case 6:
                                allChar = BaseIntCollection.Concat(BaseUpperCharCollection).ToList();
                                break;
                            default:
                                break;
                        }
                        int tempNum = randomNum.Next(0, allChar.Count());
                        while (!isRepeat && hasPosList.Contains(tempNum))
                        {
                            tempNum = randomNum.Next(0, allChar.Count);
                        }
                        hasPosList.Add(tempNum);
                        sb.Append(allChar[randomNum.Next(0, allChar.Count())]);
                    }
                }
                catch (Exception)
                {
                    for (int i = 0; i < RLenth; i++)
                    {
                        Random randomNum = new Random(Guid.NewGuid().GetHashCode());
                        sb.Append(allChar[randomNum.Next(0, allChar.Count())]);
                    }
                }
                return sb.ToString();
            }
        }
        #endregion

        #region 构建动态类,当前net版本下不支持

        /// <summary>
        /// 创建属性
        /// </summary>
        /// <param name="propertyType">属性基础类型(byte/int/string/float/double/bool等)和List基础类型</param>
        /// <param name="propertyName">属性名称</param>
        /// <param name="propertyDefaultValue">属性初始值</param>
        /// <returns></returns>
        private static string PropertyString(string propertyType, string propertyName, string propertyDefaultValue)
        {
            StringBuilder sbproperty = new StringBuilder();
            sbproperty.Append(" private " + propertyType + "  _" + propertyName + "   =  " + propertyDefaultValue + ";\n");
            sbproperty.Append(" public " + propertyType + " " + propertyName + "\n");
            sbproperty.Append(" {\n");
            sbproperty.Append(" get{   return   _" + propertyName + ";}   \n");
            sbproperty.Append(" set{   _" + propertyName + "   =   value;   }\n");
            sbproperty.Append(" }\n");
            return sbproperty.ToString();
        }
        /// <summary>
        /// 创建动态类
        /// </summary>
        /// <param name="className">动态类名字</param>
        /// <param name="propertyList">属性列表</param>
        /// <returns></returns>
        public static Assembly Newassembly(string className, List<string[]> propertyList)
        {
            //创建编译器实例。   
            CSharpCodeProvider provider = new CSharpCodeProvider();
            //设置编译参数。   
            CompilerParameters paras = new CompilerParameters();
            //编译器生成的临时文件，参数2为true，放置为当前文件夹下，false则放入windows的temp文件夹下
            paras.TempFiles = new TempFileCollection(".", false);
            //如果为true，则生成exe文件，false会生成临时dll文件
            paras.GenerateExecutable = false;
            paras.GenerateInMemory = true;
            //创建动态代码。   
            StringBuilder classsource = new StringBuilder();
            //float、bool等类型需要引用System
            classsource.Append("using System;\n");
            //List需要引用System.Collections.Generic
            classsource.Append("using System.Collections.Generic;\n");
            classsource.Append("public class " + className + " \n");
            classsource.Append("{\n");

            try
            {
                //创建属性。   
                for (int i = 0; i < propertyList.Count; i++)
                {
                    classsource.Append(PropertyString(propertyList[i][0], propertyList[i][1], propertyList[i][2]));
                }
                classsource.Append("}");
                System.Diagnostics.Debug.WriteLine(classsource.ToString());
                //编译代码。   
                CompilerResults result = provider.CompileAssemblyFromSource(paras, classsource.ToString());
                //获取编译后的程序集。   
                Assembly assembly = result.CompiledAssembly;

                return assembly;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine(ex.Message);
                return null;
            }

        }
        /// <summary>
        /// 给属性赋值
        /// </summary>
        /// <param name="objclass">先进行dynamic objclass = assembly.CreateInstance(className)，得到的objclass</param>
        /// <param name="propertyname">属性名称</param>
        /// <param name="value">属性值</param>
        public static void ReflectionSetValue(object objclass, string propertyname, object value)
        {
            PropertyInfo[] infos = objclass.GetType().GetProperties();

            try
            {
                foreach (PropertyInfo info in infos)
                {
                    if (info.Name == propertyname && info.CanWrite)
                    {
                        info.SetValue(objclass, value, null);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine(ex.Message);
            }

        }
        /// <summary>
        /// 得到属性值
        /// </summary>
        /// <param name="objclass">先进行dynamic objclass = assembly.CreateInstance(className)，得到的objclass</param>
        /// <param name="propertyname">属性名称</param>
        /// <returns>属性值，是object类型，使用时记得转换</returns>
        public static object ReflectionGetValue(object objclass, string propertyname)
        {
            object result = null;
            PropertyInfo[] infos = objclass.GetType().GetProperties();
            try
            {
                foreach (PropertyInfo info in infos)
                {
                    if (info.Name == propertyname && info.CanRead)
                    {
                        System.Console.WriteLine(info.GetValue(objclass, null));
                        result = info.GetValue(objclass, null);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine(ex.Message);
                result = null;
            }

            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dict"></param>
        /// <returns></returns>
        public static dynamic ToDynamic(this Dictionary<string, object> dict)
        {
            var Propertys = dict.Keys.Select(x => new string[]{
                     "string",x,"null"
                }).ToList();
            //创建动态类，构建一个程序集
            string className = "XlsReport";
            Assembly assembly = Newassembly(className, Propertys);
            //构建动态类的实例
            dynamic objclass = assembly.CreateInstance(className);
            //给动态类属性赋值，byte/short/unshort/long/float要强制转换
            foreach (KeyValuePair<string,object> item in dict)
            {
                ReflectionSetValue(objclass, item.Key, item.Value);
            }
            return objclass;
        }

        #endregion


        #region 构建动态类，当前net版本下支持

        /// <summary>
        /// 根据字典生成动态类
        /// </summary>
        /// <param name="dict"></param>
        /// <returns></returns>
        public static Type CreateClass(this Dictionary<string, object> dict, string className = null)
        {
            List<string> keys = new List<string>(dict.Keys);
            if (string.IsNullOrEmpty(className))
            {
                className = GetRandomCode("_", 10, 2);
            }
            List<ClassProperty> properties = new List<ClassProperty>();
            foreach (KeyValuePair<string, object> kv in dict)
            {
                properties.Add(new ClassProperty() { PropName = kv.Key, PropType = kv.Value.GetType() });
            }
            Type dyClass = CreateDynamicClass(className, properties);
            return dyClass;
        }

        /// <summary>
        /// 获取动态类
        /// </summary>
        /// <param name="dict"></param>
        /// <param name="className"></param>
        /// <returns></returns>
        public static dynamic DicToDynamic(this Dictionary<string, object> dict, Type dyClass)
        {
            //List<string> keys = new List<string>(dict.Keys);
            //if (string.IsNullOrEmpty(className))
            //{
            //    className = GetRandomCode("_", 10, 2);
            //}
            //List<ClassProperty> properties = new List<ClassProperty>();
            //foreach (KeyValuePair<string, object> kv in dict)
            //{
            //    properties.Add(new ClassProperty() { PropName = kv.Key, PropType = kv.Value.GetType() });
            //}
            //Type dyClass = CreateDynamicClass(className, properties);
            object instance = Activator.CreateInstance(dyClass);
            dynamic result = Convert.ChangeType(instance, dyClass);
            //设置值
            PropertyInfo[] infos = result.GetType().GetProperties();
            foreach (PropertyInfo info in infos)
            {
                if (dict.Keys.Contains(info.Name))
                {
                    info.SetValue(result, dict[info.Name], null);
                }
            }
            return result;
        }

        /// <summary>
        /// 创建动态类相关信息
        /// </summary>
        /// <param name="className"></param>
        /// <param name="properties"></param>
        /// <returns></returns>
        public static Type CreateDynamicClass(string className, List<ClassProperty> properties)// string propName, Type propType
        {
            // 创建程序集
            AssemblyName assemblyName = new AssemblyName("Common.Utility");
            AssemblyBuilder assemblyBuilder = AssemblyBuilder.DefineDynamicAssembly(assemblyName, AssemblyBuilderAccess.Run);

            // 创建模块
            ModuleBuilder moduleBuilder = assemblyBuilder.DefineDynamicModule("DynamicModule");

            // 创建类型
            TypeBuilder typeBuilder = moduleBuilder.DefineType(className, TypeAttributes.Public);

            properties.ForEach(prop => {
                // 创建属性
                PropertyBuilder propertyBuilder = typeBuilder.DefineProperty(prop.PropName, PropertyAttributes.None, prop.PropType, null);
                // 创建字段
                FieldBuilder fieldBuilder = typeBuilder.DefineField("_" + prop.PropName, prop.PropType, FieldAttributes.Private);
                // 创建属性的getter和setter方法
                MethodBuilder getterBuilder = typeBuilder.DefineMethod("get_" + prop.PropName, MethodAttributes.Public | MethodAttributes.SpecialName | MethodAttributes.HideBySig, prop.PropType, Type.EmptyTypes);
                ILGenerator getterIL = getterBuilder.GetILGenerator();
                getterIL.Emit(OpCodes.Ldarg_0); // this
                getterIL.Emit(OpCodes.Ldfld, fieldBuilder);
                getterIL.Emit(OpCodes.Ret);

                MethodBuilder setterBuilder = typeBuilder.DefineMethod("set_" + prop.PropName, MethodAttributes.Public | MethodAttributes.SpecialName | MethodAttributes.HideBySig, null, new Type[] { prop.PropType });
                ILGenerator setterIL = setterBuilder.GetILGenerator();
                setterIL.Emit(OpCodes.Ldarg_0); // this
                setterIL.Emit(OpCodes.Ldarg_1); // value
                setterIL.Emit(OpCodes.Stfld, fieldBuilder);
                setterIL.Emit(OpCodes.Ret);

                // 关联getter和setter方法与属性
                propertyBuilder.SetGetMethod(getterBuilder);
                propertyBuilder.SetSetMethod(setterBuilder);

            });
            // 创建类型并返回
            return typeBuilder.CreateType();
        }

        /// <summary>
        /// 动态类属性数据
        /// </summary>
        public class ClassProperty
        {
            public string PropName { get; set; }

            public Type PropType { get; set; }
        }

        #endregion


        #region EPPLus

        /// <summary>
        /// 获取类的属性值
        /// </summary>
        /// <param name="obj">类</param>
        /// <param name="property">属性</param>
        /// <returns></returns>
        private static object GetPropertyValue(object obj, string property)
        {
            return obj.GetType().GetProperty(property).GetValue(obj);
        }

        /// <summary>
        /// 获取类的全部属性
        /// </summary>
        /// <param name="t"></param>
        /// <returns></returns>
        private static PropertyInfo[] GetProperties<T>(T t) where T : class, new()
        {
            PropertyInfo[] properties = t.GetType().GetProperties();
            return properties;
        }

        /// <summary>
        /// 导出Excel
        /// </summary>
        /// <param name="fileInfo">FileInfo</param>
        /// <param name="tList">数据</param>
        /// <returns></returns>
        public static void OutPutExcel<T>(FileInfo fileInfo, List<T> tList) where T : class, new()
        {
            //指定EPPlus使用非商业化许可证
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            using (ExcelPackage package = new ExcelPackage(fileInfo))
            {
                //工作簿
                ExcelWorksheet worksheet = package.Workbook.Worksheets.Add(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                //实体属性
                PropertyInfo[] properties = GetProperties(tList.FirstOrDefault());
                //填充表头
                for (int i = 1; i < properties.Length + 1; i++)
                {
                    worksheet.Cells[1, i].Value = properties[i - 1].Name;
                }
                //填充行(从第二行开始)
                for (int i = 2; i < tList.Count + 2; i++)
                {
                    //填充行内列
                    for (int j = 1; j < properties.Length + 1; j++)
                    {
                        var property = properties[j - 1].Name;
                        worksheet.Cells[i, j].Value = GetPropertyValue(tList[i - 2], property);
                    }
                }
                //列宽自适应
                worksheet.Cells.AutoFitColumns();
                //保存
                package.Save();
            }
        }


        /// <summary>
        /// 导出Excel
        /// </summary>
        /// <param name="fileInfo">FileInfo</param>
        /// <param name="tList">数据</param>
        /// <returns></returns>
        public static void DicOutPutExcel(FileInfo fileInfo, List<Dictionary<string,object>> tList)
        {
            //指定EPPlus使用非商业化许可证
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            using (ExcelPackage package = new ExcelPackage(fileInfo))
            {
                //工作簿
                ExcelWorksheet worksheet = package.Workbook.Worksheets.Add(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                //实体属性
                List<string> keys = new List<string>(tList[0].Keys);
                //填充表头
                for (int i = 1; i < keys.Count + 1; i++)
                {
                    worksheet.Cells[1, i].Value = keys[i - 1];
                }
                //填充行(从第二行开始)
                for (int i = 2; i < tList.Count + 2; i++)
                {
                    //填充行内列
                    for (int j = 1; j < keys.Count + 1; j++)
                    {
                        var dic = tList[i - 2];
                        worksheet.Cells[i, j].Value = dic[keys[j - 1]];
                    }
                }
                //列宽自适应
                worksheet.Cells.AutoFitColumns();
                //保存
                package.Save();
            }
        }

        #endregion

        public static BoolResultDto IsTelnetSuccess(string ip, int port)
        {
            BoolResultDto result = new BoolResultDto { Flag = false };
            try
            {
                TcpClient tcpClient = new TcpClient(ip, port);
                NetworkStream networkStream = tcpClient.GetStream();
                StreamWriter streamWriter = new StreamWriter(networkStream);
                StreamReader streamReader = new StreamReader(networkStream);
                var isConn = tcpClient.Connected;
                result.Flag = true;
            }
            catch (Exception ex)
            {
                result.Flag = false;
                result.Message = $"连接到远程服务器{ip}:{port}发生异常：{ex.Message}";
            }
            return result;
        }

        #region 字符串处理
        /// <summary>
        /// 生成随机字符串
        /// </summary>
        /// <param name="count">字符串长度</param>
        /// <returns></returns>
        public static string GetTicketRandomStr(int count)
        {
            string baseStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            Random random = new Random();
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < count; i++)
            {
                int number = random.Next(baseStr.Length);
                sb.Append(baseStr[number]);
            }
            return sb.ToString();
        }

        /// <summary>
        /// 深度克隆
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="source"></param>
        /// <returns></returns>
        public static T DeepClone<T>(this T source)
        {
            // Don't serialize a null object, simply return the default for that object
            if (Object.ReferenceEquals(source, null))
            {
                return default(T);
            }

            var deserializeSettings = new JsonSerializerSettings { ObjectCreationHandling = ObjectCreationHandling.Replace };
            var serializeSettings = new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore };
            return JsonConvert.DeserializeObject<T>(JsonConvert.SerializeObject(source, serializeSettings), deserializeSettings);
        }

        #endregion

    }
    /// <summary>
    /// bool类型的返回
    /// </summary>
    public class BoolResultDto
    {
        /// <summary>
        /// 返回的结果
        /// </summary>
        public bool Flag { get; set; }
        /// <summary>
        /// 返回的消息
        /// </summary>
        public string Message { get; set; }
    }

}
