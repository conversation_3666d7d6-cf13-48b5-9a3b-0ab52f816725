﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_tasksteptpl_columns : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "EffectiveEndTimeHoursOffset",
                table: "TaskStepTpls",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "EffectiveStartTimeHoursOffset",
                table: "TaskStepTpls",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "EffectiveTimeType",
                table: "TaskStepTpls",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EffectiveEndTimeHoursOffset",
                table: "TaskStepTpls");

            migrationBuilder.DropColumn(
                name: "EffectiveStartTimeHoursOffset",
                table: "TaskStepTpls");

            migrationBuilder.DropColumn(
                name: "EffectiveTimeType",
                table: "TaskStepTpls");
        }
    }
}
