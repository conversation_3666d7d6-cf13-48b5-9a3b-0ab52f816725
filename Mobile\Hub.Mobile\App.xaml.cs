﻿using Acr.UserDialogs;
using Hub.Mobile.ApiClient;
using Hub.Mobile.CommonControl;
using Hub.Mobile.Utility;
using NLog;
using System;
using System.Threading.Tasks;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace Hub.Mobile
{
    public partial class App : Application
    {
        private ILogger logger = LogManager.GetCurrentClassLogger();
        public App()
        {
            InitializeComponent();
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
            TaskScheduler.UnobservedTaskException += TaskScheduler_UnobservedTaskException;
            MobileGPSHelper.StartListenGPS();
            MobileContext.InitAsync();
            ApiConfig.Init();
            //MainPage = new NavigationPage(new TestPage());
            MainPage = new NavigationPage(new MainPage());
        }

        protected override void OnStart()
        {
        }

        protected override void OnSleep()
        {
        }

        protected override void OnResume()
        {
        }
        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            Exception exception = e.ExceptionObject as Exception;
            if (exception != null)
            {
                logger.Error(exception);
                UserDialogs.Instance.Alert($"{Mobile.Resources.ExceptionResources.ExceptionLevel_Error}:{exception?.Message}");
            }
        }
        private void TaskScheduler_UnobservedTaskException(object sender, UnobservedTaskExceptionEventArgs e)
        {
            if (e.Exception != null)
            {
                logger.Error(e.Exception);
                UserDialogs.Instance.Alert($"{Mobile.Resources.ExceptionResources.Exception_Thread} : {e.Exception?.Message}");
            }
        }
    }
}
