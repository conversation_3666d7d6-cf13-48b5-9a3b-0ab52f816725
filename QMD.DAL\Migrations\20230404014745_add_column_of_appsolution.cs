﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_column_of_appsolution : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "FirstDisName",
                table: "AppSolutions",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "FirstEnName",
                table: "AppSolutions",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "SecondDisName",
                table: "AppSolutions",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "SecondEnName",
                table: "AppSolutions",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ThirdDisName",
                table: "AppSolutions",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ThirdEnName",
                table: "AppSolutions",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "FirstDisName",
                table: "AppSolutions");

            migrationBuilder.DropColumn(
                name: "FirstEnName",
                table: "AppSolutions");

            migrationBuilder.DropColumn(
                name: "SecondDisName",
                table: "AppSolutions");

            migrationBuilder.DropColumn(
                name: "SecondEnName",
                table: "AppSolutions");

            migrationBuilder.DropColumn(
                name: "ThirdDisName",
                table: "AppSolutions");

            migrationBuilder.DropColumn(
                name: "ThirdEnName",
                table: "AppSolutions");
        }
    }
}
