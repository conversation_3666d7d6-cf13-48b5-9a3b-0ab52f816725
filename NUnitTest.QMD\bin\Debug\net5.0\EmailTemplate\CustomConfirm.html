﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title></title>
    <style type="text/css">
        p {
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <div>
        <p>%nickname%，您好：</p>
        <p>工单编号：%orderno%</p>
        <p>工单主题：%title%</p>
        <p>工单级别：%orderlevel%</p>
        <p>网络名称：%netname%</p>
        <p>产品型号：%productmodel%</p>
        <p>上报时间：%uploadtime%</p>
        <p>上报人：%uploaduser%</p>
        <p>任务名称：%taskname%</p>
        <p>任务要求：%taskask%</p>
        <p>任务链接：<a href="%tasklink%" target="_blank">%tasklink%</a></p>
        <p>授权码：<span style="font-size:32px; color:red">%authcode%</span></p>
        <p>
            <button id="btnPass" type="button" style="width: 175px; border: solid 1px #057ab8;border-radius:5px; height: 72px; color: #fff; font-size: 26px; background: #057ab8 repeat; margin-right: 30px;">授权</button>
            <button id="btnNoPass" type="button" style="width: 175px; border: solid 1px #057ab8; border-radius: 5px; height: 72px; color: #fff; font-size: 26px; background: #057ab8 repeat; margin-right: 30px;">不授权</button>
        </p>

        <p>
            <a id="abtnPass" style="display:inline-block; width: 175px; border: solid 1px #057ab8;border-radius:5px; height: 72px; color: #fff; font-size: 26px; background: #057ab8 repeat; margin-right: 30px;" href="javascript:Approval(true);">授权</a>
            <a id="abtnNoPass" style="display: inline-block; width: 175px; border: solid 1px #057ab8; border-radius: 5px; height: 72px; color: #fff; font-size: 26px; background: #057ab8 repeat; margin-right: 30px;" href="javascript:Approval(false);">不授权</a>
        </p>

    </div>
    <script type="text/javascript">
        //document.onload = function () {
        //    alert(111);
        //}
        window.onload = function () {
            var passEle = document.getElementById("btnPass");
            var noPassEle = document.getElementById("btnNoPass");
            passEle.addEventListener('click', function () {
                alert(222);
            });
            noPassEle.addEventListener('click', function () {
                alert(333);
            });
        }

        function Approval(isPass) {
            alert(222);
        }
    </script>
</body>
</html>