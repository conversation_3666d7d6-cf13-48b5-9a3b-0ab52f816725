﻿using Hub.Mobile.Interface;
using Hub.Mobile.Utility;
using Newtonsoft.Json;
using Plugin.Permissions;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Xamarin.Forms;

namespace Hub.Mobile.Plugin.Plugins
{
    public class MFile:IPlugin
    {
        public class FileOption
        {
            public string Url { get; set; }
            public string FileName { get; set; }
            public string Md5 { get; set; }
        }
        public async Task<ExecResult> SaveToLocal(string args)
        {
            FileOption option = JsonConvert.DeserializeObject<FileOption>(args);
            ExecResult result = new ExecResult() { IsString = true };
            if (string.IsNullOrWhiteSpace(option.Md5))
            {
                result.Success = false;
                result.Message = "md5 is empty";
                return result;
            }
            if (string.IsNullOrWhiteSpace(option.Url))
            {
                result.Success = false;
                result.Message = "download url is empty";
                return result;
            }
            string fileName = string.IsNullOrWhiteSpace(option.FileName) ? Path.GetFileName(new Uri(option.Url).AbsolutePath) : option.FileName;
            string saveToLocalPath = DependencyService.Get<IFile>().GetSaveToLocalDir();
            string newFileFullPath = Path.Combine(saveToLocalPath, fileName);
            if (File.Exists(newFileFullPath))
            {
                string existMd5 = FileHelper.GetMD5HashFromFile(newFileFullPath);
                if (string.Compare(option.Md5, existMd5, true) == 0)
                {
                    result.Result = newFileFullPath;
                    result.Success = true;
                    return result;
                }
            }
            string filePath = await Task.Run(() => { return DependencyService.Get<IDownload>().DownloadFile(option.Url, "", fileName); });
            result.Result = DependencyService.Get<IFile>().SaveToLocal(filePath);
            result.Success = true;
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }
            //if (await PermissionHelper.CheckPermission<StoragePermission>())
            //{
            //    string filePath = await Task.Run(() => { return DependencyService.Get<IDownload>().DownloadFile(option.Url, "", fileName); });
            //    result.Result = DependencyService.Get<IFile>().SaveToLocal(filePath);
            //    result.Success = true;
            //    if (File.Exists(filePath))
            //    {
            //        File.Delete(filePath);
            //    }
            //}
            //else
            //{
            //    result.Success = false;
            //    result.Message = "No Storage Permission";
            //}
            return result;
        }
    }

}
