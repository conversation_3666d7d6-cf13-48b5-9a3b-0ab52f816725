﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AcquireVerificationCode" xml:space="preserve">
    <value>Mendapatkan</value>
  </data>
  <data name="AcquireVerificationCodeFailed" xml:space="preserve">
    <value>Perolehan gagal</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>Menambahkan</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Alamat</value>
  </data>
  <data name="Alert" xml:space="preserve">
    <value>Peringatan</value>
  </data>
  <data name="AlreadyLatestVersion" xml:space="preserve">
    <value>Versi ini adalah yang terbaru</value>
  </data>
  <data name="Approved" xml:space="preserve">
    <value>Disetujui</value>
  </data>
  <data name="Area" xml:space="preserve">
    <value>Daerah</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Batal</value>
  </data>
  <data name="ChangePassword" xml:space="preserve">
    <value>Ganti kata Sandi</value>
  </data>
  <data name="CheckVersion" xml:space="preserve">
    <value>Periksa untuk versi yang terbaru</value>
  </data>
  <data name="Compressing" xml:space="preserve">
    <value>Mengkompres</value>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>Konfirmasi Kata Sandi Baru</value>
  </data>
  <data name="Cover" xml:space="preserve">
    <value>Sampul</value>
  </data>
  <data name="DateCreated" xml:space="preserve">
    <value>Tanggal Pembuatan</value>
  </data>
  <data name="DecompressCompleted" xml:space="preserve">
    <value>Dekompresi selesai</value>
  </data>
  <data name="DecompressFailed" xml:space="preserve">
    <value>Dekompresi gagal</value>
  </data>
  <data name="Decompressing" xml:space="preserve">
    <value>Proses dekompresi</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Menghapus</value>
  </data>
  <data name="DeleteConfirmMsg" xml:space="preserve">
    <value>Apakah kamu yakin untuk menghapus?</value>
  </data>
  <data name="DownloadFailed" xml:space="preserve">
    <value>unduhan gagal</value>
  </data>
  <data name="Downloading" xml:space="preserve">
    <value>mengunduh</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="EmailIncorrect" xml:space="preserve">
    <value>Email tidak sesuai</value>
  </data>
  <data name="EmailRequired" xml:space="preserve">
    <value>Email dibutuhkan</value>
  </data>
  <data name="EmailVerificationCode" xml:space="preserve">
    <value>Kode Email</value>
  </data>
  <data name="Empty" xml:space="preserve">
    <value>Kosong</value>
  </data>
  <data name="Fail" xml:space="preserve">
    <value>Gagal</value>
  </data>
  <data name="Failed" xml:space="preserve">
    <value>Failed</value>
  </data>
  <data name="Finished" xml:space="preserve">
    <value>Selesai</value>
  </data>
  <data name="Group" xml:space="preserve">
    <value>Grup</value>
  </data>
  <data name="Image" xml:space="preserve">
    <value>gambar</value>
  </data>
  <data name="ImageVerificationCode" xml:space="preserve">
    <value>Kode Gambar</value>
  </data>
  <data name="InconsistentPassword" xml:space="preserve">
    <value>Kata sandi tidak konsisten</value>
  </data>
  <data name="InstallFailed" xml:space="preserve">
    <value>Instalasi Gagal</value>
  </data>
  <data name="LanguageSwitch" xml:space="preserve">
    <value>Ganti bahasa</value>
  </data>
  <data name="LastSynchronizationTime" xml:space="preserve">
    <value>Waktu Sinkronasi Terakhir:</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Log In</value>
  </data>
  <data name="LogInFailed" xml:space="preserve">
    <value>Log in gagal, periksa jaringan</value>
  </data>
  <data name="LogOut" xml:space="preserve">
    <value>Keluar</value>
  </data>
  <data name="LogoutConfirmMsg" xml:space="preserve">
    <value>Apakah kamu yakin untuk keluar?</value>
  </data>
  <data name="LogUploadConfirmMsg" xml:space="preserve">
    <value>Apakah kamu yakin untuk mengunggah log?</value>
  </data>
  <data name="MsgAlert" xml:space="preserve">
    <value>Peringatan</value>
  </data>
  <data name="MsgExitRectificationReportEditMode" xml:space="preserve">
    <value>Silakan keluar dari mode edit di halaman Laporan Rekifikasi!</value>
  </data>
  <data name="MsgFailedReasonFormat" xml:space="preserve">
    <value>Alasan Gagal:{0}</value>
  </data>
  <data name="MsgGPSEmpty" xml:space="preserve">
    <value>Tidak bisa mendapatkan GPS, silakan coba lagi!</value>
  </data>
  <data name="MsgMustSelectSite" xml:space="preserve">
    <value>Anda harus memilih situs!</value>
  </data>
  <data name="MsgRefreshLocalData" xml:space="preserve">
    <value>Segarkan data lokal...</value>
  </data>
  <data name="MsgSaveFinished" xml:space="preserve">
    <value>Simpan Selesai!</value>
  </data>
  <data name="MsgSiteIdEmpty" xml:space="preserve">
    <value>Id Situs tidak boleh kosong!</value>
  </data>
  <data name="MsgSiteIdRepeated" xml:space="preserve">
    <value>Penyimpanan lokal Anda sudah berisi data dengan ID Situs ini, ID Situs tidak dapat diulang!</value>
  </data>
  <data name="MsgSubmitEmptyGroupConfirm" xml:space="preserve">
    <value>Selalu kirimkan semua acara grup yang dipilih jika grup kosong.</value>
  </data>
  <data name="MsgSubmittedFinished" xml:space="preserve">
    <value>Kirim Selesai !</value>
  </data>
  <data name="MyMessage" xml:space="preserve">
    <value>Pesanku</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Nama</value>
  </data>
  <data name="NetworkUnavailable" xml:space="preserve">
    <value>Jaringan tidak tersedia</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>Kata Sandi Baru</value>
  </data>
  <data name="NoDataSubmbitted" xml:space="preserve">
    <value>Tidak ada data yang akan dikirimkan!</value>
  </data>
  <data name="Ok" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="OldPassword" xml:space="preserve">
    <value>Kata Sandi Lama</value>
  </data>
  <data name="OpenGPS" xml:space="preserve">
    <value>Silakan buka GPS</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Kata sandi</value>
  </data>
  <data name="PasswordRequired" xml:space="preserve">
    <value>Kata sandi dibutuhkan</value>
  </data>
  <data name="PersonalInformation" xml:space="preserve">
    <value>Informasi personal</value>
  </data>
  <data name="PleaseInput" xml:space="preserve">
    <value>Mohon masukkan</value>
  </data>
  <data name="PleaseSearch" xml:space="preserve">
    <value>Silakan Cari</value>
  </data>
  <data name="Position" xml:space="preserve">
    <value>Posisi</value>
  </data>
  <data name="Project" xml:space="preserve">
    <value>Proyek</value>
  </data>
  <data name="QRRAfter" xml:space="preserve">
    <value>Setelah</value>
  </data>
  <data name="QRRBefore" xml:space="preserve">
    <value>Sebelum</value>
  </data>
  <data name="QRRComment" xml:space="preserve">
    <value>Komentar</value>
  </data>
  <data name="QRRNo" xml:space="preserve">
    <value>Tidak.</value>
  </data>
  <data name="RectificationReport" xml:space="preserve">
    <value>Laporan Punch List</value>
  </data>
  <data name="RequiredFormat" xml:space="preserve">
    <value>{0} diperlukan !</value>
  </data>
  <data name="RequiredPrompt" xml:space="preserve">
    <value>Silahkan lengkapi item yang diperlukan untuk Pengambilan Gambar</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Simpan</value>
  </data>
  <data name="SearchHistory" xml:space="preserve">
    <value>Riwayat Pencarian</value>
  </data>
  <data name="SearchPlaceholder" xml:space="preserve">
    <value>Silakan Cari</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>Memilih</value>
  </data>
  <data name="Sending" xml:space="preserve">
    <value>Mengirim</value>
  </data>
  <data name="Setting" xml:space="preserve">
    <value>Pengaturan</value>
  </data>
  <data name="SiteList" xml:space="preserve">
    <value>Daftar NAP</value>
  </data>
  <data name="SiteName" xml:space="preserve">
    <value>Site Name</value>
  </data>
  <data name="SiteSelection" xml:space="preserve">
    <value>Pilihan NAP</value>
  </data>
  <data name="Submit" xml:space="preserve">
    <value>Kirim</value>
  </data>
  <data name="Submitted" xml:space="preserve">
    <value>Dikirim</value>
  </data>
  <data name="SubmittedByFormat" xml:space="preserve">
    <value>{0} disampaikan oleh  {1}</value>
  </data>
  <data name="SubmittedFormat" xml:space="preserve">
    <value>diserahkan pada {0}</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>Kesuksesan</value>
  </data>
  <data name="Sure" xml:space="preserve">
    <value>Tentu</value>
  </data>
  <data name="Synchronize" xml:space="preserve">
    <value>Sinkron</value>
  </data>
  <data name="SynchronizeBasicData" xml:space="preserve">
    <value>Sinkronisasi Data Dasar</value>
  </data>
  <data name="SynchronizeConfirmMsg" xml:space="preserve">
    <value>Apakah kamu yakin untuk mensikronisasi?</value>
  </data>
  <data name="UpgradeConfirmMsg" xml:space="preserve">
    <value>Ada versi terbaru, apakah kamu yakin untuk meng-upgade?</value>
  </data>
  <data name="Upload" xml:space="preserve">
    <value>Unggah</value>
  </data>
  <data name="Uploading" xml:space="preserve">
    <value>Mengunggah</value>
  </data>
  <data name="UploadLog" xml:space="preserve">
    <value>Unggah Log</value>
  </data>
  <data name="ValidatorFailed" xml:space="preserve">
    <value>Verifikasi salah</value>
  </data>
  <data name="VerificationCodeRequired" xml:space="preserve">
    <value>Kode Verifikasi diperlukan</value>
  </data>
</root>