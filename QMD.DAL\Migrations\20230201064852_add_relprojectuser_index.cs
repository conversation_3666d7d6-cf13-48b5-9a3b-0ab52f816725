﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_relprojectuser_index : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_RelProjectUser_ProjectID_UserEmail",
                table: "RelProjectUser",
                columns: new[] { "ProjectID", "UserEmail" },
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_RelProjectUser_ProjectID_UserEmail",
                table: "RelProjectUser");
        }
    }
}
