﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Hub.Mobile.Views.ChangePasswordPage" Title="{DynamicResource ChangePassword}">
    <ContentPage.Resources>
        <ResourceDictionary>
            <x:Double x:Key="RowHeight">50</x:Double>
            <x:Double x:Key="LineHeight">1</x:Double>
            <Style TargetType="Label">
                <Setter Property="VerticalOptions" Value="Center"></Setter>
                <Setter Property="TextColor" Value="Black"></Setter>
            </Style>
            <Style TargetType="Entry">
                <Setter Property="PlaceholderColor" Value="#bababa"></Setter>
                <Setter Property="TextColor" Value="Black"></Setter>
                <Setter Property="HorizontalOptions" Value="FillAndExpand"></Setter>
            </Style>
        </ResourceDictionary>
    </ContentPage.Resources>
    <ContentPage.ToolbarItems>
        <ToolbarItem Text="{DynamicResource Submit}" Command="{Binding SubmitCommand}"></ToolbarItem>
    </ContentPage.ToolbarItems>
    <ContentPage.Content>
        <Grid BackgroundColor="White" Padding="20,10,20,10" VerticalOptions="Start">
            <Grid.RowDefinitions>
                <RowDefinition Height="auto"></RowDefinition>
                <RowDefinition Height="{StaticResource RowHeight}"></RowDefinition>
                <RowDefinition Height="{StaticResource LineHeight}"></RowDefinition>
                <RowDefinition Height="{StaticResource RowHeight}"></RowDefinition>
                <RowDefinition Height="{StaticResource LineHeight}"></RowDefinition>
                <RowDefinition Height="{StaticResource RowHeight}"></RowDefinition>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
            </Grid.ColumnDefinitions>
            <Image Grid.Row="0" Grid.ColumnSpan="2" Source="{Binding VisualImage}" HorizontalOptions="Start"  HeightRequest="30" WidthRequest="30">
                <Image.GestureRecognizers>
                    <TapGestureRecognizer Command="{Binding HiddenCommand}"></TapGestureRecognizer>
                </Image.GestureRecognizers>
            </Image>
            <Label Grid.Row="1" Grid.Column="0" Text="{DynamicResource OldPassword}" FontSize="Subtitle"></Label>
            <Entry Grid.Row="1" Grid.Column="1" IsPassword="{Binding Hidden}" Text="{Binding OldPassword}"></Entry>
            <Line Grid.Row="2" Grid.ColumnSpan="2" Background="#F7F8FA"></Line>
            <Label Grid.Row="3" Grid.Column="0" Text="{DynamicResource NewPassword}" FontSize="Subtitle"></Label>
            <Entry Grid.Row="3" Grid.Column="1" IsPassword="{Binding Hidden}" Text="{Binding NewPassword}"></Entry>
            <Line Grid.Row="4" Grid.ColumnSpan="2" Background="#F7F8FA"></Line>
            <Label Grid.Row="5" Grid.Column="0" Text="{DynamicResource ConfirmPassword}" FontSize="Subtitle"></Label>
            <Entry Grid.Row="5" Grid.Column="1" IsPassword="{Binding Hidden}" Text="{Binding ConfirmPassword}"></Entry>
        </Grid>
    </ContentPage.Content>
</ContentPage>