﻿using Hub.Mobile.ApiClient;
using Hub.Mobile.Const;
using Hub.Mobile.DAL;
using Hub.Mobile.DAL.Tables;
using Hub.Mobile.Interface;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using WebApiClient;
using Xamarin.Forms;

[assembly: Xamarin.Forms.Dependency(typeof(Hub.Mobile.Services.SynchronizeDataService))]
namespace Hub.Mobile.Services
{
    public interface ISynchronizeData
    {
        string Identity { get; }
        int Order { get; }
        Task<Tuple<bool, string>> SynchronizeData();
        Tuple<bool, string> Validate();
    }
    public abstract class BaseSynchronize : ISynchronizeData
    {
        public virtual string Identity { get; }
        public virtual int Order { get; }
        public abstract Task<Tuple<bool, string>> SynchronizeData();
        public virtual Tuple<bool, string> Validate()
        {
            bool success = true;
            string message = string.Empty;
            return new Tuple<bool, string>(success, message);
        }
    }
    #region APP数据同步
    public class SynchronizeApps : BaseSynchronize
    {
        public static event EventHandler AppChanged;
        public override string Identity
        {
            get
            {
                return MobileCommonConsts.Synchronize_Apps;
            }
        }
        public override int Order
        {
            get
            {
                return 1;
            }
        }
        public override async Task<Tuple<bool, string>> SynchronizeData()
        {
            bool success = false;
            string message = string.Empty;
            var response = await HttpApi.Resolve<IHubApiClient>().GetApps();
            success = response.Flag;
            message = response.Message;
            if (response.Flag && response.Data != null)
            {
                var configChanged = DependencyService.Get<IApp>().SaveAppConfig(response.Data);
                //App配置有变化并且注册事件了
                if (configChanged && AppChanged != null)
                {
                    AppChanged(this, new EventArgs());
                }
            }
            return new Tuple<bool, string>(success, message);
        }
    }
    #endregion
    public class SynchronizeDataService
    {
        static int running = 0;
        static ILogger logger = LogManager.GetCurrentClassLogger();
        private static Dictionary<string, ISynchronizeData> synchronizeContainer = new Dictionary<string, ISynchronizeData>();
        static SynchronizeDataService()
        {
            var types = Assembly.GetExecutingAssembly().GetTypes().Where(t => t.IsClass && !t.IsAbstract && typeof(BaseSynchronize).IsAssignableFrom(t));
            foreach (var type in types)
            {
                ISynchronizeData instance = Activator.CreateInstance(type) as ISynchronizeData;
                synchronizeContainer.Add(instance.Identity, instance);
            }
        }
        public static ISynchronizeData GetService(string identity)
        {
            ISynchronizeData service = null;
            synchronizeContainer.TryGetValue(identity, out service);
            return service;
        }
        /// <summary>
        /// failedContinueNext 失败是否继续同步下一个
        /// true 同步，false 不同步
        /// </summary>
        /// <param name="failedContinueNext"></param>
        /// <returns></returns>
        public async Task<Tuple<bool, string>> SynchronizeData(bool failedContinueNext = false)
        {
            bool success = true;
            string msg = string.Empty;
            if (Interlocked.CompareExchange(ref running, 1, 0) == 0)
            {
                try
                {
                    Tuple<bool, string> result;
                    foreach (var synchronize in synchronizeContainer.Values.OrderBy(p => p.Order))
                    {
                        try
                        {
                            var validate = synchronize.Validate();
                            if (validate.Item1)
                            {
                                result = await synchronize.SynchronizeData();
                                if (!result.Item1)
                                {
                                    success = false;
                                    msg = !string.IsNullOrWhiteSpace(result.Item2) ? result.Item2 : Resources.UIResources.DownloadFailed;
                                }
                            }
                            else
                            {
                                success = false;
                                msg = validate.Item2;
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.Error(ex);
                            success = false;
                            //msg = Resources.UIResources.DownloadFailed;
                            msg = ex?.Message ?? Resources.UIResources.DownloadFailed;
                        }
                        if (!success && !failedContinueNext)
                        {
                            //失败并且不继续下一个那么终止
                            break;
                        }
                    }
                    if (success)
                    {
                        var ret = await MobileSQLiteHelper.Current.DbContext.Table<DataCache>().Where(p => p.Key == MobileCommonConsts.SynchronizeTimeCacheKey).FirstOrDefaultAsync();
                        if (ret == null)
                        {
                            ret = new DataCache() { Key = MobileCommonConsts.SynchronizeTimeCacheKey, Value = DateTime.UtcNow.ToString() };
                            await MobileSQLiteHelper.Current.DbContext.InsertAsync(ret);
                        }
                        else
                        {
                            ret.Value = DateTime.UtcNow.ToString();
                            await MobileSQLiteHelper.Current.DbContext.UpdateAsync(ret);
                        }
                    }
                }
                catch { }
                finally
                {
                    Interlocked.CompareExchange(ref running, 0, 1);
                }
            }
            return new Tuple<bool, string>(success, msg);
        }
        public async Task<DateTime?> GetLastSynchronizeTime()
        {
            DateTime? lastTime = null;
            var ret = await MobileSQLiteHelper.Current.DbContext.Table<DataCache>().Where(p => p.Key == MobileCommonConsts.SynchronizeTimeCacheKey).FirstOrDefaultAsync();
            if (ret != null)
            {
                lastTime = DateTime.Parse(ret.Value).ToLocalTime();
            }
            return lastTime;
        }
    }

}
