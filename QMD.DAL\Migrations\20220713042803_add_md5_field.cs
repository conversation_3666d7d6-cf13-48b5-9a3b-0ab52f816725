﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_md5_field : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "MD5",
                table: "TaskStepValues",
                type: "varchar(128)",
                maxLength: 128,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "MD5",
                table: "TaskStepSamples",
                type: "varchar(128)",
                maxLength: 128,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "MD5",
                table: "TaskStepValues");

            migrationBuilder.DropColumn(
                name: "MD5",
                table: "TaskStepSamples");
        }
    }
}
