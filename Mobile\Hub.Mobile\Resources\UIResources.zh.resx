﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AcquireVerificationCode" xml:space="preserve">
    <value>获取验证码</value>
  </data>
  <data name="AcquireVerificationCodeFailed" xml:space="preserve">
    <value>验证码获取失败</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>新建</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>详细地址</value>
  </data>
  <data name="Alert" xml:space="preserve">
    <value>警告</value>
  </data>
  <data name="AlreadyLatestVersion" xml:space="preserve">
    <value>当前版本已经是最新版本</value>
  </data>
  <data name="Approved" xml:space="preserve">
    <value>已审批</value>
  </data>
  <data name="Area" xml:space="preserve">
    <value>区域</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="ChangePassword" xml:space="preserve">
    <value>修改密码</value>
  </data>
  <data name="CheckVersion" xml:space="preserve">
    <value>检查升级</value>
  </data>
  <data name="Compressing" xml:space="preserve">
    <value>正在压缩</value>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>确认密码</value>
  </data>
  <data name="Cover" xml:space="preserve">
    <value>Cover</value>
  </data>
  <data name="Create" xml:space="preserve">
    <value>创建</value>
  </data>
  <data name="DateCreated" xml:space="preserve">
    <value>创建时间</value>
  </data>
  <data name="DecompressCompleted" xml:space="preserve">
    <value>解压完成</value>
  </data>
  <data name="DecompressFailed" xml:space="preserve">
    <value>解压失败</value>
  </data>
  <data name="Decompressing" xml:space="preserve">
    <value>正在解压</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="DeleteConfirmMsg" xml:space="preserve">
    <value>确定删除？</value>
  </data>
  <data name="DownloadFailed" xml:space="preserve">
    <value>下载失败</value>
  </data>
  <data name="Downloading" xml:space="preserve">
    <value>正在下载</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>编辑</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>邮箱</value>
  </data>
  <data name="EmailIncorrect" xml:space="preserve">
    <value>邮箱格式不正确</value>
  </data>
  <data name="EmailOrUserName" xml:space="preserve">
    <value>邮箱或用户名</value>
  </data>
  <data name="EmailOrUserNameRequired" xml:space="preserve">
    <value>邮箱或用户名必填</value>
  </data>
  <data name="EmailRequired" xml:space="preserve">
    <value>邮箱必填</value>
  </data>
  <data name="EmailVerificationCode" xml:space="preserve">
    <value>邮件验证码</value>
  </data>
  <data name="Empty" xml:space="preserve">
    <value>空值</value>
  </data>
  <data name="Fail" xml:space="preserve">
    <value>打回</value>
  </data>
  <data name="Failed" xml:space="preserve">
    <value>失败</value>
  </data>
  <data name="Finished" xml:space="preserve">
    <value>完成</value>
  </data>
  <data name="Group" xml:space="preserve">
    <value>分组</value>
  </data>
  <data name="Image" xml:space="preserve">
    <value>图片</value>
  </data>
  <data name="ImageVerificationCode" xml:space="preserve">
    <value>图片验证码</value>
  </data>
  <data name="InconsistentPassword" xml:space="preserve">
    <value>密码不一致</value>
  </data>
  <data name="InstallFailed" xml:space="preserve">
    <value>安装失败</value>
  </data>
  <data name="LanguageSwitch" xml:space="preserve">
    <value>切换语言</value>
  </data>
  <data name="LastSynchronizationTime" xml:space="preserve">
    <value>上次同步时间：</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>登录</value>
  </data>
  <data name="LogInFailed" xml:space="preserve">
    <value>登录失败，请检查网络是否可用</value>
  </data>
  <data name="LogOut" xml:space="preserve">
    <value>退出</value>
  </data>
  <data name="LogoutConfirmMsg" xml:space="preserve">
    <value>确定退出当前账号？</value>
  </data>
  <data name="LogUploadConfirmMsg" xml:space="preserve">
    <value>确定上传日志？</value>
  </data>
  <data name="MsgAlert" xml:space="preserve">
    <value>警告</value>
  </data>
  <data name="MsgExitRectificationReportEditMode" xml:space="preserve">
    <value>请退出Rectification Report页面的编辑模式</value>
  </data>
  <data name="MsgFailedReasonFormat" xml:space="preserve">
    <value>失败原因 : {0}</value>
  </data>
  <data name="MsgGPSEmpty" xml:space="preserve">
    <value>无法获取GPS信息，请重试 ！</value>
  </data>
  <data name="MsgMustSelectSite" xml:space="preserve">
    <value>您必须选择一个站点 ！</value>
  </data>
  <data name="MsgRefreshLocalData" xml:space="preserve">
    <value>正在刷新本地数据...</value>
  </data>
  <data name="MsgSaveFinished" xml:space="preserve">
    <value>保存完毕 !</value>
  </data>
  <data name="MsgSiteIdEmpty" xml:space="preserve">
    <value>站点ID不能为空</value>
  </data>
  <data name="MsgSiteIdRepeated" xml:space="preserve">
    <value>站点ID不允许重复，您本地已经包含了一条数据拥有此站点ID ！</value>
  </data>
  <data name="MsgSubmitEmptyGroupConfirm" xml:space="preserve">
    <value>提交所有分组，无论是否为空</value>
  </data>
  <data name="MsgSubmittedFinished" xml:space="preserve">
    <value>提交完成 !</value>
  </data>
  <data name="MyMessage" xml:space="preserve">
    <value>我的消息</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>姓名</value>
  </data>
  <data name="NetworkUnavailable" xml:space="preserve">
    <value>网络不可用</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>新密码</value>
  </data>
  <data name="NoDataSubmbitted" xml:space="preserve">
    <value>没有数据将会被提交 ！</value>
  </data>
  <data name="Ok" xml:space="preserve">
    <value>确定</value>
  </data>
  <data name="OldPassword" xml:space="preserve">
    <value>原来密码</value>
  </data>
  <data name="OpenGPS" xml:space="preserve">
    <value>请打开位置信息</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>密码</value>
  </data>
  <data name="PasswordRequired" xml:space="preserve">
    <value>密码必填</value>
  </data>
  <data name="PersonalInformation" xml:space="preserve">
    <value>个人信息</value>
  </data>
  <data name="PleaseInput" xml:space="preserve">
    <value>请输入</value>
  </data>
  <data name="PleaseSearch" xml:space="preserve">
    <value>请输入搜索内容</value>
  </data>
  <data name="Position" xml:space="preserve">
    <value>职位</value>
  </data>
  <data name="Project" xml:space="preserve">
    <value>项目</value>
  </data>
  <data name="QRRAfter" xml:space="preserve">
    <value>After</value>
  </data>
  <data name="QRRBefore" xml:space="preserve">
    <value>Before</value>
  </data>
  <data name="QRRComment" xml:space="preserve">
    <value>评论</value>
  </data>
  <data name="QRRNo" xml:space="preserve">
    <value>编号</value>
  </data>
  <data name="RectificationReport" xml:space="preserve">
    <value>Rectification Report</value>
  </data>
  <data name="RequiredFormat" xml:space="preserve">
    <value>{0}为必填 !</value>
  </data>
  <data name="RequiredPrompt" xml:space="preserve">
    <value>请完成拍摄所需的项目</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="SearchHistory" xml:space="preserve">
    <value>历史搜索</value>
  </data>
  <data name="SearchPlaceholder" xml:space="preserve">
    <value>请输入搜索内容</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>选择</value>
  </data>
  <data name="Sending" xml:space="preserve">
    <value>正在发送</value>
  </data>
  <data name="Setting" xml:space="preserve">
    <value>设置</value>
  </data>
  <data name="SiteList" xml:space="preserve">
    <value>NAP列表</value>
  </data>
  <data name="SiteName" xml:space="preserve">
    <value>站点名称</value>
  </data>
  <data name="SiteSelection" xml:space="preserve">
    <value>NAP选择</value>
  </data>
  <data name="Submit" xml:space="preserve">
    <value>提交</value>
  </data>
  <data name="Submitted" xml:space="preserve">
    <value>已提交</value>
  </data>
  <data name="SubmittedByFormat" xml:space="preserve">
    <value>{0} 提交于 {1}</value>
  </data>
  <data name="SubmittedFormat" xml:space="preserve">
    <value>提交于{0}</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>成功</value>
  </data>
  <data name="Sure" xml:space="preserve">
    <value>确定</value>
  </data>
  <data name="Synchronize" xml:space="preserve">
    <value>同步</value>
  </data>
  <data name="SynchronizeBasicData" xml:space="preserve">
    <value>同步基础数据</value>
  </data>
  <data name="SynchronizeConfirmMsg" xml:space="preserve">
    <value>确定同步数据？</value>
  </data>
  <data name="UpgradeConfirmMsg" xml:space="preserve">
    <value>有新版本，是否升级？</value>
  </data>
  <data name="Upload" xml:space="preserve">
    <value>上传</value>
  </data>
  <data name="Uploading" xml:space="preserve">
    <value>正在上传</value>
  </data>
  <data name="UploadLog" xml:space="preserve">
    <value>上传日志</value>
  </data>
  <data name="ValidatorFailed" xml:space="preserve">
    <value>验证不通过</value>
  </data>
  <data name="VerificationCodeRequired" xml:space="preserve">
    <value>验证码必填</value>
  </data>
</root>