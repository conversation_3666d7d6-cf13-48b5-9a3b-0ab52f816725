﻿using Android.App;
using Android.Content;
using Android.Net;
using Android.OS;
using Android.Runtime;
using Android.Views;
using Android.Widget;
using Hub.Mobile.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Xamarin.Forms;

[assembly: Dependency(typeof(Hub.Mobile.Droid.Imps.Network))]
namespace Hub.Mobile.Droid.Imps
{
    public class Network : INetWork
    {
        public Network()
        {

        }
        //判断是否有网络连接
        public bool IsNetworkAvailable()
        {
            ConnectivityManager connectivityManager = ConnectivityManager.FromContext(MainActivity.AppContext);
            if (Build.VERSION.SdkInt >= Android.OS.BuildVersionCodes.M)
            {
                //获取网络属性
                NetworkCapabilities networkCapabilities = connectivityManager.GetNetworkCapabilities(connectivityManager.ActiveNetwork);
                if (networkCapabilities != null)
                {
                    return networkCapabilities.HasCapability(NetCapability.Validated);
                }
            }
            else
            {
                NetworkInfo mNetworkInfo = connectivityManager.ActiveNetworkInfo;
                if (mNetworkInfo != null)
                {
                    return mNetworkInfo.IsConnected;
                }
            }
            return false;
        }
    }
}