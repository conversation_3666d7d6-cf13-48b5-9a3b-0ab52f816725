﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Hub.Mobile.Interface
{
    public interface IDeviceService
    {
        /// <summary>
        /// 获取手机厂商
        /// </summary>
        /// <returns></returns>
        string GetDeviceBrand();
        /// <summary>
        /// 获取手机型号
        /// </summary>
        /// <returns></returns>
        string GetDeviceModel();
        /// <summary>
        /// 获取手机系统版本号
        /// </summary>
        /// <returns></returns>
        string GetSystemVersion();
    }
}
