{"runtimeTarget": {"name": ".NETStandard,Version=v2.0/", "signature": ""}, "compilationOptions": {}, "targets": {".NETStandard,Version=v2.0": {}, ".NETStandard,Version=v2.0/": {"Hub.Mobile.Plugin/1.0.0": {"dependencies": {"Hub.Mobile.Interface": "1.0.0", "Hub.Mobile.Utility": "1.0.0", "NETStandard.Library": "2.0.3", "Newtonsoft.Json": "13.0.1", "Plugin.Permissions": "6.0.1", "Xam.Plugin.Media": "5.0.1", "Xamarin.Forms": "5.0.0.2401"}, "runtime": {"Hub.Mobile.Plugin.dll": {}}}, "AutoMapper/10.1.1": {"dependencies": {"Microsoft.CSharp": "4.7.0", "System.Reflection.Emit": "4.7.0"}, "runtime": {"lib/netstandard2.0/AutoMapper.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.1.1.0"}}}, "Microsoft.CSharp/4.7.0": {"runtime": {"lib/netstandard2.0/Microsoft.CSharp.dll": {"assemblyVersion": "4.0.5.0", "fileVersion": "4.700.19.56404"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "NETStandard.Library/2.0.3": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0"}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1.25517"}}}, "NLog/4.7.9": {"runtime": {"lib/netstandard2.0/NLog.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.7.9.12899"}}}, "Plugin.Permissions/6.0.1": {"dependencies": {"Xamarin.Essentials": "1.5.2"}, "runtime": {"lib/netstandard2.0/Plugin.Permissions.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "System.Numerics.Vectors/4.5.0": {"runtime": {"lib/netstandard2.0/System.Numerics.Vectors.dll": {"assemblyVersion": "4.1.4.0", "fileVersion": "4.6.26515.6"}}}, "System.Reflection.Emit/4.7.0": {"dependencies": {"System.Reflection.Emit.ILGeneration": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Reflection.Emit.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.700.19.56404"}}}, "System.Reflection.Emit.ILGeneration/4.7.0": {"runtime": {"lib/netstandard2.0/System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.700.19.56404"}}}, "Xam.Plugin.Geolocator/4.5.0.6": {"runtime": {"lib/netstandard2.0/Plugin.Geolocator.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Xam.Plugin.Media/5.0.1": {"dependencies": {"Xamarin.Essentials": "1.5.2"}, "runtime": {"lib/netstandard2.0/Plugin.Media.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Xamarin.Essentials/1.5.2": {"dependencies": {"System.Numerics.Vectors": "4.5.0"}, "runtime": {"lib/netstandard2.0/Xamarin.Essentials.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Xamarin.Forms/5.0.0.2401": {"runtime": {"lib/netstandard2.0/Xamarin.Forms.Core.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "5.0.0.905"}, "lib/netstandard2.0/Xamarin.Forms.Platform.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "5.0.0.905"}, "lib/netstandard2.0/Xamarin.Forms.Xaml.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "5.0.0.905"}}}, "Hub.Mobile.Const/1.0.0": {"runtime": {"Hub.Mobile.Const.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}, "Hub.Mobile.Interface/1.0.0": {"dependencies": {"AutoMapper": "10.1.1", "Hub.Mobile.Const": "1.0.0", "Hub.Mobile.Model": "1.0.0", "Xamarin.Forms": "5.0.0.2401"}, "runtime": {"Hub.Mobile.Interface.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}, "Hub.Mobile.Model/1.0.0": {"dependencies": {"Xamarin.Forms": "5.0.0.2401"}, "runtime": {"Hub.Mobile.Model.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}, "Hub.Mobile.Utility/1.0.0": {"dependencies": {"NLog": "4.7.9", "Plugin.Permissions": "6.0.1", "Xam.Plugin.Geolocator": "4.5.0.6"}, "runtime": {"Hub.Mobile.Utility.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"Hub.Mobile.Plugin/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AutoMapper/10.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-uMgbqOdu9ZG5cIOty0C85hzzayBH2i9BthnS5FlMqKtMSHDv4ts81a2jS1VFaDBVhlBeIqJ/kQKjQY95BZde9w==", "path": "automapper/10.1.1", "hashPath": "automapper.10.1.1.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "NETStandard.Library/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-st47PosZSHrjECdjeIzZQbzivYBJFv6P2nv4cj2ypdI204DO+vZ7l5raGMiX4eXMJ53RfOIg+/s4DHVZ54Nu2A==", "path": "netstandard.library/2.0.3", "hashPath": "netstandard.library.2.0.3.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "NLog/4.7.9": {"type": "package", "serviceable": true, "sha512": "sha512-Y0dKY5d506ZVcb8dTMp3BSb+jUGaFu/ArvRkgn4daDWKf7qqFXjEFYjqoyTrWkcNguqS6avkwICS0hsFsr4BzA==", "path": "nlog/4.7.9", "hashPath": "nlog.4.7.9.nupkg.sha512"}, "Plugin.Permissions/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-na/0FNhyKmSicANWZMLSM0y/FW2AvLoawX3lsvBpELwP15sdJRyuBhRKk1OQjRoNrmTIewcON1tJFyydjMGWcA==", "path": "plugin.permissions/6.0.1", "hashPath": "plugin.permissions.6.0.1.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Reflection.Emit/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "path": "system.reflection.emit/4.7.0", "hashPath": "system.reflection.emit.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-AucBYo3DSI0IDxdUjKksBcQJXPHyoPyrCXYURW1WDsLI4M65Ar/goSHjdnHOAY9MiYDNKqDlIgaYm+zL2hA1KA==", "path": "system.reflection.emit.ilgeneration/4.7.0", "hashPath": "system.reflection.emit.ilgeneration.4.7.0.nupkg.sha512"}, "Xam.Plugin.Geolocator/4.5.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Ro7peBDxsDXPc7J7yLx0ekAcXIfVTAXj/rXiBKiTgZ4Lm7KZJIU8PXOCcI4JztzT3MQrL8Rmka48il/ix0pwoA==", "path": "xam.plugin.geolocator/4.5.0.6", "hashPath": "xam.plugin.geolocator.4.5.0.6.nupkg.sha512"}, "Xam.Plugin.Media/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-UUR31EEnhDVa4IXgVfQIAL3u9v6YAyppsb5UlO9MNJ4quGGkfcWX++JexjB1nMGqvCzsjAT2t32drKDIXoW4SQ==", "path": "xam.plugin.media/5.0.1", "hashPath": "xam.plugin.media.5.0.1.nupkg.sha512"}, "Xamarin.Essentials/1.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-qHxkrkx5jIC0X/qWhxCPZ3MuuOJm8bfCzAoUtlpOBSE4oV0JhrkTezK0A+IV+k/MeJ3zKc8FexaIXah9rlCTIw==", "path": "xamarin.essentials/1.5.2", "hashPath": "xamarin.essentials.1.5.2.nupkg.sha512"}, "Xamarin.Forms/5.0.0.2401": {"type": "package", "serviceable": true, "sha512": "sha512-fk/V8QWjeyo8UXcsqsNRMd6o1H5PBfyXYm97nxvhMsJIdeqDgfRWyIsSurM8uLGLQGdQP23R+hHj7vcTSo2UjQ==", "path": "xamarin.forms/5.0.0.2401", "hashPath": "xamarin.forms.5.0.0.2401.nupkg.sha512"}, "Hub.Mobile.Const/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Hub.Mobile.Interface/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Hub.Mobile.Model/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Hub.Mobile.Utility/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}