﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using QMD.DAL;

namespace QMD.DAL.Migrations
{
    [DbContext(typeof(QmdDbContext))]
    [Migration("20241010091235_add_reportorder_newcolumn")]
    partial class add_reportorder_newcolumn
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("Relational:MaxIdentifierLength", 64)
                .HasAnnotation("ProductVersion", "5.0.17");

            modelBuilder.Entity("QMD.DAL.Table.ActionRecord", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Action")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Desc")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Operator")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("ProjectID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Property1")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Property2")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Property3")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Property4")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Property5")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Property6")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<string>("Property7")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Property8")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("TriggerDay")
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)");

                    b.HasKey("ID");

                    b.ToTable("ActionRecords");
                });

            modelBuilder.Entity("QMD.DAL.Table.AppSolution", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Code")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("FirstDisName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("FirstEnName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("FirstNode")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("ProjectType")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)");

                    b.Property<string>("SecondDisName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("SecondEnName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("SecondNode")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ThirdDisName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ThirdEnName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ThirdNode")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.HasKey("ID");

                    b.ToTable("AppSolutions");
                });

            modelBuilder.Entity("QMD.DAL.Table.Approval", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("ApprovalStatus")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("GroupID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSerious")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("Level")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Remark")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)");

                    b.Property<string>("StepID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int>("TargetType")
                        .HasColumnType("int");

                    b.Property<string>("TaskItemID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TplID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TriggerDay")
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)");

                    b.Property<string>("ValueID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.HasKey("ID");

                    b.HasIndex("TaskItemID", "StepID", "Level", "TargetType", "ValueID");

                    b.ToTable("Approvals");
                });

            modelBuilder.Entity("QMD.DAL.Table.Approver", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("AllowLevel")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ProjectID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("UserEmail")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("UserName")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.HasKey("ID");

                    b.HasIndex("UserEmail", "AllowLevel", "ProjectID");

                    b.ToTable("Approvers");
                });

            modelBuilder.Entity("QMD.DAL.Table.Contractor", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Code")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("DisplayName")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ProjectID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Source")
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)");

                    b.HasKey("ID");

                    b.ToTable("Contractors");
                });

            modelBuilder.Entity("QMD.DAL.Table.CycleTaskRecord", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastUploadDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("Level1LastApprovalTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("Level1NotPassedFileCount")
                        .HasColumnType("int");

                    b.Property<int>("Level1NotPassedStepCount")
                        .HasColumnType("int");

                    b.Property<int>("Level1PassedFileCount")
                        .HasColumnType("int");

                    b.Property<int>("Level1PassedStepCount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("Level2LastApprovalTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("Level2NotPassedFileCount")
                        .HasColumnType("int");

                    b.Property<int>("Level2NotPassedStepCount")
                        .HasColumnType("int");

                    b.Property<int>("Level2PassedFileCount")
                        .HasColumnType("int");

                    b.Property<int>("Level2PassedStepCount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("SubmitStatus")
                        .HasColumnType("int");

                    b.Property<DateTime?>("SubmitTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("TaskItemID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int>("TotalFileCount")
                        .HasColumnType("int");

                    b.Property<int>("TotalRequiredStepCount")
                        .HasColumnType("int");

                    b.Property<string>("TplID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TriggerDay")
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)");

                    b.Property<int>("UploadedRequiredStepCount")
                        .HasColumnType("int");

                    b.Property<int>("WaitLevel1ApproveFileCount")
                        .HasColumnType("int");

                    b.Property<int>("WaitLevel1ApproveStepCount")
                        .HasColumnType("int");

                    b.Property<int>("WaitLevel2ApproveFileCount")
                        .HasColumnType("int");

                    b.Property<int>("WaitLevel2ApproveStepCount")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.HasIndex("TplID", "TaskItemID", "TriggerDay");

                    b.ToTable("CycleTaskRecords");
                });

            modelBuilder.Entity("QMD.DAL.Table.DingTalkUserInfo", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("Active")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("Admin")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Avatar")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<bool>("Boss")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<long>("DeptId")
                        .HasColumnType("bigint");

                    b.Property<string>("DeptIdList")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<long>("DeptOrder")
                        .HasColumnType("bigint");

                    b.Property<string>("DingEmail")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("DingMobile")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("ExclusiveAccount")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ExclusiveAccountCorpId")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ExclusiveAccountCorpName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ExclusiveAccountType")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("HideMobile")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("IdentityId")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("JobNumber")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("Leader")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("LoginId")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("NickName")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("NmospPhone")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<int>("PostType")
                        .HasColumnType("int");

                    b.Property<string>("UnionId")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("UserEmail")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("UserId")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<int>("UserType")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.ToTable("DingTalkUserInfos");
                });

            modelBuilder.Entity("QMD.DAL.Table.DingtalkDepartment", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("AutoAddUser")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("CreateDeptGroup")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<long>("DeptId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Name")
                        .HasColumnType("longtext");

                    b.Property<long>("ParentId")
                        .HasColumnType("bigint");

                    b.Property<string>("WhyName")
                        .HasColumnType("longtext");

                    b.HasKey("ID");

                    b.ToTable("DingtalkDepartments");
                });

            modelBuilder.Entity("QMD.DAL.Table.FunctionInfo", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("FunctionKey")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("FunctionName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<string>("ParentFunctionId")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Path")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.HasKey("ID");

                    b.ToTable("per_functioninfo");
                });

            modelBuilder.Entity("QMD.DAL.Table.ItrWorkOrder", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("City")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("CodeSk")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<string>("Comemail")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Committee")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Commobile")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<double?>("CurOrderProgress")
                        .HasColumnType("double");

                    b.Property<bool>("DingHasDone")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("DingTaskId")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("FirstSolution")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSendMail")
                        .HasColumnType("tinyint(1)");

                    b.Property<double>("LeftTime")
                        .HasColumnType("double");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NetAttributes")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("NetName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("NetType")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Operator")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("OrderLevel")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("OrderNo")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("OrderSolution")
                        .HasMaxLength(2000)
                        .HasColumnType("varchar(2000)");

                    b.Property<string>("OrderStatus")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("OrderType")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("ProductLine")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ProductModel")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ProgramProvider")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ProviderEmail")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ProviderPhone")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("Province")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<double?>("RemainSLATime")
                        .HasColumnType("double");

                    b.Property<DateTime?>("SLATime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("SecondSolution")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("SendMailMsg")
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("SolutionTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Title")
                        .HasColumnType("longtext");

                    b.Property<double?>("TotalSLATime")
                        .HasColumnType("double");

                    b.Property<string>("UploadEmail")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("UploadPhone")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<DateTime>("UploadTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("UploadUser")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.HasKey("ID");

                    b.ToTable("ItrWorkOrders");
                });

            modelBuilder.Entity("QMD.DAL.Table.Lang", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("CultureCode")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Title")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("UsedFor")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.HasKey("ID");

                    b.HasIndex("Key", "CultureCode")
                        .IsUnique();

                    b.ToTable("Langs");
                });

            modelBuilder.Entity("QMD.DAL.Table.NetUserPosition", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("CompanyName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("DepName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Department1")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Department2")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Department3")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("EmployeeCode")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("IdCardName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("IdCardNumber")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsOnTheJob")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ManageEmployeeCode")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NickName")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("OpenId")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Position")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("SessionKey")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("UserAreas")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<int>("UserDataType")
                        .HasColumnType("int");

                    b.Property<string>("UserEmail")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("UserLevel")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("UserPhone")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("UserPhoto")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<int>("UserPosition")
                        .HasColumnType("int");

                    b.Property<int>("UserSource")
                        .HasColumnType("int");

                    b.Property<int>("UserType")
                        .HasColumnType("int");

                    b.Property<string>("WeixinId")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<DateTime?>("lastFaceAuthDateTime")
                        .HasColumnType("datetime(6)");

                    b.HasKey("ID");

                    b.ToTable("NetUserPositions");
                });

            modelBuilder.Entity("QMD.DAL.Table.Netprovider", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ChargePersonEmail")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ChargePersonName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ChargePersonNo")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ChargePersonPhone")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("City")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("CodeSk")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<string>("Country")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("DepName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsQaNet")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NetName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("NetProperties")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("Operator")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ProductSpe")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Province")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Region")
                        .HasMaxLength(10)
                        .HasColumnType("varchar(10)");

                    b.Property<string>("WhyOperator")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("WhyProductSpe")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("WhyRegion")
                        .HasMaxLength(10)
                        .HasColumnType("varchar(10)");

                    b.HasKey("ID");

                    b.HasIndex("CodeSk");

                    b.ToTable("Netproviders");
                });

            modelBuilder.Entity("QMD.DAL.Table.ObjTypeLevel", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("LevelColor")
                        .HasMaxLength(10)
                        .HasColumnType("varchar(10)");

                    b.Property<string>("LevelName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<int>("LevelType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<double>("OlaTime")
                        .HasColumnType("double");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.HasIndex("Code");

                    b.ToTable("ObjTypeLevels");
                });

            modelBuilder.Entity("QMD.DAL.Table.ObjTypeLevelCfg", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("CfgType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ObjTypeLevelId")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("ObjTypeName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.HasKey("ID");

                    b.ToTable("ObjTypeLevelCfgs");
                });

            modelBuilder.Entity("QMD.DAL.Table.Project", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Code")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsPrivate")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("ProjectStatus")
                        .HasColumnType("int");

                    b.Property<int>("ProjectType")
                        .HasColumnType("int");

                    b.Property<string>("RegionCode")
                        .IsRequired()
                        .HasMaxLength(12)
                        .HasColumnType("varchar(12)");

                    b.Property<string>("Remark")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)");

                    b.Property<string>("ServiceLineID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("StartedTime")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("StopAutoImportWosContract")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("TaskType")
                        .HasColumnType("int");

                    b.Property<string>("WOSProjNames")
                        .HasColumnType("text");

                    b.Property<string>("Year")
                        .HasMaxLength(4)
                        .HasColumnType("varchar(4)");

                    b.HasKey("ID");

                    b.HasIndex("Code");

                    b.HasIndex("DisplayName");

                    b.ToTable("Projects");
                });

            modelBuilder.Entity("QMD.DAL.Table.QaImportantObject", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("DataType")
                        .HasColumnType("int");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NetType")
                        .HasColumnType("longtext");

                    b.Property<string>("ObjectType")
                        .HasColumnType("longtext");

                    b.Property<string>("ProductSpe")
                        .HasColumnType("longtext");

                    b.HasKey("ID");

                    b.ToTable("ImportantObjects");
                });

            modelBuilder.Entity("QMD.DAL.Table.QaOrders", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime>("InspectTime")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NeName")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<string>("OrderNo")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ParentOrderNo")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ProjectID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("QaTaskId")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ServiceLineID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("TaskItemId")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TaskStepID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TaskStepValueID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TplId")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TriggerDay")
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)");

                    b.HasKey("ID");

                    b.ToTable("QaOrders");
                });

            modelBuilder.Entity("QMD.DAL.Table.QaWorkOrder", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("AppCode")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("AppName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("BigDataId")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<string>("City")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<double>("InspectScore")
                        .HasColumnType("double");

                    b.Property<DateTime?>("InspectTime")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSendMail")
                        .HasColumnType("tinyint(1)");

                    b.Property<float?>("Lat")
                        .HasColumnType("float");

                    b.Property<float?>("Lng")
                        .HasColumnType("float");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NetAttributes")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("NetName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("NetType")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Operator")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("OrderNo")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("OrderSolution")
                        .HasMaxLength(2000)
                        .HasColumnType("varchar(2000)");

                    b.Property<string>("ProjectID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Province")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("QaTaskId")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Remark")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("SendMailMsg")
                        .HasColumnType("longtext");

                    b.Property<string>("ServiceLineID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Source")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("TaskItemId")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TaskStepID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TaskStepValueID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TplId")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TriggerDay")
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)");

                    b.HasKey("ID");

                    b.HasIndex("TaskItemId", "TplId", "TaskStepID", "TriggerDay");

                    b.ToTable("QaWorkOrders");
                });

            modelBuilder.Entity("QMD.DAL.Table.Region", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(12)
                        .HasColumnType("varchar(12)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int?>("TimeZoneOffset")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.HasIndex("Code");

                    b.ToTable("Regions");
                });

            modelBuilder.Entity("QMD.DAL.Table.RelAdminUser", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ProjectID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("UserEmail")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("UserName")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.HasKey("ID");

                    b.ToTable("RelAdminUser");
                });

            modelBuilder.Entity("QMD.DAL.Table.RelAppSolutionAndTpl", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("AppSolutionID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("TplID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.HasKey("ID");

                    b.ToTable("AppSolutionAndTplRels");
                });

            modelBuilder.Entity("QMD.DAL.Table.RelContractorTaskItemTpl", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ContractorID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("TaskItemID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TplID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.HasKey("ID");

                    b.HasIndex("ContractorID", "TaskItemID", "TplID");

                    b.ToTable("ContractorTaskItemTplRels");
                });

            modelBuilder.Entity("QMD.DAL.Table.RelContractorUser", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ContractorID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("UserEmail")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("UserName")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("UserType")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.HasIndex("ContractorID");

                    b.ToTable("ContractorUserRels");
                });

            modelBuilder.Entity("QMD.DAL.Table.RelProjectUser", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ProjectID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("UserEmail")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("UserName")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.HasKey("ID");

                    b.HasIndex("ProjectID", "UserEmail");

                    b.ToTable("RelProjectUser");
                });

            modelBuilder.Entity("QMD.DAL.Table.RelRoleDataPermission", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("PerName")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("PerType")
                        .HasColumnType("int");

                    b.Property<string>("ProductSpe")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RoleId")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.HasKey("ID");

                    b.ToTable("per_relroledatapermission");
                });

            modelBuilder.Entity("QMD.DAL.Table.RelRoleFunction", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("FunctionId")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("RoleId")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("RoleName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.HasKey("ID");

                    b.ToTable("per_relrolefunction");
                });

            modelBuilder.Entity("QMD.DAL.Table.RelRoleUser", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("RoleId")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("UserEmail")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("UserNetType")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.ToTable("per_relroleuser");
                });

            modelBuilder.Entity("QMD.DAL.Table.RelTaskItemAndTpl", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastUploadDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Level1ApproverID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("Level1LastApprovalTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("Level1NotPassedFileCount")
                        .HasColumnType("int");

                    b.Property<int>("Level1NotPassedStepCount")
                        .HasColumnType("int");

                    b.Property<int>("Level1PassedFileCount")
                        .HasColumnType("int");

                    b.Property<int>("Level1PassedStepCount")
                        .HasColumnType("int");

                    b.Property<string>("Level2ApproverID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("Level2LastApprovalTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("Level2NotPassedFileCount")
                        .HasColumnType("int");

                    b.Property<int>("Level2NotPassedStepCount")
                        .HasColumnType("int");

                    b.Property<int>("Level2PassedFileCount")
                        .HasColumnType("int");

                    b.Property<int>("Level2PassedStepCount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("SubmitStatus")
                        .HasColumnType("int");

                    b.Property<DateTime?>("SubmitTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("TaskItemID")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("TotalFileCount")
                        .HasColumnType("int");

                    b.Property<int>("TotalRequiredStepCount")
                        .HasColumnType("int");

                    b.Property<string>("TplID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int>("UploadedRequiredStepCount")
                        .HasColumnType("int");

                    b.Property<int>("WaitLevel1ApproveFileCount")
                        .HasColumnType("int");

                    b.Property<int>("WaitLevel1ApproveStepCount")
                        .HasColumnType("int");

                    b.Property<int>("WaitLevel2ApproveFileCount")
                        .HasColumnType("int");

                    b.Property<int>("WaitLevel2ApproveStepCount")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.HasIndex("TplID", "TaskItemID");

                    b.ToTable("TaskItemAndTplRels");
                });

            modelBuilder.Entity("QMD.DAL.Table.RoleInfo", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsAllOperator")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsAllProductSpe")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("OperatorsListStr")
                        .HasColumnType("longtext");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<string>("ProductSpeListStr")
                        .HasColumnType("longtext");

                    b.Property<int>("RoleApproveTypeName")
                        .HasColumnType("int");

                    b.Property<int>("RoleDataTypeName")
                        .HasColumnType("int");

                    b.Property<string>("RoleName")
                        .HasColumnType("longtext");

                    b.HasKey("ID");

                    b.ToTable("per_roleinfo");
                });

            modelBuilder.Entity("QMD.DAL.Table.ServiceLine", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.HasKey("ID");

                    b.ToTable("ServiceLines");
                });

            modelBuilder.Entity("QMD.DAL.Table.SyncQaRecord", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("ApprovalCount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("ExceptionCount")
                        .HasColumnType("int");

                    b.Property<DateTime>("InspectTime")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("NewExceptionCount")
                        .HasColumnType("int");

                    b.Property<int>("NormalCount")
                        .HasColumnType("int");

                    b.Property<string>("QaTaskId")
                        .HasColumnType("longtext");

                    b.Property<int>("QaTaskType")
                        .HasColumnType("int");

                    b.Property<string>("SyncResult")
                        .HasColumnType("longtext");

                    b.Property<string>("TaskItemID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TplID")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("TriggerDay")
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)");

                    b.HasKey("ID");

                    b.ToTable("SyncQaRecords");
                });

            modelBuilder.Entity("QMD.DAL.Table.TaskGroupTpl", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsHide")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsTemporary")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<string>("TplID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.HasKey("ID");

                    b.ToTable("TaskGroupTpls");
                });

            modelBuilder.Entity("QMD.DAL.Table.TaskItem", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Addr1")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Addr2")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Addr3")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Addr4")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("AuthCode")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<bool>("BelongLevel2Sampling")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("BigDataId")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Contractor")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<bool>("HasLevel2Sampling")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("InspectTime")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("KeyPropertyChangedTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("LastEmailRemindTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("LastUploadDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<float?>("Lat")
                        .HasColumnType("float");

                    b.Property<string>("Level1CustomerProgStepID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Level2CustomerProgStepID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<float?>("Lng")
                        .HasColumnType("float");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NmpId")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ProjectID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Region")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Remark")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ServiceLineID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Source")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("SubRegion")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<int>("TaskItemStatus")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.HasIndex("Code", "ProjectID");

                    b.ToTable("TaskItems");
                });

            modelBuilder.Entity("QMD.DAL.Table.TaskItemDimValue", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("DimDepth")
                        .HasColumnType("int");

                    b.Property<string>("DimTag")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Level1")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Level2")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Level3")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Value1")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Value2")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Value3")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.HasKey("ID");

                    b.ToTable("TaskItemDimValues");
                });

            modelBuilder.Entity("QMD.DAL.Table.TaskStepTpl", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("AllowMany")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("AllowSelect")
                        .HasColumnType("tinyint(1)");

                    b.Property<int?>("CountMaxLimit")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("DataType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DateTimeMaxLimit")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("DateTimeMinLimit")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("EffectiveEndTimeHoursOffset")
                        .HasColumnType("int");

                    b.Property<int>("EffectiveStartTimeHoursOffset")
                        .HasColumnType("int");

                    b.Property<int>("EffectiveTimeType")
                        .HasColumnType("int");

                    b.Property<string>("GroupID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int?>("HybridModeOrder")
                        .HasColumnType("int");

                    b.Property<string>("HybridModeParentID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int?>("IntegerMaxLimit")
                        .HasColumnType("int");

                    b.Property<int?>("IntegerMinLimit")
                        .HasColumnType("int");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsHide")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsRequired")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsTemporary")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)");

                    b.Property<string>("SetValue")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<int>("SupportFileTypes")
                        .HasColumnType("int");

                    b.Property<int?>("TextMaxLimit")
                        .HasColumnType("int");

                    b.Property<int?>("TextMinLimit")
                        .HasColumnType("int");

                    b.Property<string>("TplID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.HasKey("ID");

                    b.ToTable("TaskStepTpls");
                });

            modelBuilder.Entity("QMD.DAL.Table.TaskStepValue", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("BigDataId")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<string>("ContractorID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("DataType")
                        .HasColumnType("int");

                    b.Property<string>("ExtValue1")
                        .HasColumnType("longtext");

                    b.Property<string>("ExtValue2")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<string>("ExtValue3")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<string>("ExtValue4")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<string>("ExtValue5")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<string>("ExtValue6")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<string>("ExtValue7")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<string>("ExtValue8")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<int?>("FileVersion")
                        .HasColumnType("int");

                    b.Property<bool>("ForceRectify")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("HybridOrder")
                        .HasColumnType("int");

                    b.Property<DateTime?>("InspectTime")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Lat")
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)");

                    b.Property<string>("Lng")
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)");

                    b.Property<string>("MD5")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<string>("ProjectID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("RegionCode")
                        .IsRequired()
                        .HasMaxLength(12)
                        .HasColumnType("varchar(12)");

                    b.Property<string>("Remark")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)");

                    b.Property<string>("ServiceLineID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int>("SupportFileTypes")
                        .HasColumnType("int");

                    b.Property<string>("TaskItemID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TaskStepID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TaskTplID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TriggerDay")
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)");

                    b.Property<string>("Value")
                        .HasMaxLength(2000)
                        .HasColumnType("varchar(2000)");

                    b.HasKey("ID");

                    b.HasIndex("TaskItemID", "TaskTplID", "TaskStepID");

                    b.HasIndex("ProjectID", "TaskItemID", "TaskTplID", "IsActived");

                    b.ToTable("TaskStepValues");
                });

            modelBuilder.Entity("QMD.DAL.Table.TaskStepValueSample", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("DataType")
                        .HasColumnType("int");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("MD5")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)");

                    b.Property<string>("TaskStepID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("TaskTplID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Value")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.HasKey("ID");

                    b.ToTable("TaskStepSamples");
                });

            modelBuilder.Entity("QMD.DAL.Table.TaskTpl", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("CycleType")
                        .HasColumnType("int");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<bool>("GroupPublished")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Milestone")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("NoRequiredStep")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ObjTypeLevelId")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("ProjectID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<bool>("Published")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("RegionCode")
                        .IsRequired()
                        .HasMaxLength(12)
                        .HasColumnType("varchar(12)");

                    b.Property<int>("RequiredStepCount")
                        .HasColumnType("int");

                    b.Property<string>("ServiceLineID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<bool>("StepPublished")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("TriggerDay")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("TriggerTimes")
                        .HasColumnType("int");

                    b.Property<int>("UnrequiredStepCount")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.HasIndex("DisplayName");

                    b.ToTable("TaskTpls");
                });

            modelBuilder.Entity("QMD.DAL.Table.WosContractor", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("ImportedToQmd")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Mail")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Phone")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("SubconCode")
                        .IsRequired()
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)");

                    b.Property<string>("SubconName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.HasKey("ID");

                    b.HasIndex("SubconCode")
                        .IsUnique();

                    b.ToTable("WosContractors");
                });

            modelBuilder.Entity("QMD.DAL.Table.WosRelSiteAndContractor", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("ImportedToQmd")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("SubconCode")
                        .IsRequired()
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)");

                    b.Property<string>("WbsCode")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.HasKey("ID");

                    b.HasIndex("SubconCode", "WbsCode")
                        .IsUnique();

                    b.ToTable("WosRelSiteAndContractors");
                });

            modelBuilder.Entity("QMD.DAL.Table.WosSiteInfo", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("City")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("District")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<bool>("ImportedToQmd")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ProjectName")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ProjectTeam")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Province")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Region")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("SiteId")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("SiteName")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("SubProjectName")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Village")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("WbsCode")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.HasKey("ID");

                    b.HasIndex("WbsCode")
                        .IsUnique();

                    b.ToTable("WosSiteInfos");
                });

            modelBuilder.Entity("QMD.DAL.Table.emt.emt_dingtalktask", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("AppUrl")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("CreaterEmail")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Description")
                        .HasColumnType("longtext");

                    b.Property<string>("DingTaskId")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ExecutorEmails")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime>("LastOprTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("OrderKind")
                        .HasColumnType("int");

                    b.Property<string>("OrderNo")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("PcUrl")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<int>("ProcessNode")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Subject")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<int>("TaskStatus")
                        .HasColumnType("int");

                    b.Property<int>("TaskType")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.HasIndex("OrderNo");

                    b.ToTable("emt_dingtalktask");
                });

            modelBuilder.Entity("QMD.DAL.Table.emt.emt_dispatchprocess", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Code")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("DisOrderNo")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int>("FinishCount")
                        .HasColumnType("int");

                    b.Property<double>("FinishPercent")
                        .HasColumnType("double");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("OrderKind")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<int>("TotalCount")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.ToTable("emt_dispatchprocess");
                });

            modelBuilder.Entity("QMD.DAL.Table.emt.emt_histasknotice", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ChargeUserMail")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ChargeUserName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ChargeUserPhone")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("CheckUserStr")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("CodeSk")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("DepName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("FouLineUserStr")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("FstLineUserStr")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("InvUserEmailStr")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NetName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<int>("NoticeType")
                        .HasColumnType("int");

                    b.Property<string>("Operator")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<int>("OprType")
                        .HasColumnType("int");

                    b.Property<string>("ProductSpe")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RiskLevel")
                        .HasMaxLength(30)
                        .HasColumnType("varchar(30)");

                    b.Property<string>("SecLineUserStr")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("TaskNo")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("TaskStatus")
                        .HasColumnType("int");

                    b.Property<string>("TaskTitle")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("TaskUserStr")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("ThdLineUserStr")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("TriggerDay")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<DateTime?>("UtcEndTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("UtcStartTime")
                        .HasColumnType("datetime(6)");

                    b.HasKey("ID");

                    b.ToTable("emt_histasknotice");
                });

            modelBuilder.Entity("QMD.DAL.Table.emt.emt_involvedevice", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("CompileTime")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("InvBoardName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("InvDeviceType")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("InvHardVersion")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<int>("InvOrder")
                        .HasColumnType("int");

                    b.Property<string>("InvSoftVersion")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("OrderKind")
                        .HasColumnType("int");

                    b.Property<string>("OrderNo")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Remark")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("TarCompileTime")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("TarInvHardVersion")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("TarInvSoftVersion")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.HasKey("ID");

                    b.HasIndex("OrderNo");

                    b.ToTable("emt_involvedevice");
                });

            modelBuilder.Entity("QMD.DAL.Table.emt.emt_involvenmpinfo", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("CompileTime")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("InvOrder")
                        .HasColumnType("int");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NmpPatches")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("NmpSeries")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("NmpVersion")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<int>("OrderKind")
                        .HasColumnType("int");

                    b.Property<string>("OrderNo")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Remark")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("TarCompileTime")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("TarNmpPatch")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("TarNmpSeries")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("TarNmpVersion")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.HasKey("ID");

                    b.HasIndex("OrderNo");

                    b.ToTable("emt_involvenmpinfo");
                });

            modelBuilder.Entity("QMD.DAL.Table.emt.emt_involveproduct", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("ActObjectCount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("InvOrder")
                        .HasColumnType("int");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("ObjectCount")
                        .HasColumnType("int");

                    b.Property<string>("ObjectName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ObjectType")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<int>("OrderKind")
                        .HasColumnType("int");

                    b.Property<string>("OrderNo")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<int>("PlanObjectCount")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.HasKey("ID");

                    b.HasIndex("OrderNo");

                    b.ToTable("emt_involveproduct");
                });

            modelBuilder.Entity("QMD.DAL.Table.emt.emt_involveuser", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("HandleType")
                        .HasColumnType("int");

                    b.Property<string>("InvDept")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("InvEmail")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("InvNickName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<int>("InvOrder")
                        .HasColumnType("int");

                    b.Property<string>("InvPhone")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("InvPostCode")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("InvPostName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("InvUserLevel")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("InvUserName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("OperatorEmailStr")
                        .HasColumnType("longtext");

                    b.Property<int>("OrderKind")
                        .HasColumnType("int");

                    b.Property<string>("OrderNo")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Remark")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<int>("UserType")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.HasIndex("OrderNo");

                    b.ToTable("emt_involveuser");
                });

            modelBuilder.Entity("QMD.DAL.Table.emt.emt_personoutsourced", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("AuthYear")
                        .HasColumnType("int");

                    b.Property<string>("CertificateNo")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Company")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("EffEndTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("EffStartTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("IdNumber")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NickName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ProductSpe")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Province")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("UserLevel")
                        .HasMaxLength(10)
                        .HasColumnType("varchar(10)");

                    b.Property<string>("UserStatus")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.HasKey("ID");

                    b.ToTable("emt_personoutsourced");
                });

            modelBuilder.Entity("QMD.DAL.Table.emt.emt_personowned", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("AuthStatus")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<int>("AuthYear")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedNickName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("DepName")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("EditNickName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("EffEndTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("EffStartTime")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("PositionName")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ProductSpe")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("ThdCreatedTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("ThdUpdatedTime")
                        .HasMaxLength(100)
                        .HasColumnType("datetime(6)");

                    b.Property<string>("UserLevel")
                        .HasMaxLength(10)
                        .HasColumnType("varchar(10)");

                    b.Property<string>("UserNickName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("UserNo")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.HasKey("ID");

                    b.ToTable("emt_personowned");
                });

            modelBuilder.Entity("QMD.DAL.Table.emt.emt_transferrecord", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime>("ApplyTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Code")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("ConfirmTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("InvolveUserType")
                        .HasColumnType("int");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NewInvUserEmail")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<int>("OperateKind")
                        .HasColumnType("int");

                    b.Property<string>("OrderNo")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("OrgInvUserEmail")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Remark")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<int>("TransferStatus")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.HasIndex("OrderNo");

                    b.ToTable("emt_transferrecord");
                });

            modelBuilder.Entity("QMD.DAL.Table.emt_dispatchorders", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ApplyDepName")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime>("ApplyTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("ApproveTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("AttachInfo")
                        .HasColumnType("longtext");

                    b.Property<string>("BoardNames")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("Code")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<decimal>("CurProcess")
                        .HasColumnType("decimal(65,30)");

                    b.Property<string>("DeviceTypes")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("HardWares")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsEffective")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("LineRemark")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NetType")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("NmpPatches")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("NmpSeries")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("NmpVersions")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Operator")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<int>("OrderKind")
                        .HasColumnType("int");

                    b.Property<string>("OrderNo")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int>("OrderStatus")
                        .HasColumnType("int");

                    b.Property<int>("OrderType")
                        .HasColumnType("int");

                    b.Property<string>("OutputLine")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<DateTime>("PlanEndTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("PlanStartTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ProductLine")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ProductLmt")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Region")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Remark")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("RiskLevel")
                        .HasMaxLength(30)
                        .HasColumnType("varchar(30)");

                    b.Property<int>("RoleDataType")
                        .HasColumnType("int");

                    b.Property<string>("SoftWares")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("SolutionContent")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("SubmitTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("TechNoticeType")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Title")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.HasKey("ID");

                    b.HasIndex("OrderNo");

                    b.ToTable("emt_dispatchorders");
                });

            modelBuilder.Entity("QMD.DAL.Table.emt_executetask", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("AttachInfo")
                        .HasColumnType("longtext");

                    b.Property<string>("AuthorizeAttachInfo")
                        .HasColumnType("longtext");

                    b.Property<string>("Code")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("CurOperator")
                        .HasColumnType("longtext");

                    b.Property<string>("DisOrderNo")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("EndFinishedTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("EndRemindCount")
                        .HasColumnType("int");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("MiddleFinishedTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("MiddleRemindCount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NetOrderNo")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NoticeContent")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<int>("ObjectCount")
                        .HasColumnType("int");

                    b.Property<string>("ObjectUnit")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<int>("OprType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("PlanEndTime")
                        .HasColumnType("datetime(6)");

                    b.Property<decimal?>("PlanFinishPercent")
                        .HasColumnType("decimal(65,30)");

                    b.Property<DateTime?>("PlanMiddleTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("PlanStartTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("PlanUserCount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("RealEndTime")
                        .HasColumnType("datetime(6)");

                    b.Property<decimal?>("RealFinishPercent")
                        .HasColumnType("decimal(65,30)");

                    b.Property<DateTime?>("RealStartTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("RealUserCount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("RecentSubmitTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Remark")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("RepOrderNo")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("SolutionContent")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("SolutionId")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("StartFinishedTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("StartRemindCount")
                        .HasColumnType("int");

                    b.Property<string>("StepContent")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("TaskNo")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("TaskStatus")
                        .HasColumnType("int");

                    b.Property<string>("TaskTitle")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<int>("UrgingCount")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.HasIndex("DisOrderNo");

                    b.HasIndex("NetOrderNo");

                    b.HasIndex("RepOrderNo");

                    b.HasIndex("TaskNo");

                    b.ToTable("emt_executetask");
                });

            modelBuilder.Entity("QMD.DAL.Table.emt_extproperties", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Code")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<int>("OrderKind")
                        .HasColumnType("int");

                    b.Property<int>("OrderType")
                        .HasColumnType("int");

                    b.Property<int>("PropOrder")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ValColumnName")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.HasKey("ID");

                    b.ToTable("emt_extproperties");
                });

            modelBuilder.Entity("QMD.DAL.Table.emt_extvalues", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Code")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ExtValue1")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ExtValue10")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ExtValue11")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ExtValue12")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ExtValue13")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ExtValue14")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ExtValue15")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ExtValue2")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ExtValue3")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ExtValue4")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ExtValue5")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ExtValue6")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ExtValue7")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ExtValue8")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ExtValue9")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("OperateKind")
                        .HasColumnType("int");

                    b.Property<string>("OrderNo")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Remark")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.HasKey("ID");

                    b.HasIndex("OrderNo");

                    b.ToTable("emt_extvalues");
                });

            modelBuilder.Entity("QMD.DAL.Table.emt_netproviderrel", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("AttachList")
                        .HasColumnType("longtext");

                    b.Property<string>("CodeSk")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("DelayApprContent")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("DelayAttach")
                        .HasColumnType("longtext");

                    b.Property<string>("DelayReason")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<int>("DelayStatus")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DelayTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("DisOrderNo")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<bool>("IdDelay")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("IsChange")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("NetSource")
                        .HasColumnType("int");

                    b.Property<int>("ObjFinishCount")
                        .HasColumnType("int");

                    b.Property<int>("ObjTotalCount")
                        .HasColumnType("int");

                    b.Property<string>("ObjUnit")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("OrderNo")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("PlanContent")
                        .HasMaxLength(4000)
                        .HasColumnType("varchar(4000)");

                    b.Property<DateTime>("PlanEndTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("PlanStartTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Remark")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.HasIndex("CodeSk");

                    b.HasIndex("DisOrderNo");

                    b.HasIndex("OrderNo");

                    b.ToTable("emt_netproviderrel");
                });

            modelBuilder.Entity("QMD.DAL.Table.emt_noticerecords", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Code")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("DingDingFlag")
                        .HasColumnType("int");

                    b.Property<string>("DingDingMessage")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("MailFlag")
                        .HasColumnType("int");

                    b.Property<string>("MailMessage")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("MsgFlag")
                        .HasColumnType("int");

                    b.Property<string>("MsgMessage")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("NoticeContent")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("NoticeTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ObjectId")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("ObjectStatus")
                        .HasColumnType("int");

                    b.Property<string>("ObjectStatusStr")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<int>("OperateKind")
                        .HasColumnType("int");

                    b.Property<int>("Priority")
                        .HasColumnType("int");

                    b.Property<string>("RecUserName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Remark")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.HasKey("ID");

                    b.HasIndex("ObjectId");

                    b.ToTable("emt_noticerecords");
                });

            modelBuilder.Entity("QMD.DAL.Table.emt_operationrecords", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Code")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ObjectId")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("OldOperateStatus")
                        .HasColumnType("int");

                    b.Property<string>("OperateContent")
                        .HasColumnType("longtext");

                    b.Property<int>("OperateKind")
                        .HasColumnType("int");

                    b.Property<int>("OperateLevel")
                        .HasColumnType("int");

                    b.Property<int>("OperateStatus")
                        .HasColumnType("int");

                    b.Property<DateTime?>("OperateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("OperateType")
                        .HasColumnType("int");

                    b.Property<string>("OperateUserName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("OperateUserRole")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<int>("ProcessNode")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.HasKey("ID");

                    b.HasIndex("ObjectId");

                    b.ToTable("emt_operationrecords");
                });

            modelBuilder.Entity("QMD.DAL.Table.emt_reportorders", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("ApplyTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("AttachInfo")
                        .HasColumnType("longtext");

                    b.Property<string>("AttchFileNames")
                        .HasColumnType("longtext");

                    b.Property<string>("BoardNames")
                        .HasMaxLength(2000)
                        .HasColumnType("varchar(2000)");

                    b.Property<int>("CloseType")
                        .HasColumnType("int");

                    b.Property<string>("Code")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("CodeSk")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<decimal?>("CurProcess")
                        .HasColumnType("decimal(65,30)");

                    b.Property<string>("DeviceTypes")
                        .HasMaxLength(2000)
                        .HasColumnType("varchar(2000)");

                    b.Property<string>("DisOrderNo")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsEffective")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsNeedGuarantee")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsReinstated")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("LineRemark")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NetOrderNo")
                        .HasColumnType("longtext");

                    b.Property<int>("OrderKind")
                        .HasColumnType("int");

                    b.Property<string>("OrderNo")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<int>("OrderStatus")
                        .HasColumnType("int");

                    b.Property<int>("OrderType")
                        .HasColumnType("int");

                    b.Property<string>("OutputLine")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("PlanEndTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("PlanStartTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ProductLMT")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ProductLine")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("RealEndTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("RealStartTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Remark")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("RiskLevel")
                        .HasMaxLength(30)
                        .HasColumnType("varchar(30)");

                    b.Property<string>("SoftwareForm")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<DateTime?>("SubmitTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("TargetCount")
                        .HasColumnType("int");

                    b.Property<string>("TargetUnit")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("TaskContent")
                        .HasColumnType("longtext");

                    b.Property<string>("Title")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("UpgradeReason")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.HasKey("ID");

                    b.HasIndex("OrderNo");

                    b.ToTable("emt_reportorders");
                });

            modelBuilder.Entity("QMD.DAL.Table.emt_solutioninfo", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Code")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("ExeEndTime")
                        .HasColumnType("int");

                    b.Property<int>("ExeStartTime")
                        .HasColumnType("int");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NeedUserLevel")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<int>("NeedUserSpe")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<int>("SolLevel")
                        .HasColumnType("int");

                    b.Property<string>("SolName")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.HasKey("ID");

                    b.ToTable("emt_solutioninfo");
                });

            modelBuilder.Entity("QMD.DAL.Table.emt_syncitrproductinfo", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Code")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("CreatedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("DeviceType")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<bool>("IsActived")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedUser")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Operator")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("OutputLine")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ProductGroup")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ProductLMT")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ProductLine")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ProductSpe")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Profession")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Remark")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.HasKey("ID");

                    b.ToTable("emt_syncitrproductinfo");
                });
#pragma warning restore 612, 618
        }
    }
}
