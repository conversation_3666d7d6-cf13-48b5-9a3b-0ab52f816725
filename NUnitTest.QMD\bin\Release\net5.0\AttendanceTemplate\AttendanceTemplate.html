﻿<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset='UTF-8'>
    <title></title>
    <style>
        body {
            text-align: center;
            width: 240mm;
            margin: 0 auto;
        }

        .logo {
            margin: 0 auto;
            /* background-color: aqua; */
            padding-bottom: 0px;
        }

        .covert {
            margin-top: 15px;
        }

        .logo-left {
            float: left;
            display: inline;
        }

        .logo-image {
            max-width: 150px;
        }

        .logo-right {
            float: left;
            display: inline;
            max-width: 60%;
            margin-left: 15%;
            font-size: large;
            font-weight: bolder;
        }

        .first-table {
            width: 100%;
            /* 边框设置是否会被分开 */
            border-collapse: collapse;
            border-spacing: 0px 10px;
            border: 3px solid black;
        }
        .example-table {
            border-collapse: collapse;
            margin-top: 5px;
            width: 100%;
        }
        .example-table td {
            text-align: left;
            background-color: white;
            background-clip: content-box;
            border-radius: 3%;
            font-size: x-small;
        }

        .table {
            border-collapse: collapse;
            margin-top: 5px;
            width: 100%;
        }

        .table td {
            text-align: left;
            background-color: white;
            background-clip: content-box;
            border-radius: 3%;
            font-size: x-small;
            border: 1px black solid;
        }
        .special_color td {
            background-color: red;
        }
        .pic-tip {
            padding: 10px 0;
        }

        .image-border {
            border: 1px solid white;
            border-radius: 3px;
            /* max-width: 100%;
            height: auto; */
            width: 100%;
            height: 100%;
            background-size: contain;
        }

        .line_view .label {
            display: inline-block;
            width: 120px;
        }

        .line_view .value {
            border-bottom: 1px solid #000;
            width: 150px;
            display: inline-block;
        }

        .line_view_left .label {
            width: 150px
        }

        .line_view_left .value,
        .line_view_right .value {
            width: 200px;
        }

        .line_view_right .label {
            width: 60px;
        }
    </style>

</head>
<body>
    <div class="logo">
        <div class="logo-left"><img src="%logo%" alt="" class="logo-image"></div>
        <div class="logo-right">PT. Fiberhome Technologies Indonesia</div>
    </div>
    <div class="covert">

        <table class="example-table">
            <tr>
                <td>Address : APL Tower Lt. 30 Suite 7</td>
            </tr>
            <tr>
                <td>Jl. Letjen S Parman Kav. 28</td>
            </tr>
            <tr>
                <td>Jakarta - 11470</td>
            </tr>
        </table>
        <table class="example-table">
            <tr>
                <td class="line_view">
                    <p class="label">Employee Name: </p>
                    <p class="value">%EmployeeName%</p>
                </td>
                <td class="line_view">
                    <p class="label">Title: </p>
                    <p class="value">%Title%</p>
                </td>
            </tr>
            <tr>
                <td class="line_view">
                    <p class="label">Department:</p>
                    <p class="value">%Department%</p>
                </td>
                <td class="line_view">
                    <p class="label">Active Supervisor:</p>
                    <p class="value">%ActiveSupervisor%</p>
                </td>
            </tr>
        </table>

        <table class="table">
            <tr>
                <td>Date</td>
                <td>StartTime</td>
                <td>EndTime</td>
                <td>Remark1</td>
                <td>Remark2</td>
                <td>GPS</td>
            </tr>
            %param%
        </table>

        <table class="example-table">
            <tr>
                <td class="line_view line_view_left">
                    <p class="label">Employee Signature ：</p>
                    <p class="value">&nbsp;</p>
                </td>
                <td class="line_view line_view_right">
                    <p class="label">Date ：</p>
                    <p class="value">&nbsp;</p>
                </td>
            </tr>
            <tr>
                <td class="line_view line_view_left">
                    <p class="label">Supervisor Signature ：</p>
                    <p class="value">&nbsp;</p>
                </td>
                <td class="line_view line_view_right">
                    <p class="label">Date ：</p>
                    <p class="value">&nbsp;</p>
                </td>
            </tr>
            <tr>
                <td class="line_view line_view_left">
                    <p class="label">Manager Signature ：</p>
                    <p class="value">&nbsp;</p>
                </td>
                <td class="line_view line_view_right">
                    <p class="label">Date ：</p>
                    <p class="value">&nbsp;</p>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>