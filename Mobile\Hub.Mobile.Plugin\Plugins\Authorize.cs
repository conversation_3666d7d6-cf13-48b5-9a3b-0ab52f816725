﻿using Hub.Mobile.Interface;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Xamarin.Forms;

namespace Hub.Mobile.Plugin.Plugins
{
    public class Authorize : IPlugin
    {
        IAccountService account = DependencyService.Get<IAccountService>();
        public async Task<ExecResult> GetToken()
        {
            ExecResult result = new ExecResult() {Success=true, IsString = true };
            var currentUser = await account.GetCurrentUser();
            result.Result = currentUser?.Token;
            return result;
        }
        public async Task<ExecResult> GetCurrentUser()
        {
            ExecResult result = new ExecResult() { Success = true};
            var currentUser = (await account.GetCurrentUser())??new Model.UserInfo() { };
            result.Result = JsonConvert.SerializeObject(new { currentUser.Email, currentUser.NickName, currentUser.Position, currentUser.Areas });
            return result;
        }
    }
}
