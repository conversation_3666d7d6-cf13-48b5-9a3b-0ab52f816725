﻿using Hub.Mobile.ApiClient;
using Hub.Mobile.ApiClient.Hub;
using Hub.Mobile.Interface;
using Hub.Mobile.Model;
using Hub.Mobile.Resources;
using NLog;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WebApiClient;
using WebApiClient.Parameterables;
using Xamarin.Forms;

namespace Hub.Mobile.ViewModels
{
    public class UploadLogViewModel : BasePageViewModel
    {
        static ILogger logger = LogManager.GetCurrentClassLogger();
        public Command CloseCommand { get; }
        public UploadLogViewModel()
        {
            CloseCommand = new Command(OnCloseClicked);
            UploadLog();
        }
        private async void OnCloseClicked(object obj)
        {
            await Navigation.PopPopupAsync();
        }
        private string tip = string.Empty;
        public string Tip
        {
            get { return tip; }
            set
            {
                SetProperty(ref tip, value);
            }
        }
        bool btnCloseEnabled = false;
        public bool BtnCloseEnabled { get { return btnCloseEnabled; } set { SetProperty(ref btnCloseEnabled, value); } }
        private async Task UploadLog()
        {
            try
            {
                var logPath = Path.Combine(System.Environment.GetFolderPath(System.Environment.SpecialFolder.MyDocuments), "logs");
                if (string.IsNullOrWhiteSpace(logPath))
                {
                    Tip = "log path is null";
                }
                else
                {
                    if (!Directory.Exists(logPath))
                    {
                        logger.Info("log init ");
                        if (!Directory.Exists(logPath))
                        {
                            Tip = "log path doesn't exist";
                        }
                    }
                    else
                    {
                        var files = Directory.GetFiles(logPath).Where(p => Path.GetExtension(p) != ".zip").OrderByDescending(p => File.GetCreationTimeUtc(p)).Take(7);
                        if (files.Count() == 0)
                        {
                            Tip = "log doesn't generate";
                        }
                        else
                        {
                            string desArchiveFullFileName = Path.Combine(logPath, $"{DateTime.UtcNow.Ticks}.zip");
                            Tip = UIResources.Compressing;
                            await Task.Factory.StartNew(() => { DependencyService.Get<IZipService>().ZipFiles(logPath, desArchiveFullFileName, files.Select(p => Path.GetFileName(p)).ToArray()); });
                            Tip = UIResources.Uploading;
                            var response = await HttpApi.Resolve<IHubApiClient>().UploadLog(new MulitpartFile(desArchiveFullFileName), new LogUploadRequest
                            {
                                DeviceBrand = DependencyService.Get<IDeviceService>().GetDeviceBrand(),
                                DeviceModel = DependencyService.Get<IDeviceService>().GetDeviceModel(),
                                SystemVersion = DependencyService.Get<IDeviceService>().GetSystemVersion(),
                                AppVersion = DependencyService.Get<IUpgrade>().GetVersion()
                            });
                            if (response.Flag)
                            {
                                if (File.Exists(desArchiveFullFileName))
                                {
                                    File.Delete(desArchiveFullFileName);
                                }
                                Tip = UIResources.Finished;
                            }
                            else
                            {
                                if (!string.IsNullOrWhiteSpace(response.Message))
                                {
                                    Tip = response.Message;
                                }
                                else
                                {
                                    Tip = ExceptionResources.ExceptionLevel_Error;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                Tip = ExceptionResources.ExceptionLevel_Error;
            }
            finally
            {
                BtnCloseEnabled = true;
            }
        }
    }
}
