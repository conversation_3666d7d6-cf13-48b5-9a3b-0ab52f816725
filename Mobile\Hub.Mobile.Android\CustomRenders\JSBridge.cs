﻿using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Views;
using Android.Webkit;
using Android.Widget;
using Hub.Mobile.CommonControl;
using Hub.Mobile.Plugin;
using Java.Interop;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Xamarin.Forms;

namespace Hub.Mobile.Droid.CustomRenders
{
    public class JSBridge : Java.Lang.Object
    {
        ILogger logger= LogManager.GetCurrentClassLogger();
        readonly WeakReference<HybridWebViewRenderer> hybridWebViewRenderer;

        public JSBridge(HybridWebViewRenderer hybridRenderer)
        {
            hybridWebViewRenderer = new WeakReference<HybridWebViewRenderer>(hybridRenderer);
        }

        [JavascriptInterface]
        [Export("handleRequest")]
        public void HandleRequest(string successCallBackId, string errorCallBackId, string service, string action, string args)
        {
            try
            {
                HybridWebViewRenderer hybridRenderer;
                if (hybridWebViewRenderer != null && hybridWebViewRenderer.TryGetTarget(out hybridRenderer))
                {
                    var result = PluginService.Exec(service, action, args).Result;
                    Device.BeginInvokeOnMainThread(() =>
                    {
                        try
                        {
                            if (result.Success)
                            {
                                ((HybridWebView)hybridRenderer.Element).Eval($"hub_mobile_drive.callBack('{successCallBackId}',{result.Result})");
                            }
                            else
                            {
                                ((HybridWebView)hybridRenderer.Element).Eval($"hub_mobile_drive.callBack('{errorCallBackId}','{result.Message}')");
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.Error(ex);
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
            }
        }
        [JavascriptInterface]
        [Export("mobileBack")]
        public void MobileBack()
        {
            try
            {
                Xamarin.Forms.WebView webView = null;
                HybridWebViewRenderer hybridRenderer;
                if (hybridWebViewRenderer != null && hybridWebViewRenderer.TryGetTarget(out hybridRenderer))
                {
                    webView = hybridRenderer.Element;
                }
                Device.BeginInvokeOnMainThread(async () =>
                {
                    try
                    {
                        if (webView != null && webView.CanGoBack)
                        {
                            webView.GoBack();
                        }
                        else
                        {
                            await Xamarin.Forms.Application.Current.
                          MainPage.Navigation.PopAsync();
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.Error(ex);
                    }
                });
            }
            catch (Exception ex)
            {
                logger.Error(ex);
            }
        }


    }
}