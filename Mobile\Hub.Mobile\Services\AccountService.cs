﻿using Acr.UserDialogs;
using Hub.Mobile.ApiClient;
using Hub.Mobile.DAL;
using Hub.Mobile.DAL.Tables;
using Hub.Mobile.Interface;
using Hub.Mobile.Model;
using Hub.Mobile.Resources;
using Hub.Mobile.Views;
using Rg.Plugins.Popup.Services;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using WebApiClient;
using Xamarin.Forms;

[assembly: Xamarin.Forms.Dependency(typeof(Hub.Mobile.Services.AccountService))]
namespace Hub.Mobile.Services
{
    public class AccountService : IAccountService
    {
        private static IAutoMapperService autoMapperService = DependencyService.Get<IAutoMapperService>();
        private static UserInfo loginUser =null;
        private static bool userInited = false;
        public async Task<UserInfo> GetCurrentUser()
        {
            if (!userInited)
            {
                var user = await MobileSQLiteHelper.Current.FirstOrDefaultByCreatedTime<LoginUser>();
                if (user != null)
                {
                    loginUser = autoMapperService.AutoMapper.Map<LoginUser, UserInfo>(user);
                }
                userInited = true;
            }
            return loginUser;
        }
        public async Task LoginAsync(UserInfo userInfo)
        {
            var hisDatas = await MobileSQLiteHelper.Current.QueryList<LoginUser>();
            await MobileSQLiteHelper.Current.Insert(
                new LoginUser() { CreatedTime = DateTime.UtcNow, Email = userInfo.Email, UserId = userInfo.UserId, NickName = userInfo.NickName, Position = userInfo.Position, Areas = userInfo.Areas, Token = userInfo.Token }
            );
            foreach (var hisData in hisDatas)
            {
                await MobileSQLiteHelper.Current.Delete(hisData);
            }
            loginUser = userInfo;
        }
        public async Task LogoutAsync(bool showLoginDialog = true, Action loginDialogClosedCallBack = null)
        {
            var ret = true;
            if (!string.IsNullOrWhiteSpace((await GetCurrentUser())?.Token))
            {
                //当前已登录,二次确认
                ret = await UserDialogs.Instance.ConfirmAsync(UIResources.LogoutConfirmMsg, okText: UIResources.Ok, cancelText: UIResources.Cancel);
            }
            if (ret)
            {
                var hisDatas = await MobileSQLiteHelper.Current.QueryList<LoginUser>();
                foreach (var hisData in hisDatas)
                {
                    await MobileSQLiteHelper.Current.Delete(hisData);
                }
                loginUser = null;
                if (showLoginDialog)
                {
                    await PopupNavigation.Instance.PushAsync(new LoginPage(loginDialogClosedCallBack));
                }
            }
        }
        public async Task<bool> ValidateTokenAsync()
        {
            var currentUser=await GetCurrentUser();
            if (string.IsNullOrWhiteSpace(currentUser?.Token))
            {
                return false;
            }
            #region api验证 token
            try
            {
                await HttpApi.Resolve<ILoginApiClient>().ValidateToken($"Bearer {currentUser?.Token}");
                return true;

            }
            catch (Exception ex)
            {
                if (ex.Message != null && ex.Message.Contains("Unauthorized"))
                {
                    return false;
                }
            }
            return true;
            #endregion
        }
        public async Task<bool> SubmitCheckAsync(bool autoShowLoginDialog = true, bool autoShowNetworkDialog = true)
        {
            bool result = false;
            if (DependencyService.Get<INetWork>().IsNetworkAvailable())
            {
                result = await ValidateTokenAsync();
                if (!result && autoShowLoginDialog)
                {
                    await PopupNavigation.Instance.PushAsync(new LoginPage());
                }
            }
            else
            {
                if (autoShowNetworkDialog)
                {
                    await UserDialogs.Instance.AlertAsync(UIResources.NetworkUnavailable, okText: UIResources.Ok);
                }
            }
            return result;
        }
        public async Task OpenSettingPage(INavigation navigation)
        {
            await navigation.PushAsync(new SettingPage());
        }
        public async Task<bool> IsLogin()
        {
            return string.IsNullOrWhiteSpace((await GetCurrentUser())?.Token) ? false : true;
        }
    }
}
