﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Utility
{
    public class HtmlHelper
    {
        public static string CreateTableHtmlStr(List<TableDate> data, string tableClassName = "highlight", string subHeaderClassName = "sub-header")
        {
            var result = new StringBuilder();
            result.AppendLine($"<table class=\"{tableClassName}\">");
            result.AppendLine("<tbody>");
            foreach (var item in data)
            {
                //一级标题
                result.AppendLine($"<tr class=\"{subHeaderClassName}\">");
                result.AppendLine($"<th colspan=\"2\">{item.Title}</th>");
                result.AppendLine("</tr>");
                //标题下的内容
                foreach (var row in item.RowDatas)
                {
                    if (row != null && !string.IsNullOrEmpty(row.Title))
                    {
                        result.AppendLine("<tr>");
                        result.AppendLine($"<td>{row.Title}</td>");
                        if (row.Title == "实施工单号")
                        {
                            result.AppendLine($"<td id = \"taskNo\">{row.Content}</td>");
                        }
                        else
                        {
                            if (row.Title.Contains("工单号"))
                            {
                                result.AppendLine($"<td id = \"orderNo\">{row.Content}</td>");
                            }
                            else
                            {
                                result.AppendLine($"<td>{row.Content}</td>");
                            }
                        }
                        result.AppendLine("</tr>");
                    }
                }
            }
            result.AppendLine("</tbody>");
            result.AppendLine($"</table>");
            return result.ToString();
        }

        /// <summary>
        /// 将数据转换成html 的 table
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="data"></param>
        /// <returns></returns>
        public static string CreateListTableHtmlStr<T>(List<T> data, string tableClass = "width-auto") where T : class
        {
            var table = data.ToDataTable();
            var result = new StringBuilder();
            result.AppendLine($"<table class=\"highlight {tableClass}\">");
            result.AppendLine("<tbody>");
            //增加header
            result.AppendLine($"<tr class=\"sub-header\">");
            var headerCols = table.Columns;
            foreach (DataColumn col in headerCols) 
            {
                result.AppendLine($"<th>{col.ColumnName}</th>");
            }
            result.AppendLine("</tr>");
            //增加数据
            foreach (DataRow row in table.Rows)
            {
                result.AppendLine("<tr>");
                foreach (DataColumn col in headerCols)
                {
                    result.AppendLine($"<td class=\"first-child-nobg\">{row[col.ColumnName]}</td>");
                }
                result.AppendLine("</tr>");
            }
            result.AppendLine("</tbody>");
            result.AppendLine($"</table>");
            return result.ToString();
        }

        /// <summary>
        /// 将数据转换成html 的 table
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="data"></param>
        /// <returns></returns>
        public static string CreateListTableHtmlStr(List<Dictionary<string,object>> data, string tableClass = "width-auto")
        {
            var result = new StringBuilder();
            result.AppendLine($"<table class=\"highlight {tableClass}\">");
            result.AppendLine("<tbody>");
            //增加header
            result.AppendLine($"<tr class=\"sub-header\">");
            var headerCols = data.FirstOrDefault().Keys.ToList();
            foreach (string col in headerCols)
            {
                result.AppendLine($"<th>{col}</th>");
            }
            result.AppendLine("</tr>");
            //增加数据
            foreach (Dictionary<string,object> row in data)
            {
                result.AppendLine("<tr>");
                foreach (string col in headerCols)
                {
                    result.AppendLine($"<td class=\"first-child-nobg\">{row[col]}</td>");
                }
                result.AppendLine("</tr>");
            }
            result.AppendLine("</tbody>");
            result.AppendLine($"</table>");
            return result.ToString();
        }
    }
    public class TableDate
    {
        /// <summary>
        /// 大标题
        /// </summary>
        public string Title { get; set; }
        /// <summary>
        /// 行数据
        /// </summary>
        public List<RowData> RowDatas { get; set; } = new List<RowData>();
    }
    public class RowData
    {
        public string Title { get; set; }
        public string Content { get; set; }
    }
}
