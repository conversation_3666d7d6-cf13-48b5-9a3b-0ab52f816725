﻿using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Views;
using Android.Widget;
using AndroidX.Core.Content;
using Hub.Mobile.Const;
using Hub.Mobile.Interface;
using Java.IO;
using Java.Net;
using Java.Util;
using Java.Util.Zip;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using Xamarin.Forms;

[assembly: Dependency(typeof(Hub.Mobile.Droid.Imps.Upgrade))]
namespace Hub.Mobile.Droid.Imps
{
    public class Upgrade : IUpgrade
    {
        static NLog.Logger log = NLog.LogManager.GetCurrentClassLogger();
        public static string AppSavePath
        {
            get
            {
                return Path.Combine(System.Environment.GetFolderPath(System.Environment.SpecialFolder.LocalApplicationData), "bakti_upgrade");
            }
        }
        public string GetVersion()
        {
            string packName = MainActivity.AppContext.PackageName;
            return MainActivity.AppContext.PackageManager.GetPackageInfo(packName, 0).VersionName;
        }
        public void DoUpgrade(string downloadUrl, string packageName, Action<EnumUpgradeState, object> callBack = null)
        {
            try
            {
                URL url = new URL(downloadUrl);

                HttpURLConnection conn = (HttpURLConnection)url.OpenConnection();
                conn.SetRequestProperty("User-Agent", "PacificHttpClient");
                conn.Connect();
                conn.ConnectTimeout = 10000;
                conn.ReadTimeout = 20000;
                int length = conn.ContentLength;
                System.IO.Stream stream = conn.InputStream;
                if (!Directory.Exists(AppSavePath))
                {
                    Directory.CreateDirectory(AppSavePath);
                }
                else
                {
                    var files = Directory.GetFiles(AppSavePath);
                    for (int i = 0; i < files.Length; i++)
                    {
                        System.IO.File.Delete(files[i]);
                    }
                }
                string filePath = Path.Combine(AppSavePath, packageName);
                Java.IO.File zipFile = new Java.IO.File(filePath);
                FileOutputStream fileOS = new FileOutputStream(zipFile);
                int count = 0;
                int intBufferSize = 16384 * 8;
                byte[] byteBuffer = new byte[intBufferSize];
                int numread = 0;
                int progress = 0;
                while ((numread = stream.Read(byteBuffer, 0, intBufferSize)) > 0)
                {
                    count += numread;
                    progress = (int)(((float)count / length) * 100);
                    if (callBack != null)
                    {
                        callBack(EnumUpgradeState.正在下载, (double)count / length);
                    }
                    fileOS.Write(byteBuffer, 0, numread);
                }
                fileOS.Close();
                stream.Close();
                if (numread == 0)
                {
                    if (callBack != null)
                    {
                        callBack(EnumUpgradeState.正在解压, null);
                    }
                    #region 解压apk
                    try
                    {
                        string apkFile = string.Empty;
                        ZipFile zfile = new ZipFile(filePath);
                        IEnumeration zList = zfile.Entries();
                        ZipEntry ze = null;
                        byte[] buf = new byte[1024];
                        OutputStream os = null;
                        InputStream ism = null;
                        while (zList.HasMoreElements)
                        {
                            ze = (ZipEntry)zList.NextElement();
                            if (!ze.IsDirectory && ze.Name.IndexOf("apk") > 0)
                            {
                                apkFile = Path.Combine(AppSavePath, ze.Name);
                                os = new FileOutputStream(Path.Combine(AppSavePath, ze.Name));
                                ism = new BufferedInputStream(zfile.GetInputStream(ze));
                                int readLen = 0;
                                while ((readLen = ism.Read(buf, 0, 1024)) != -1)
                                {
                                    os.Write(buf, 0, readLen);
                                }
                                break;
                            }

                        }
                        ism.Close();
                        os.Close();
                        if (callBack != null)
                        {
                            callBack(EnumUpgradeState.解压完成, null);
                        }
                        //删除本地zip
                        System.IO.File.Delete(filePath);
                        //(new Java.IO.File(filePath)).Delete();
                        try
                        {
                            InstallApk(apkFile);
                            if (callBack != null)
                            {
                                callBack(EnumUpgradeState.已启动安装, null);
                            }
                        }
                        catch (Exception ex)
                        {
                            log.Error(ex, ex.Message);
                            if (callBack != null)
                            {
                                callBack(EnumUpgradeState.安装失败, null);
                            }
                        }

                    }
                    catch (Exception ex)
                    {
                        log.Error(ex, ex.Message);
                        if (callBack != null)
                        {
                            callBack(EnumUpgradeState.解压失败, null);
                        }
                    }
                    #endregion
                }
            }
            catch (Exception ex)
            {
                log.Error(ex, ex.Message);
                callBack(EnumUpgradeState.下载失败, null);
            }
        }
        private void InstallApk(string apkFile)
        {
            Intent intent = new Intent(Intent.ActionView);
            intent.AddFlags(ActivityFlags.NewTask);
            if (Build.VERSION.SdkInt >= Android.OS.BuildVersionCodes.N)
            {
                intent.AddFlags(ActivityFlags.GrantReadUriPermission);
                Android.Net.Uri contentUri = FileProvider.GetUriForFile(MainActivity.AppContext, $"{MainActivity.AppContext.PackageName}.fileprovider", new Java.IO.File(apkFile));
                intent.SetDataAndType(contentUri, "application/vnd.android.package-archive");
            }
            else
            {
                intent.SetDataAndType(Android.Net.Uri.FromFile(new Java.IO.File(apkFile)), "application/vnd.android.package-archive");
            }
            MainActivity.AppContext.StartActivity(intent);
        }
    }
}