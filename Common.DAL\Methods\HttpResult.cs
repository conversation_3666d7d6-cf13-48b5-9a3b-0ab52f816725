﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Common.DAL.Methods
{
    public class HttpResult
    {
        //成功后返回的数据
        public static BaseRes<T> Success<T>(in T data)
        {
            return new BaseRes<T>(data);
        }
        public static BaseRes<object> Success(object data)
        {
            return new BaseRes<object>(data);
        }
        public static BaseRes Success()
        {
            return new BaseRes();
        }
        public static PageRes<T> PaginationSuccess<T>(List<T> data, int totalCount)
        {
            return new PageRes<T>() { Data = data, TotalCount = totalCount, Flag = true, Message = null };
        }

        //失败后返回的数据
        public static BaseRes<T> Fail<T>(string msg, T data)
        {
            return new BaseRes<T>(data, false, msg);
        }
        public static BaseRes<T> Fail<T>(string msg)
        {
            return new BaseRes<T>(false, msg);
        }
        public static BaseRes Fail(string msg)
        {
            return new BaseRes(false, msg);
        }
        public static PageRes<T> PaginationFail<T>(string msg, List<T> data)
        {
            return new PageRes<T>
            {
                Data = data,
                Message = msg,
                TotalCount = 0,
                Flag = false
            };
        }
    }
}
