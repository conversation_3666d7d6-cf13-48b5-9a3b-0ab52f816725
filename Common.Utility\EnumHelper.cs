﻿using Org.BouncyCastle.Asn1.X509.Qualified;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Common.Utility
{
    public static class EnumHelper
    {
        /// <summary>
        /// 将枚举类型转换为List
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public static List<EnumResponse> TryToList(this Type type)
        {
            var result = new List<EnumResponse>();

            foreach (var item in Enum.GetValues(type))
            {
                var response = new EnumResponse
                {
                    Key = item.ToString(),
                    Value = Convert.ToInt32(item)
                };

                DescriptionAttribute attribute = Attribute.GetCustomAttribute(item.GetType().GetField(item.ToString()), typeof(DescriptionAttribute)) as DescriptionAttribute;
                response.Description = attribute == null ? item.ToString() : attribute.Description;
                //var objArray = item.GetType().GetField(item.ToString()).GetCustomAttributes(typeof(DescriptionAttribute), true);
                //if (objArray.Any()) response.Description = (objArray.First() as DescriptionAttribute).Description;
                result.Add(response);
            }
            return result;
        }

        /// <summary>
        /// 将枚举类型转换为List
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public static List<EnumResponse> TryToList<T>(this Type type) where T : Attribute
        {
            var result = new List<EnumResponse>();

            foreach (var item in Enum.GetValues(type))
            {
                var response = new EnumResponse
                {
                    Key = item.ToString(),
                    Value = Convert.ToInt32(item),
                    EnumOrder = Convert.ToInt32(item)
                };

                DescriptionAttribute attribute = Attribute.GetCustomAttribute(item.GetType().GetField(item.ToString()), typeof(DescriptionAttribute)) as DescriptionAttribute;
                response.Description = attribute == null ? item.ToString() : attribute.Description;
                //var objArray = item.GetType().GetField(item.ToString()).GetCustomAttributes(typeof(DescriptionAttribute), true);
                //if (objArray.Any()) response.Description = (objArray.First() as DescriptionAttribute).Description;
                try
                {
                    T defineAttr = Attribute.GetCustomAttribute(item.GetType().GetField(item.ToString()), typeof(T)) as T;
                    var propties = typeof(T).GetProperties();
                    foreach (var prop in propties)
                    {
                        if (prop.Name == "Order" && prop.PropertyType.Name == "Int32")
                        {
                            response.EnumOrder = (int)prop.GetValue(defineAttr);
                        }
                    }
                }
                catch
                {
                }
                result.Add(response);
            }
            return result;
        }


        /// <summary>
        /// 将枚举类型转换为List
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public static List<EnumResponse> TryToList<T1,T2>(this Type type) where T1: Attribute where T2:Attribute
        {
            var result = new List<EnumResponse>();

            foreach (var item in Enum.GetValues(type))
            {
                var response = new EnumResponse
                {
                    Key = item.ToString(),
                    Value = Convert.ToInt32(item),
                    EnumOrder = Convert.ToInt32(item)
                };

                DescriptionAttribute attribute = Attribute.GetCustomAttribute(item.GetType().GetField(item.ToString()), typeof(DescriptionAttribute)) as DescriptionAttribute;
                response.Description = attribute == null ? item.ToString() : attribute.Description;
                //var objArray = item.GetType().GetField(item.ToString()).GetCustomAttributes(typeof(DescriptionAttribute), true);
                //if (objArray.Any()) response.Description = (objArray.First() as DescriptionAttribute).Description;
                try
                {
                    T1 defineAttr = Attribute.GetCustomAttribute(item.GetType().GetField(item.ToString()), typeof(T1)) as T1;
                    T2 defineAttr2 = Attribute.GetCustomAttribute(item.GetType().GetField(item.ToString()), typeof(T2)) as T2;
                    var propties = typeof(T1).GetProperties();
                    var propties2 = typeof(T2).GetProperties();
                    foreach (var prop in propties)
                    {
                        if (prop.Name == "Order" && prop.PropertyType.Name == "Int32")
                        {
                            response.EnumOrder = (int)prop.GetValue(defineAttr);
                        }
                    }
                    foreach (var prop in propties2)
                    {
                        if (prop.Name == "OrderKind" && prop.PropertyType.Name == "Int32")
                        {
                            response.EnumDataType = (int)prop.GetValue(defineAttr2);
                        }
                    }
                }
                catch
                {
                }
                result.Add(response);
            }
            return result;
        }


        /// <summary>
        /// 获取枚举的注释信息
        /// </summary>
        /// <param name="enumVal"></param>
        /// <returns></returns>
        public static string GetDescription(this Enum value)
        {
            string result = string.Empty;
            try
            {
                FieldInfo field = value.GetType().GetField(value.ToString());
                DescriptionAttribute attribute = Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute)) as DescriptionAttribute;
                result = attribute == null ? value.ToString() : attribute.Description;
            }
            catch
            {
                
            }
            return result;
        }

        /// <summary>
        /// 获取枚举的Key信息
        /// </summary>
        /// <param name="enumVal"></param>
        /// <returns></returns>
        public static string GetEnumKey(this Enum value)
        {
            string result = string.Empty;
            try
            {
                result = value.ToString();
            }
            catch
            {
            }
            return result;
        }

        /// <summary>
        /// 将枚举类型转换为List
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public static List<DbEntityResponse> TransProperties<T>(this T model) where T : class
        {
            Type type = typeof(T);
            var result = new List<DbEntityResponse>();

            foreach (var prop in type.GetProperties())
            {
                result.Add(new DbEntityResponse()
                {
                    ColName = prop.Name,
                    ColValue = prop.GetValue(model, null) == null ? "" : prop.GetValue(model, null).ToString()
                });
            }
            return result;
        }

    }


    public class EnumResponse
    {
        /// <summary>
        /// Key
        /// </summary>
        public string Key { get; set; }

        /// <summary>
        /// Value
        /// </summary>
        public int Value { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int EnumOrder { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        public int EnumDataType { get; set; }
    }

    public class DbEntityResponse
    {
        public string ColName { get; set; }

        public string ColValue { get; set; }

        /// <summary>
        /// 显示的中文名称
        /// </summary>
        public string DisName { get; set; }
    }
}
