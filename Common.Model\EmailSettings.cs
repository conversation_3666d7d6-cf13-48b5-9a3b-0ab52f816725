﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Model
{
    /// <summary>
    /// 邮箱设置参数
    /// </summary>
    public class EmailSettings
    {
        /// <summary>
        /// 名称
        /// </summary>
        public string StepName { get; set; }
        /// <summary>
        /// Imap名称
        /// </summary>
        public string ImapStepName { get; set; }
        /// <summary>
        /// 端口
        /// </summary>
        public string StepPort { get; set; }
        /// <summary>
        /// IMAP端口
        /// </summary>
        public string ImapPort { get; set; }
        /// <summary>
        /// 发件人邮箱
        /// </summary>
        public string UserEmail { get; set; }
        /// <summary>
        /// 发件人密码
        /// </summary>
        public string Password { get; set; }
        /// <summary>
        /// 邮箱昵称
        /// </summary>
        public string EmailBlonger { get; set; }
    }
}
