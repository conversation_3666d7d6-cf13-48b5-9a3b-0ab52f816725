﻿using Common.DAL.Methods;
using Hub.Model;
using Hub.Service;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Hub.Web.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
#if RELEASE
    [Authorize]
#endif
    public class ClientController : ControllerBase
    {
        private readonly ClientLogService _logService;
        private readonly OTAService _otaService;
        public ClientController(ClientLogService logService, OTAService otaService)
        {
            _logService = logService;
            _otaService = otaService;
        }

        [HttpGet]
        //[AllowAnonymous]
        [Route("GetAppDownloadUrl")]
        public BaseRes GetAppDownloadUrl([FromQuery] string filename = "")
        {
            var url = _otaService.GetCDNDownloadUrl(filename);
            if (!string.IsNullOrEmpty(url))
            {
                return HttpResult.Success(url);
            }
            else
            {
                return HttpResult.Fail("App not found");
            }
        }

        //[AllowAnonymous]
        [HttpPost]
        [Route("UploadLog")]
        [RequestFormLimits(MultipartBodyLengthLimit = 50 * 1024 * 1024)]
        public async Task<BaseRes> UploadLog([FromForm] ClientLogDto req)
        {
            await _logService.Upload(req);
            return HttpResult.Success(); 
        }
    }
}
