﻿using System;
using System.Linq;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Primitives;
using static Microsoft.AspNetCore.Hosting.Internal.HostingApplication;


namespace Common.DAL.Methods
{
    public static class HttpContextHelper
    {
        private static IServiceProvider _serviceProvider;
        public static void InitHttpContext(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public static Microsoft.AspNetCore.Http.HttpContext Current
        {
            get
            {
                object factory = _serviceProvider.GetService(typeof(Microsoft.AspNetCore.Http.IHttpContextAccessor));
                return (factory == null ? null : ((Microsoft.AspNetCore.Http.HttpContextAccessor)factory).HttpContext);
            }
        }

        public static string CurrentUserName
        {
            get
            {
                //return Current != null ? Current.User.Identity.Name : "sys";
                return CurrentUserEmail;
            }
        }

        public static string CurrentUserEmail
        {
            get
            {
                string result = "";

                if (Current == null)
                {
                    result = "sys";
                }
                else
                {
                    var email = Current.User.Claims.FirstOrDefault(i => i.Type == "Email");
                    if (email != null)
                    {
                        result = email.Value;
                    }
                    else
                    {
                        result = Current != null ? Current.User.Identity.Name : "sys";
                        if (!String.IsNullOrWhiteSpace(result))
                            if (result.Contains("@"))
                                result = result.Split("@")[0];
                    }
                }
                return result;
            }
        }

        public static string CurrentNickName
        {
            get
            {
                string name = "";
                var user = Current.User.Claims.FirstOrDefault(i => i.Type == "NickName");
                if (user != null)
                {
                    name = user.Value;
                }
                else
                {
                    name = Current != null ? Current.User.Identity.Name : "sys";
                    if (!String.IsNullOrWhiteSpace(name))
                        if (name.Contains("@"))
                            name = name.Split("@")[0];
                }
                return name;
            }
        }
    }
}
