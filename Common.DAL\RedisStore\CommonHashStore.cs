﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.DAL.RedisStore
{
    public static class CommonHashStore
    {
        public static void Cache(string address, int dbIndex, string hashName, string key, string value)
        {
            using (RedisStore rs = new RedisStore(address, dbIndex))
            {
                var db = rs.RedisCache;
                db.HashSet(hashName, key, value);
            }
        }

        public static void CacheList(string address, int dbIndex, string hashName, List<string> keys, List<string> values)
        {
            using (RedisStore rs = new RedisStore(address, dbIndex))
            {
                var db = rs.RedisCache;
                for (int i = 0; i < keys.Count(); i++)
                {
                    db.HashSet(hashName, keys[i], values[i]);
                }
            }
        }

        //public static void Cache(string hashName, string key, string value)
        //{
        //    using (RedisStore rs = new RedisStore())
        //    {
        //        var db = rs.RedisCache;
        //        db.HashSet(hashName, key, value);
        //    }
        //}
        public static bool Exist(string address, int dbIndex, string hashName, string key)
        {
            using (RedisStore rs = new RedisStore(address, dbIndex))
            {
                var db = rs.RedisCache;
                return db.HashExists(hashName, key);
            }
        }
        //public static bool Exist(string hashName, string key)
        //{
        //    using (RedisStore rs = new RedisStore())
        //    {
        //        var db = rs.RedisCache;
        //        return db.HashExists(hashName, key);
        //    }
        //}
        //public static Task<bool> ExistAsync(string hashName, string key)
        //{
        //    using (RedisStore rs = new RedisStore())
        //    {
        //        var db = rs.RedisCache;
        //        return db.HashExistsAsync(hashName, key);
        //    }
        //}

        public static Task<bool> ExistAsync(string address, int dbIndex, string hashName, string key)
        {
            using (RedisStore rs = new RedisStore(address, dbIndex))
            {
                var db = rs.RedisCache;
                return db.HashExistsAsync(hashName, key);
            }
        }
        public static async Task CacheAsync(string address, int dbIndex, string hashName, string key, string value)
        {
            using (RedisStore rs = new RedisStore(address, dbIndex))
            {
                var db = rs.RedisCache;
                await db.HashSetAsync(hashName, key, value);
            }
        }
        //public static async Task CacheAsync(string hashName, string key, string value)
        //{
        //    using (RedisStore rs = new RedisStore())
        //    {
        //        var db = rs.RedisCache;
        //        await db.HashSetAsync(hashName, key, value);
        //    }
        //}
        public static string Get(string address, int dbIndex, string hashName, string key)
        {
            using (RedisStore rs = new RedisStore(address, dbIndex))
            {
                var db = rs.RedisCache;
                if (db.HashExists(hashName, key))
                {
                    return db.HashGet(hashName, key);
                }
                else
                {
                    return null;
                }
            }
        }


        public static List<string> GetAllKeys(string address, int dbIndex, string hashName)
        {
            try
            {
                List<string> list = new List<string>();
                using (RedisStore rs = new RedisStore(address, dbIndex))
                {
                    var db = rs.RedisCache;
                    var allList = db.HashGetAll(hashName);
                    foreach (var item in allList)
                    {
                        list.Add(item.Name);
                    }
                }
                return list;
            }
            catch (Exception)
            {
                return null;
            }
        }

        public static List<Dictionary<string, object>> GetAll(string address, int dbIndex, string hashName)
        {
            try
            {
                List<Dictionary<string, object>> list = new List<Dictionary<string, object>>();
                using (RedisStore rs = new RedisStore(address, dbIndex))
                {
                    var db = rs.RedisCache;
                    var allList = db.HashGetAll(hashName);
                    foreach (var item in allList)
                    {
                        Dictionary<string, object> tempDic = new Dictionary<string, object>();
                        tempDic.Add("key", item.Name);
                        tempDic.Add("value", item.Value);
                        list.Add(tempDic);
                    }
                    return list;
                }
            }
            catch (Exception)
            {
                return null;
            }
        }

        public static List<string> GetAllValues(string address, int dbIndex, string hashName)
        {
            List<string> resList = new List<string>();
            try
            {
                using (RedisStore rs = new RedisStore(address, dbIndex))
                {
                    var db = rs.RedisCache;

                    //var values = db.HashValues(hashName);
                    // return values.Select(x=>x.ToString()).ToList();


                    //var batch = db.CreateBatch(hashName);
                    //var values = batch.HashValuesAsync(hashName);
                    //batch.Execute();
                    //var tempList = values.Result.ToList();


                    var allList = db.HashGetAll(hashName);
                    foreach (var item in allList)
                    {
                        resList.Add(item.Value.ToString());
                    }
                    return resList;
                }
            }
            catch (Exception)
            {
                return null;
            }
        }

        //public static string Get(string hashName, string key)
        //{
        //    using (RedisStore rs = new RedisStore())
        //    {
        //        var db = rs.RedisCache;
        //        if (db.HashExists(hashName, key))
        //        {
        //            return db.HashGet(hashName, key);
        //        }
        //        else
        //        {
        //            return null;
        //        }
        //    }
        //}
        public static async Task<string> GetAsync(string address, int dbIndex, string hashName, string key)
        {
            using (RedisStore rs = new RedisStore(address, dbIndex))
            {
                var db = rs.RedisCache;
                if (await db.HashExistsAsync(hashName, key))
                {
                    return await db.HashGetAsync(hashName, key);
                }
                else
                {
                    return null;
                }
            }
        }
        //public static async Task<string> GetAsync(string hashName, string key)
        //{
        //    using (RedisStore rs = new RedisStore())
        //    {
        //        var db = rs.RedisCache;
        //        if (await db.HashExistsAsync(hashName, key))
        //        {
        //            return await db.HashGetAsync(hashName, key);
        //        }
        //        else
        //        {
        //            return null;
        //        }
        //    }
        //}

        public static bool Remove(string address, int dbIndex, string hashName, string key)
        {
            using (RedisStore rs = new RedisStore(address, dbIndex))
            {
                var db = rs.RedisCache;
                if (db.HashExists(hashName, key))
                {
                    return db.HashDelete(hashName, key);
                }
                else
                {
                    return true;
                }
            }
        }

        public static bool RemoveAll(string address, int dbIndex, string hashName)
        {
            using (RedisStore rs = new RedisStore(address, dbIndex))
            {
                var db = rs.RedisCache;
                var allList = db.HashGetAll(hashName);
                if (allList != null && allList.Count() > 0)
                {
                    foreach (var item in allList)
                    {
                        db.HashDelete(hashName, item.Name);
                    }
                    return true;
                }
                else
                {
                    return true;
                }
            }
        }

        public static bool Remove(string address, int dbIndex, string hashName, List<string> keys)
        {
            try
            {
                bool isDelete = false;
                using (RedisStore rs = new RedisStore(address, dbIndex))
                {
                    var db = rs.RedisCache;
                    foreach (string key in keys)
                    {
                        if (db.HashExists(hashName, key))
                        {
                            isDelete = db.HashDelete(hashName, key);
                        }
                    }
                }
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        //public static bool Remove(string hashName, string key)
        //{
        //    using (RedisStore rs = new RedisStore())
        //    {
        //        var db = rs.RedisCache;
        //        if (db.HashExists(hashName, key))
        //        {
        //            return db.HashDelete(hashName, key);
        //        }
        //        else
        //        {
        //            return true;
        //        }
        //    }
        //}

        //public static async Task<bool> RemoveAsync(string hashName, string key)
        //{
        //    using (RedisStore rs = new RedisStore())
        //    {
        //        var db = rs.RedisCache;
        //        if (db.HashExists(hashName, key))
        //        {
        //            return await db.HashDeleteAsync(hashName, key);
        //        }
        //        else
        //        {
        //            return true;
        //        }
        //    }
        //}
        public static async Task<bool> RemoveAsync(string address, int dbIndex, string hashName, string key)
        {
            using (RedisStore rs = new RedisStore(address, dbIndex))
            {
                var db = rs.RedisCache;
                if (db.HashExists(hashName, key))
                {
                    return await db.HashDeleteAsync(hashName, key);
                }
                else
                {
                    return true;
                }
            }
        }

        /// <summary>
        /// 批量插入
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="address"></param>
        /// <param name="dataList"></param>
        /// <returns></returns>
        public static bool BatchInsert<T>(string address, int dbIndex, string hashName, List<T> dataList) where T : class
        {
            try
            {
                using (RedisStore rs = new RedisStore(address, dbIndex))
                {
                    var db = rs.RedisCache;
                    var batch = db.CreateBatch();
                    foreach (var item in dataList)
                    {
                        batch.StringAppendAsync(hashName, JsonConvert.SerializeObject(item));
                    }
                    batch.Execute();
                }
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
}
