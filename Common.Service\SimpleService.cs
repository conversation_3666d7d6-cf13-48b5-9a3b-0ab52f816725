﻿using AutoMapper;
using Common.DAL;
using Common.DAL.Methods;
using Common.Repository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Common.Service
{
    public class SimpleService : IServiceBase
    {
        public List<To> Transfer<From, To>(List<From> entities)
        {
            return AutoMapperConfig.Mapper.Map<List<To>>(entities);
        }

        public To Transfer<From, To>(From entity)
        {
            return AutoMapperConfig.Mapper.Map<To>(entity);
        }

        public X Clone<X>(X entity, bool newID) where X : EntityBase, new()
        {
            X clone = new X();
            AutoMapperConfig.Mapper.Map(entity, clone);
            if (newID)
                clone.ID = Guid.NewGuid().ToString("N");
            clone.CreatedUser = clone.ModifiedUser = EntityBaseHelper.GetCurrentUser();
            clone.CreatedDateTime = clone.ModifiedDateTime = DateTime.UtcNow;
            return clone;
        }

        public List<X> Clone<X>(List<X> entities, bool newID) where X : EntityBase, new()
        {
            var list = new List<X>();
            var currentUser = EntityBaseHelper.GetCurrentUser();
            var dt = DateTime.UtcNow;
            entities.ForEach(entity =>
            {
                X clone = new X();
                AutoMapperConfig.Mapper.Map(entity, clone);
                if (newID)
                    clone.ID = Guid.NewGuid().ToString("N");
                clone.CreatedUser = clone.ModifiedUser = currentUser;
                clone.CreatedDateTime = clone.ModifiedDateTime = dt;
                list.Add(clone);
            });

            return list;
        }
    }


    public class SimpleService<S, T> : SimpleService, IServiceBase<S, T> where S : class, IPrimaryKeyEntity, new() where T : EntityBase, new()
    {
        protected IRepository<T> _repository;
        public SimpleService(IRepository<T> repository)
        {
            _repository = repository;
        }

        public virtual S Create()
        {
            var entity = _repository.Create();
            return AutoMapperConfig.Mapper.Map<S>(entity);
        }

        public virtual void Delete(string id, bool indeed = false)
        {
            _repository.Delete(id, indeed);
        }

        public virtual void DeleteAll()
        {
            _repository.DeleteAll();
        }

        public virtual void Delete(T entity, bool indeed = false)
        {
            _repository.Delete(entity, indeed);
        }

        public virtual void Delete(List<T> entities, bool indeed = false)
        {
            _repository.Delete(entities, indeed);
        }

        public virtual void Dispose()
        {
            _repository.Dispose();
        }

        public S Find(string id)
        {
            var entity = _repository.Find(id);
            return TransferToDto(entity);
        }


        public virtual List<S> FindAll(Expression<Func<T, bool>> predicate)
        {
            return TransferToDto(_repository.Query(predicate).ToList());
        }

        public S Find(Expression<Func<T,bool>> predicate)
        {
            var entity = _repository.Query(predicate).FirstOrDefault();
            return TransferToDto(entity);
        }

        public bool Exists(Expression<Func<T, bool>> predicate)
        {
            return _repository.Query(predicate).Count() > 0;
        }

        public virtual List<S> FindAll()
        {
            var list = _repository.FindAll();
            return TransferToDto(list);
        }
        public virtual List<S> GetPageData(PageCriteria criteriaBase, out int totalCount)
        {
            var list = _repository.GetPageData(criteriaBase, out totalCount);
            return TransferToDto(list);
        }

        public virtual List<S> GetPageData(Expression<Func<T, bool>> filter, PageCriteria criteriaBase, out int totalCount)
        {
            var list = _repository.GetPageData(filter, criteriaBase, out totalCount);
            return TransferToDto(list);
        }

        public virtual List<S> GetPageData(IQueryable<T> query, PageCriteria criteriaBase, out int totalCount)
        {
            var list = _repository.GetPageData(query, criteriaBase, out totalCount);
            return TransferToDto(list);
        }

        public virtual S Insert(S entity)
        {
            var target = AutoMapperConfig.Mapper.Map<T>(entity);
            return Insert(target);
        }

        public virtual IList<S> Insert(IList<S> entities)
        {
            var targets = AutoMapperConfig.Mapper.Map<IList<T>>(entities);
            return Insert(targets);
        }

        public virtual List<S> Insert(IList<T> entities)
        {
            var result = _repository.Insert(entities);
            return TransferToDto(result);
        }

        public virtual S Insert(T entity)
        {
            var result = _repository.Insert(entity);
            return TransferToDto(result);
        }

        public S TransferToDto(T entity)
        {
            return AutoMapperConfig.Mapper.Map<S>(entity);
        }

        public List<S> TransferToDto(IList<T> entities)
        {
            return AutoMapperConfig.Mapper.Map<List<S>>(entities);
        }

        public virtual S Update(S entity)
        {
            var obj = _repository.Find(entity.ID);
            if (obj != null)
            {
                AutoMapperConfig.Mapper.Map(entity, obj);
                return Update(obj);
            }
            return null;
        }

        public virtual S Update(T entity)
        {
            var result = _repository.Update(entity);
            return TransferToDto(result);
        }
    }
}
