﻿using Common.DAL;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hub.DAL.Table
{
    /// <summary>
    /// 小程序
    /// </summary>
    public class App : EntityBase
    {
        /// <summary>
        /// App编码
        /// </summary>
        [MaxLength(32)]
        public string AppCode { get; set; }
        /// <summary>
        /// 显示名称
        /// </summary>
        [MaxLength(32)]
        public string DisplayName { get; set; }
        /// <summary>
        /// 图标数据,Base64格式的图片
        /// </summary>
        [Column(TypeName = "text")]
        public string IconData { get; set; }
        /// <summary>
        /// APP模式
        /// </summary>
        public HubAppMode AppMode { get; set; }
        /// <summary>
        /// 授权方式
        /// </summary>
        public HubAppAuthType AuthType { get; set; }
        /// <summary>
        /// Hybrid模式下为下载地址，Online模式下为在线跳转的首页地址
        /// </summary>
        [MaxLength(128)]
        public string URL { get; set; }
        /// <summary>
        /// Hybrid模式下默认打开的首页地址，Online模式下与URL内容一致
        /// </summary>
        [MaxLength(128)]
        public string HomeAddress { get; set; }
        public int Order { get; set; }
        /// <summary>
        /// 如果是非HybridApp，则版本号一直为0，因为只需要判断URL是否一致就可以了，不需要考虑是否需要下载替换
        /// </summary>
        public int HybridAppVersion { get; set; }
    }

    /// <summary>
    /// Online在线APP，直接通过URL跳转
    /// Hybrid混合本地APP，需要下载到本地解压运行
    /// </summary>
    public enum HubAppMode
    {
        Online,
        Hybrid
    }

    /// <summary>
    /// None,不需要授权即可使用
    /// Integrated集成认证，统一登录，Hub框架会传递token到App，App通过token拿取当前用户信息
    /// Independent独立认证，由App自己实现登录功能
    /// </summary>
    public enum HubAppAuthType
    {
        Integrated,
        Independent
    }
}
