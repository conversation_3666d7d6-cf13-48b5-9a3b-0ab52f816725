using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using Hub.DAL;
using IdentityServer4.AccessTokenValidation;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using System.IO;
using Hub.Env;
using Microsoft.Extensions.FileProviders;
using Hub.Service;
using Hub.Repository;
using Hub.Web;
using Common.DAL.Methods;
using Hub.Model;
using Common.Service;
using AutoMapper;
using Common.Utility;

namespace Web.Hub
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddDbContext<HubDbContext>(options =>
                options.UseMySql(
                    Configuration.GetConnectionString("DefaultConnection"),
                    ServerVersion.AutoDetect(Configuration.GetConnectionString("DefaultConnection")
                ))
            );

            services.RegisterRepositories();
            services.RegisterServices(); 

            ConfigEnvValues.Init(Configuration);
            AutoMapperConfig.Configure(new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<AutoMapperProfile>();
            }));

#if RELEASE
            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = IdentityServerAuthenticationDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            }).AddIdentityServerAuthentication(options =>
            {
                options.Authority = Configuration["IdentityServerBaseUrl"].Trim();
                options.ApiName = Configuration["IdentityServerApiName"];
                options.RequireHttpsMetadata = false;
            });
#endif
            services.AddControllers();
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "Hub.Web", Version = "v1" });

                var path = Path.Combine(AppContext.BaseDirectory, "Hub.Web.xml");
                c.IncludeXmlComments(path, true);
                c.OrderActionsBy(x => x.RelativePath);
            });
            services.AddRouting(opts => opts.LowercaseUrls = true);
            services.AddSpaStaticFiles(config =>
            {
                config.RootPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, Consts.SPA_Root);
            });
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                app.UseSwagger();
                app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "Hub.Web v1"));
            }

            NLog.LogManager.LoadConfiguration("nlog.config");

            HttpContextHelper.InitHttpContext(app.ApplicationServices);

            app.UseRouting();

            app.UseAuthentication();

            app.UseAuthorization();

            app.UseMiddleware<GlobalExceptionHandlerMiddleware>();

            app.UseStaticFiles();

            CdnAddressHelper.SetCDNRootUrl(ConfigEnvValues.CDNRootUrl, ConfigEnvValues.CDNProjectName, ConfigEnvValues.DomainURL);

            if (!Directory.Exists(ConfigEnvValues.OTAConfigFolderFullPath))
            {
                Directory.CreateDirectory(ConfigEnvValues.OTAConfigFolderFullPath);
            }
            app.UseStaticFiles(new StaticFileOptions
            {
                FileProvider = new PhysicalFileProvider(ConfigEnvValues.OTAConfigFolderFullPath),
                RequestPath = "/ota"
            });

            app.UseCors(builder => builder.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader());

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }
    }
}
