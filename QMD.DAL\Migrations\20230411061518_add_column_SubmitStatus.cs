﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_column_SubmitStatus : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "BigDataId",
                table: "TaskItems",
                type: "varchar(512)",
                maxLength: 512,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(128)",
                oldMaxLength: 128,
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<int>(
                name: "SubmitStatus",
                table: "TaskItemAndTplRels",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "FirstSolution",
                table: "ItrWorkOrders",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<double>(
                name: "LeftTime",
                table: "ItrWorkOrders",
                type: "double",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<string>(
                name: "ProductLine",
                table: "ItrWorkOrders",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "SecondSolution",
                table: "ItrWorkOrders",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<int>(
                name: "SubmitStatus",
                table: "CycleTaskRecords",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "SubmitStatus",
                table: "TaskItemAndTplRels");

            migrationBuilder.DropColumn(
                name: "FirstSolution",
                table: "ItrWorkOrders");

            migrationBuilder.DropColumn(
                name: "LeftTime",
                table: "ItrWorkOrders");

            migrationBuilder.DropColumn(
                name: "ProductLine",
                table: "ItrWorkOrders");

            migrationBuilder.DropColumn(
                name: "SecondSolution",
                table: "ItrWorkOrders");

            migrationBuilder.DropColumn(
                name: "SubmitStatus",
                table: "CycleTaskRecords");

            migrationBuilder.AlterColumn<string>(
                name: "BigDataId",
                table: "TaskItems",
                type: "varchar(128)",
                maxLength: 128,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(512)",
                oldMaxLength: 512,
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");
        }
    }
}
