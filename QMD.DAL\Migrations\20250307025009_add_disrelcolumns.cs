﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace QMD.DAL.Migrations
{
    public partial class add_disrelcolumns : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "OpticalModule",
                table: "emt_involvedevice",
                type: "varchar(255)",
                maxLength: 255,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "InvDeviceId",
                table: "emt_dispatchprocess",
                type: "varchar(32)",
                maxLength: 32,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<DateTime>(
                name: "PlanEndTime",
                table: "emt_dispatchprocess",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "PlanStartTime",
                table: "emt_dispatchprocess",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<int>(
                name: "ProcessType",
                table: "emt_dispatchprocess",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "OpticalModule",
                table: "emt_involvedevice");

            migrationBuilder.DropColumn(
                name: "InvDeviceId",
                table: "emt_dispatchprocess");

            migrationBuilder.DropColumn(
                name: "PlanEndTime",
                table: "emt_dispatchprocess");

            migrationBuilder.DropColumn(
                name: "PlanStartTime",
                table: "emt_dispatchprocess");

            migrationBuilder.DropColumn(
                name: "ProcessType",
                table: "emt_dispatchprocess");
        }
    }
}
