var hub_mobile_drive = {
  version:"1.0",
  plugins: {},
  callBackCaches: {},
  callBackIdCaches: {},
  clearCaches: function (callBackId) {
    var relationId = this.callBackIdCaches[callBackId];
    delete this.callBackCaches[relationId];
    delete this.callBackCaches[callBackId];
    delete this.callBackIdCaches[relationId];
    delete this.callBackIdCaches[callBackId];
  },
  addPlugin: function (id, plugin) {
    if (Object.prototype.hasOwnProperty.call(this.plugins, id)) {
      console.error("plugin " + id + " already exist");
      return;
    }
    this.plugins[id] = plugin;
  },
  injectJS: function (files) {
    for (var i = 0; i < files.length; i++) {
      this.injectJSByPath(files[i].path);
    }
  },
  injectJSByPath: function (path) {
    var script = document.createElement("script");
    script.onerror = function () {
      console.error(path + " injectFailed");
    };
    script.src = path+"?ver="+this.version;
    document.head.appendChild(script);
  },
  s4: function () {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  },
  guid: function () {
    return (
      this.s4() +
      this.s4() +
      "-" +
      this.s4() +
      "-" +
      this.s4() +
      "-" +
      this.s4() +
      "-" +
      this.s4() +
      this.s4() +
      this.s4()
    );
  },
  exec: function (successCallback, errorCallback, service, action, args) {
    try {
      if (typeof errorCallback != "function") {
        console.error("parameter [errorCallback] should be function");
        return;
      }
      if (typeof successCallback != "function") {
        console.error("parameter [successCallback] should be function");
        return;
      }
      if (typeof service != "string") {
        console.error("parameter [service] should be string");
        errorCallback("parameter [service] should be string");
        return;
      }
      if (typeof action != "string") {
        console.error("parameter [action] should be string");
        errorCallback("parameter [action] should be string");
        return;
      }
      var strArgs = "";
      if (args) {
        if (typeof args != "string") {
          strArgs = JSON.stringify(args);
        } else {
          strArgs = args;
        }
      }
      var successCallBackId = this.guid();
      var errorCallBackId = this.guid();
      this.callBackCaches[successCallBackId] = successCallback;
      this.callBackCaches[errorCallBackId] = errorCallback;
      this.callBackIdCaches[successCallBackId] = errorCallBackId;
      this.callBackIdCaches[errorCallBackId] = successCallBackId;
      mobileExec(successCallBackId, errorCallBackId, service, action, strArgs);
    } catch (exception) {
      console.error(exception);
      errorCallback(exception.message);
    }
  },
  callBack: function (callBackId, result) {
    try {
      this.callBackCaches[callBackId](result);
    } catch (err) {
      console.error(err);
    } finally {
      this.clearCaches(callBackId);
    }
  },
  customBack: undefined,
  registerCustomBack: function (moblieBackCallback) {
    this.customBack = moblieBackCallback;
  },
  pressBackCallBack: function () {
    try {
      if (this.customBack) {
        if (typeof this.customBack != "function") {
          console.error("customBack should be function");
        } else {
          this.customBack();
        }
      } else {
        mobileBack();
      }
    } catch (err) {
      console.error(err);
    }
  },
  closeCurrentPage: function () {
    mobileBack();
  },
};
hub_mobile_drive.injectJSByPath("hub_mobile_plugins.js");
