﻿using AutoMapper;
using Hub.DAL.Table;
using Hub.Env;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hub.Model
{
    public class AutoMapperProfile : Profile
    {
        public AutoMapperProfile()
        {
            CreateMap<AppDto, App>().ForMember(dest => dest.IsActived, opt => opt.Ignore()).ReverseMap();
        }
    }
}
