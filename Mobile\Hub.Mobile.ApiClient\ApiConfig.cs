﻿using Hub.Mobile.Const;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using WebApiClient;

namespace Hub.Mobile.ApiClient
{
    public static class ApiConfig
    {
        private static bool inited = false;
        public static void Init()
        {
            if (!inited)
            {
                inited = true;
                HttpApi.Register<ILoginApiClient>().ConfigureHttpApiConfig(c =>
                {
                    c.HttpHost = new Uri(Path.Combine(ApiAddresses.LoginApiBaseUrl, "api/"));
                    c.FormatOptions.DateTimeFormat = DateTimeFormats.ISO8601_WithMillisecond;
                    c.HttpClient.Timeout = new TimeSpan(0, 30, 0);
                });
                HttpApi.Register<IHubApiClient>().ConfigureHttpApiConfig(c =>
                {
                    c.HttpHost = new Uri(Path.Combine(ApiAddresses.HubBaseUrl, "api/"));
                    c.FormatOptions.DateTimeFormat = DateTimeFormats.ISO8601_WithMillisecond;
                    c.GlobalFilters.Add(ApiTokenFilter.GetInstance());
                    c.HttpClient.Timeout = new TimeSpan(0, 30, 0);
                });
            }
        }
    }
}
