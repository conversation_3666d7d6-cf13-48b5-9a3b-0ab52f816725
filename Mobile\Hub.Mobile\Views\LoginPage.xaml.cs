﻿using dotMorten.Xamarin.Forms;
using Hub.Mobile.ViewModels;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace Hub.Mobile.Views
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class LoginPage : PopupPage
    {
        private Action ClosedCallBack;
        LoginViewModel viewModel = null;
        public LoginPage(Action closedCallBack = null)
        {
            InitializeComponent();
            this.ClosedCallBack = closedCallBack;
            viewModel = new LoginViewModel() { Navigation = this.Navigation };
            this.BindingContext = viewModel;
        }
        private void AutoSuggestBox_TextChanged(object sender, dotMorten.Xamarin.Forms.AutoSuggestBoxTextChangedEventArgs e)
        {
            AutoSuggestBox box = (AutoSuggestBox)sender;
            var text = box.Text;
            viewModel.Email = text?.Trim();
            box.ItemsSource = GetSuggestBoxItemsSource(text);
        }
        private List<string> GetSuggestBoxItemsSource(string filter = "")
        {
            return viewModel.EmailDataCache?.OrderByDescending(p => p.ModifiedTime).Select(p => p.Value).Distinct().Where(p => string.IsNullOrWhiteSpace(filter) || p.IndexOf(filter) >= 0).ToList();
        }

        private void AutoSuggestBox_QuerySubmitted(object sender, dotMorten.Xamarin.Forms.AutoSuggestBoxQuerySubmittedEventArgs e)
        {

        }

        private void AutoSuggestBox_SuggestionChosen(object sender, dotMorten.Xamarin.Forms.AutoSuggestBoxSuggestionChosenEventArgs e)
        {

        }

        private void EmailSuggestBox_Focused(object sender, FocusEventArgs e)
        {
            AutoSuggestBox box = (AutoSuggestBox)sender;
            if (box.ItemsSource == null)
            {
                box.ItemsSource = GetSuggestBoxItemsSource();
            }
        }
        protected override void OnDisappearing()
        {
            base.OnDisappearing();

            if (ClosedCallBack != null)
            {
                ClosedCallBack();
            }

        }
    }
}