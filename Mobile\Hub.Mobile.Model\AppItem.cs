﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Hub.Mobile.Model
{
    public class AppItem
    {
        public string AppCode { get; set; }
        public string DisplayName { get; set; }
        public string IconData { get; set; }
        public HubAppMode AppMode { get; set; }
        public HubAppAuthType AuthType { get; set; }
        public string URL { get; set; }
        public string HomeAddress { get; set; }
        public int Order { get; set; }
        public int HybridAppVersion { get; set; }
    }
    public enum HubAppMode
    {
        Online,
        Hybrid
    }
    public enum HubAppAuthType
    {
        Integrated,
        Independent
    }
}
